# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * barcodes_generator_product
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-07-07 15:18+0000\n"
"PO-Revision-Date: 2017-07-07 15:18+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Netherlands) (https://www.transifex.com/oca/"
"teams/23907/nl_NL/)\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: barcodes_generator_product
#: model:ir.model.fields,help:barcodes_generator_product.field_product_product__generate_type
#: model:ir.model.fields,help:barcodes_generator_product.field_product_template__generate_type
msgid ""
"Allow to generate barcode, including a number  (a base) in the final "
"barcode.\n"
"\n"
" - 'Base Set Manually' : User should set manually the value of the barcode "
"base\n"
" - 'Base managed by Sequence': System will generate the base via a sequence"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_product__barcode_base
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_template__barcode_base
msgid "Barcode Base"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model,name:barcodes_generator_product.model_barcode_rule
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_product__barcode_rule_id
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_template__barcode_rule_id
msgid "Barcode Rule"
msgstr ""

#. module: barcodes_generator_product
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.product_variant_easy_edit_view
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_product_form
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_template_form
msgid "Generate Barcode"
msgstr ""

#. module: barcodes_generator_product
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.product_variant_easy_edit_view
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_product_form
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_template_form
msgid "Generate Barcode (Using Barcode Rule)"
msgstr ""

#. module: barcodes_generator_product
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.product_variant_easy_edit_view
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_product_form
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_template_form
msgid "Generate Base"
msgstr ""

#. module: barcodes_generator_product
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.product_variant_easy_edit_view
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_product_form
#: model_terms:ir.ui.view,arch_db:barcodes_generator_product.view_product_template_form
msgid "Generate Base (Using Sequence)"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,field_description:barcodes_generator_product.field_barcode_rule__generate_model
msgid "Generate Model"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_product__generate_type
#: model:ir.model.fields,field_description:barcodes_generator_product.field_product_template__generate_type
msgid "Generate Type"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,help:barcodes_generator_product.field_barcode_rule__generate_model
msgid "If 'Generate Type' is set, mention the model related to this rule."
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model,name:barcodes_generator_product.model_product_template
msgid "Product"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model,name:barcodes_generator_product.model_product_product
msgid "Product Variant"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields.selection,name:barcodes_generator_product.selection__barcode_rule__generate_model__product_product
msgid "Products"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,help:barcodes_generator_product.field_product_product__barcode_rule_id
msgid "Select a rule to generate a barcode"
msgstr ""

#. module: barcodes_generator_product
#: model:product.template,name:barcodes_generator_product.product_template_mono_variant
msgid "Template with Generated Barcode (Mono Variant)"
msgstr ""

#. module: barcodes_generator_product
#: model:product.template,name:barcodes_generator_product.product_template_multi_variant
msgid "Template with Generated Barcode (Multi Variant)"
msgstr ""

#. module: barcodes_generator_product
#: model:ir.model.fields,help:barcodes_generator_product.field_product_product__barcode_base
msgid ""
"This value is used to generate barcode according to the setting of the "
"barcode rule."
msgstr ""
