<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sh_res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.account</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@id = 'analytic']" position="after">
                <block title="Default Journal and Users" name="default_journal_and_users">
                    <setting>
                        <field name="journal_ids" widget="many2many_tags" />
                    </setting>
                    <setting>
                        <field name="sh_user_ids" widget="many2many_tags" />
                    </setting>
                </block>
            </xpath>
        </field>
    </record>
</odoo>