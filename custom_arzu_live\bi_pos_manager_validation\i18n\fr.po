# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* bi_pos_manager_validation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-09 10:01+0000\n"
"PO-Revision-Date: 2023-05-09 10:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,help:bi_pos_manager_validation.field_res_users__pos_security_pin
msgid ""
"A Security PIN used to protect sensible functionality in the Point of Sale"
msgstr ""
"Un code PIN de sécurité utilisé pour protéger les fonctionnalités sensibles du point de vente"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__qty_detail
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_qty_detail
msgid "Add/Remove Quantity"
msgstr "Ajouter/supprimer une quantité"

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate closing pos."
msgstr "Autoriser le responsable à valider la position de clôture."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid ""
"Allow manager to validate if Add or remove quantity is valid on order lines."
msgstr ""
"Autoriser le gestionnaire à valider si Ajouter ou supprimer une quantité est valide sur les lignes de commande."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate if discount is applicable to orderline."
msgstr "Autoriser le gestionnaire à valider si la remise est applicable à la ligne de commande."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate if order for payment."
msgstr "Autoriser le responsable à valider si commande pour paiement."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate if price change is need to be order line."
msgstr "Autoriser le gestionnaire à valider si le changement de prix doit être effectué sur la ligne de commande."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate order lines need to be delete."
msgstr "Autoriser le gestionnaire à valider les lignes de commande doivent être supprimées."

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Allow manager to validate order need to be delete."
msgstr "Autoriser le gestionnaire à valider la commande doit être supprimé."

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__discount_app
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_discount_app
msgid "Apply Discount"
msgstr "Appliquer la réduction"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__close_pos
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_close_pos
msgid "Closing Of POS"
msgstr "Fermeture du point de vente"

#. module: bi_pos_manager_validation
#: model:ir.model,name:bi_pos_manager_validation.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "If user want to added password only once for every functionality."
msgstr "Si l'utilisateur souhaite ajouter un mot de passe une seule fois pour chaque fonctionnalité."

#. module: bi_pos_manager_validation
#. odoo-javascript
#: code:addons/bi_pos_manager_validation/static/src/js/models.js:0
#, python-format
msgid "Invalid Password"
msgstr "Mot de passe incorrect"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__user_id
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_user_id
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Manager"
msgstr "Directrice"

#. module: bi_pos_manager_validation
#. odoo-javascript
#: code:addons/bi_pos_manager_validation/static/src/js/models.js:0
#, python-format
msgid "Manager Password"
msgstr "Mot de passe du gestionnaire"

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Manager Validation"
msgstr "Validation du gestionnaire"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__one_time_valid
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_one_time_valid
msgid "One Time Password for an Order"
msgstr "Mot de passe à usage unique pour une commande"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__order_delete
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_order_delete
msgid "Order Deletion"
msgstr "Suppression de commande"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__order_line_delete
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_order_line_delete
msgid "Order Line Deletion"
msgstr "Suppression de ligne de commande"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__payment_perm
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_payment_perm
msgid "Payment"
msgstr "Paiement"

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_users_view_form
msgid "Point of Sale"
msgstr "Point de vente"

#. module: bi_pos_manager_validation
#: model:ir.model,name:bi_pos_manager_validation.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuration du point de vente"

#. module: bi_pos_manager_validation
#: model:ir.model,name:bi_pos_manager_validation.model_pos_session
msgid "Point of Sale Session"
msgstr "Séance en point de vente"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_pos_config__price_change
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_config_settings__pos_price_change
msgid "Price Change"
msgstr "Changement de prix"

#. module: bi_pos_manager_validation
#: model:ir.model.fields,field_description:bi_pos_manager_validation.field_res_users__pos_security_pin
msgid "Security PIN"
msgstr "Code de securité"

#. module: bi_pos_manager_validation
#. odoo-python
#: code:addons/bi_pos_manager_validation/models/pos_config.py:0
#, python-format
msgid "Security PIN can only contain digits"
msgstr "Le code PIN de sécurité ne peut contenir que des chiffres"

#. module: bi_pos_manager_validation
#: model_terms:ir.ui.view,arch_db:bi_pos_manager_validation.res_config_settings_view_form
msgid "Set up managers for this point of sale."
msgstr "Configurez des gestionnaires pour ce point de vente."

#. module: bi_pos_manager_validation
#: model:ir.model,name:bi_pos_manager_validation.model_res_users
msgid "User"
msgstr "Utilisatrice"

#. module: bi_pos_manager_validation
#. odoo-javascript
#: code:addons/bi_pos_manager_validation/static/src/js/models.js:0
#, python-format
msgid "Wrong Password"
msgstr "Mauvais mot de passe"
