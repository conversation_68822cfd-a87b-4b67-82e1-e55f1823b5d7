<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- View for the Product Details wizard form -->
    <record id="transfer_products_details_view_form" model="ir.ui.view">
        <field name="name">transfer.products.details.view.form</field>
        <field name="model">transfer.products.details</field>
        <field name="arch" type="xml">
            <form string="Product Details">
                <group>
                    <group>
                        <field name="product"/>
                    </group>
                    <group>
                        <field name="picking"/>
                    </group>
                </group>
                <group>
                    <group>
                        <field name="qty"/>
                    </group>
                    <group>
                        <field name="date_from"/>
                    </group>
                </group>
                <notebook>
                    <page name="transfer_history"
                          string="Products Transfer History">
                        <field name="transfer_history_ids" readonly="1"
                               style="pointer-events:none;">
                            <tree editable="bottom">
                                <field name="date_picking"/>
                                <field name="picking"/>
                                <field name="partner_id"/>
                                <field name="qty"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <footer>
                    <button name="action_add_to_transfer"
                            string="Add to Transfer"
                            type="object" class="oe_highlight"/>
                    <button string="Cancel" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
