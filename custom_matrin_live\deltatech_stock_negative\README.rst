=================
No Negative Stock
=================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:56b051131c142d261e888f9bc768de1cf5ec771b503fa3cf39d3f0e7455b938b
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-OPL--1-blue.png
    :target: https://www.odoo.com/documentation/master/legal/licenses.html
    :alt: License: OPL-1
.. |badge3| image:: https://img.shields.io/badge/github-dhongu%2Fdeltatech-lightgray.png?logo=github
    :target: https://github.com/dhongu/deltatech/tree/17.0/deltatech_stock_negative
    :alt: dhongu/deltatech

|badge1| |badge2| |badge3|

-  Features:

   -  No negative stock for internal location
   -  Allows negative stock at certain locations

**Table of contents**

.. contents::
   :local:

Usage
=====

-  Inventory -> Configuration -> Settings
-  Traceability -> Negative Stock

|image1|

-  Inventory -> Configuration -> Location

|image2|

.. |image1| image:: https://raw.githubusercontent.com/dhongu/deltatech/17.0/deltatech_stock_negative/static/description/settings.png
.. |image2| image:: https://raw.githubusercontent.com/dhongu/deltatech/17.0/deltatech_stock_negative/static/description/settings_location.png

Bug Tracker
===========

Bugs are tracked on `Terrabit Issues <https://www.terrabit.ro/helpdesk>`_.
In case of trouble, please check there if your issue has already been reported.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Terrabit
* Dorin Hongu

Maintainers
-----------

.. |maintainer-dhongu| image:: https://github.com/dhongu.png?size=40px
    :target: https://github.com/dhongu
    :alt: dhongu

Current maintainer:

|maintainer-dhongu| 

This module is part of the `dhongu/deltatech <https://github.com/dhongu/deltatech/tree/17.0/deltatech_stock_negative>`_ project on GitHub.

You are welcome to contribute.
