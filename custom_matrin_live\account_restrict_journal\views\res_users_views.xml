<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Added fields for accessible journals -->
    <record id="view_users_form" model="ir.ui.view">
        <field name="name">res.users.form.inherit.account.restrict.journal</field>
        <field name="model">res.users</field>
        <field name="mode">extension</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='security']" position="after">
                <page name="account_journal" string="Restricted Journal" invisible="not is_check_user" >
                    <group>
                        <field name="journal_ids" widget="many2many_tags"/>
                        <field name="is_check_user" invisible="1"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
