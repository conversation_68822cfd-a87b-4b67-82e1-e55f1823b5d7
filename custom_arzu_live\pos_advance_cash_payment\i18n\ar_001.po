# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_advance_cash_payment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-05 11:42+0000\n"
"PO-Revision-Date: 2022-01-05 11:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Add Payment"
msgstr "إضافة الدفع"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Amount:"
msgstr "مقدار:"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Apply"
msgstr "يتقدم"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Cancel"
msgstr "يلغي"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__create_uid
msgid "Created by"
msgstr "انشأ من قبل"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__create_date
msgid "Created on"
msgstr "تم إنشاؤها على"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Enter Details Here"
msgstr "أدخل التفاصيل هنا"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__id
msgid "ID"
msgstr "بطاقة تعريف"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment____last_update
msgid "Last Modified on"
msgstr "تاريخ آخر تعديل"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Payment Type:"
msgstr "نوع الدفع:"

#. module: pos_advance_cash_payment
#: model:ir.model,name:pos_advance_cash_payment.model_pos_create_customer_payment
msgid "pos.create.customer.payment"
msgstr ""
