<?xml version="1.0" encoding="UTF-8" ?>
<!--
Copyright (C) 2023-Today: GRAP (http://www.grap.coop)
<AUTHOR> LE GAL (https://twitter.com/legalsylvain)
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<odoo>
    <record id="view_barcode_nomenclature_form" model="ir.ui.view">
        <field name="model">barcode.nomenclature</field>
        <field name="inherit_id" ref="barcodes.view_barcode_nomenclature_form" />
        <field name="arch" type="xml">
            <field name="pattern" position="after">
                <field name="generate_type" invisible="generate_type == 'no'" />
                <field name="generate_model" invisible="generate_type == 'no'" />
            </field>
        </field>
    </record>
</odoo>
