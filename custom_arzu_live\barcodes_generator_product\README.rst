==============================
Generate Barcodes for Products
==============================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:075a6bb2d5a5f99954dbebde93739ea6f1d8cc51b832a8a6d8567ed5fbec3dfe
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fstock--logistics--barcode-lightgray.png?logo=github
    :target: https://github.com/OCA/stock-logistics-barcode/tree/17.0/barcodes_generator_product
    :alt: OCA/stock-logistics-barcode
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/stock-logistics-barcode-17-0/stock-logistics-barcode-17-0-barcodes_generator_product
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/stock-logistics-barcode&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module expands Odoo functionality, allowing user to generate
barcode depending on a given barcode rule for Products.

For example, a typical pattern for products is "20.....{NNNDD}" that
means that:

-  the EAN13 code will begin by '20'
-  followed by 5 digits (named Barcode Base in this module)
-  and after 5 others digits to define the variable price
-  a 13 digit control

Another common pattern is "8012345....." which means that:

-  the EAN13 code will begin with '8012345'
-  followed by 5 digits (named Barcode Base in this module)
-  and finally the 13th digit is the control digit

Note that a dot is not necessary in the pattern as the control digit is
added automatically.

With this module, it is possible to:

-  Assign a pattern (barcode.rule) to a product.product

-  Define a Barcode base:

   -  manually, if the base of the barcode must be set by a user.
      (typically an internal code defined in your company)
   -  automaticaly by a sequence, if you want to let Odoo to increment a
      sequence. (typical case of a customer number incrementation)

-  Generate a barcode, based on the defined pattern and the barcode base

**Table of contents**

.. contents::
   :local:

Configuration
=============

To configure this module, see the 'Configuration' Section of the
description of the module 'barcodes_generator_abstract'

To manage the barcode rule and the barcode base on products you need to
include your user on "Generate Barcodes" group.

Usage
=====

To use this module, you need to:

-  Go to a Product form (or a template form):

1 for manual generation

-  Set a Barcode Rule
-  Set a Barcode Base
-  click on the button 'Generate Barcode (Using Barcode Rule)'

|image|

2 for automatic generation

-  Set a Barcode Rule
-  click on the button 'Generate Base (Using Sequence)'
-  click on the button 'Generate Barcode (Using Barcode Rule)'

.. |image| image:: https://raw.githubusercontent.com/OCA/stock-logistics-barcode/17.0/barcodes_generator_product/static/description/product_template_manual_generation.png

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/stock-logistics-barcode/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/stock-logistics-barcode/issues/new?body=module:%20barcodes_generator_product%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* GRAP
* La Louve

Contributors
------------

-  Sylvain LE GAL (https://twitter.com/legalsylvain)
-  Dave Lasley <<EMAIL>>
-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Carlos Roca

-  `Ooops404 <https://www.ooops404.com>`__:

   -  Ilyas <<EMAIL>>

Other credits
-------------

Images
~~~~~~

-  Icon of the module is based on the Oxygen Team work and is under LGPL
   licence:
   http://www.iconarchive.com/show/oxygen-icons-by-oxygen-icons.org.html
-  Product tag by `Zlatko
   Najdenovski <https://www.iconfinder.com/zlaten>`__ and is licensed
   under `CC BY 3.0 <https://creativecommons.org/licenses/by/3.0/>`__.

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-legalsylvain| image:: https://github.com/legalsylvain.png?size=40px
    :target: https://github.com/legalsylvain
    :alt: legalsylvain

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-legalsylvain| 

This module is part of the `OCA/stock-logistics-barcode <https://github.com/OCA/stock-logistics-barcode/tree/17.0/barcodes_generator_product>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
