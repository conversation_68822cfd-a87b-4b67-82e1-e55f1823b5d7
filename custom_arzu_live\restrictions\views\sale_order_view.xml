<odoo>
    <data>
<record id="inherit_view_order_form_sale_stock" model="ir.ui.view">
        <field name="name">inherit.sale.order.form</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
        <field name="arch" type="xml">
            
              <field name="warehouse_id" position="attributes">
                <attribute name="required">1</attribute>
                <attribute name="readonly">"state in ['sale','done','cancel']"</attribute>
            </field>
            <field name="analytic_account_id" position="replace">
              <field name="analytic_account_id" context="{'default_partner_id':partner_invoice_id, 'default_name':name}" readonly="1" groups="analytic.group_analytic_accounting" force_save="1"/>
            </field>
        </field>
    </record>
    </data>
</odoo>