<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <template id="report_delivery_document" inherit_id="stock.report_delivery_document">
        <!-- Changes for when the picking is not yet done -->
        <xpath expr="//table[@name='stock_move_table']/thead" position="before">
            <t
                t-set="has_line_position"
                t-value="any(o.move_ids.filtered(lambda x: x.position_sale_line and x.product_uom_qty))"
            />
        </xpath>
        <xpath expr="//table[@name='stock_move_table']/thead//th[1]" position="before">
            <th t-if="has_line_position" name="th_sm_pos"><strong>Pos</strong></th>
        </xpath>
        <xpath expr="//table[@name='stock_move_table']/tbody//td[1]" position="before">
            <td t-if="has_line_position" name="td_sm_pos">
                <span t-field="move.position_sale_line" />
            </td>
        </xpath>
        <!-- Changes for when the picking is done -->
        <xpath expr="//table[@name='stock_move_line_table']/thead" position="before">
            <t
                t-set="has_line_position"
                t-value="any(o.move_line_ids.mapped('position_sale_line'))"
            />
        </xpath>
        <xpath
            expr="//table[@name='stock_move_line_table']/thead//th[1]"
            position="before"
        >
            <th t-if="has_line_position and has_serial_number" name="th_sml_pos"><strong
                >Pos</strong></th>
        </xpath>
        <!--
            NOTE: The rows are rendered by other templates.
            `stock_report_delivery_has_serial_move_line` if the lines have lots/serials
            `stock_report_delivery_aggregated_move_lines` if they don't. In this case the lines are
            aggregated, and so we don't display the position.
        -->
    </template>

    <template
        id="stock_report_delivery_has_serial_move_line"
        inherit_id="stock.stock_report_delivery_has_serial_move_line"
    >
        <xpath expr="//td[1]" position="before">
            <td t-if="has_line_position">
                <span t-field="move_line.position_sale_line" />
            </td>
        </xpath>
    </template>

</odoo>
