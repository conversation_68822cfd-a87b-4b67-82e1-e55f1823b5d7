<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <template
        id="valued_report_picking"
        inherit_id="stock.report_delivery_document"
        priority="17"
    >
        <xpath expr="//div[hasclass('page')]" position="before">
            <t t-set="is_outgoing" t-value="o.picking_type_code == 'outgoing'" />
        </xpath>
        <xpath expr="//table[@name='stock_move_line_table']/thead/tr" position="inside">
            <t t-if="o.valued and o.sale_id and o.move_line_ids and is_outgoing">
                <th class="text-end"><strong>Unit Price</strong></th>
                <th class="text-end" groups="product.group_discount_per_so_line">
                    <strong>Discount</strong>
                </th>
                <th class="text-end"><strong>Subtotal</strong></th>
                <th class="text-end"><strong>Taxes</strong></th>
            </t>
        </xpath>
        <xpath expr="//th[@name='th_sml_qty_ordered']" position="attributes">
            <attribute
                name="t-if"
                add="and (not is_outgoing or not (o.valued and o.sale_id and o.move_line_ids))"
                separator=" "
            />
        </xpath>
        <xpath expr="//th[@name='th_sml_qty_ordered']" position="after">
            <t t-elif="not has_serial_number" />
        </xpath>
        <xpath
            expr="//t[@t-foreach='packages']//t[@t-if='has_serial_number']"
            position="attributes"
        >
            <attribute
                name="t-if"
                add="or (o.valued and o.sale_id and o.move_line_ids and is_outgoing)"
                separator=" "
            />
        </xpath>
        <xpath
            expr="//t[@name='no_package_move_lines']//t[@t-if='has_serial_number']"
            position="attributes"
        >
            <attribute
                name="t-if"
                add="or (o.valued and o.sale_id and o.move_line_ids and is_outgoing)"
                separator=" "
            />
        </xpath>
        <xpath
            expr="//table[@name='stock_move_line_table']/tbody/t[@t-else='']//t[@t-if='has_serial_number']"
            position="attributes"
        >
            <attribute
                name="t-if"
                add="or (o.valued and o.sale_id and o.move_line_ids and is_outgoing)"
                separator=" "
            />
        </xpath>
        <xpath expr="//table[@name='stock_move_line_table']" position="after">
            <t t-if="o.valued and o.sale_id and o.move_line_ids and is_outgoing">
                <table class="table table-sm mt32">
                    <thead>
                        <tr>
                            <th class="text-end"><strong>Untaxed Amount</strong></th>
                            <th class="text-end"><strong>Taxes</strong></th>
                            <th class="text-end"><strong>Total</strong></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="text-end">
                                <span t-field="o.amount_untaxed" />
                            </td>
                            <td class="text-end">
                                <span t-field="o.amount_tax" />
                            </td>
                            <td class="text-end">
                                <span t-field="o.amount_total" />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </t>
        </xpath>
    </template>

    <template
        id="valued_report_picking_has_serial_move_line"
        inherit_id="stock.stock_report_delivery_has_serial_move_line"
        priority="17"
    >
        <xpath expr="." position="inside">
            <t t-if="o.valued and o.sale_id and o.move_line_ids and is_outgoing">
                <td class="text-end"><span t-field="move_line.sale_price_unit" /></td>
                <td class="text-end" groups="product.group_discount_per_so_line">
                    <span t-field="move_line.sale_discount" />
                </td>
                <td class="text-end"><span
                        t-field="move_line.sale_price_subtotal"
                    /></td>
                <td class="text-end"><span
                        t-field="move_line.sale_tax_description"
                    /></td>
            </t>
        </xpath>
    </template>

</odoo>
