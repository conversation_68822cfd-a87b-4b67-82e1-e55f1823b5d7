<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Inventory settings form view inherited   -->
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.user.warehouse.restriction</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="1"/>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='use_own_routes']" position="after">
                <setting id="warehouse_restriction"
                         invisible="group_stock_multi_locations == False"
                             help="Enable Warehouse restriction">
                         <field name="group_user_warehouse_restriction" on_change="1"/>
                    </setting>
            </xpath>
        </field>
    </record>
</odoo>
