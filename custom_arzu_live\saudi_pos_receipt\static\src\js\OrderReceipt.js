/** @odoo-module **/

import { OrderReceipt } from "@point_of_sale/app/screens/receipt_screen/receipt/order_receipt";
import { ReceiptScreen } from "@point_of_sale/app/screens/receipt_screen/receipt_screen";

import { Orderline, Order, Payment } from "@point_of_sale/app/store/models";
import { patch } from "@web/core/utils/patch";

// Patch Orderline to add missing properties to getDisplayData
patch(Orderline.prototype, {
    getDisplayData() {
        const originalData = super.getDisplayData();
        return {
            ...originalData,
            is_discount_product: this.product ? this.product.is_discount_product : false,
            price_numeric: this.get_display_price(),
            qty_numeric: this.get_quantity(),
            discount_numeric: this.get_discount(),
            product_code: this.product ? this.product.default_code : '',
            product_display_name: this.product ? this.product.display_name : '',
        };
    }
});

// Patch Payment to extend export_for_printing with payment method info
patch(Payment.prototype, {
    export_for_printing() {
        const originalData = super.export_for_printing();
        return {
            ...originalData,
            payment_method: this.payment_method ? {
                type: this.payment_method.type,
                name: this.payment_method.name,
            } : null,
        };
    }
});

// Patch Order to extend export_for_printing with customer data
patch(Order.prototype, {
    export_for_printing() {
        const originalData = super.export_for_printing();
        
        // Calculate total amount and total discount from orderlines
        let total_amount = 0;
        let total_discount = 0;
        this.orderlines.forEach(function(line) {
            if (line.product && line.product.is_discount_product) {
                total_discount += line.get_display_price();
            } else {
                total_amount += line.get_display_price();
            }
        });
        
        return {
            ...originalData,
            customer: this.get_partner() ? {
                name: this.get_partner().name,
                vat: this.get_partner().vat,
                phone: this.get_partner().phone,
                email: this.get_partner().email,
            } : null,
            total_amount: total_amount,
            total_discount: total_discount,
            shop_name: this.pos.config.name,
        };
    }
});

patch(ReceiptScreen.prototype, {
    async printReceipt() {
        this.buttonPrintReceipt.el.className = "fa fa-fw fa-spin fa-circle-o-notch";
        const isPrinted = await this.printer.print(
            OrderReceipt,
            {
                data: this.pos.get_order().export_for_printing(),
                formatCurrency: this.env.utils.formatCurrency,
            },
            { webPrintFallback: true }
        );

        if (isPrinted) {
            this.currentOrder._printed = true;
        }

        // Double print logic
        const posConfig = this.env.services.pos.config;
        if (posConfig && posConfig.enable_double_printing) {
            console.log("Double printing enabled, printing again...");
            await new Promise(resolve => setTimeout(resolve, 1000));
            await this.printer.print(
                OrderReceipt,
                {
                    data: this.pos.get_order().export_for_printing(),
                    formatCurrency: this.env.utils.formatCurrency,
                },
                { webPrintFallback: true }
            );
        }

        if (this.buttonPrintReceipt.el) {
            this.buttonPrintReceipt.el.className = "fa fa-print";
        }
    }
});



