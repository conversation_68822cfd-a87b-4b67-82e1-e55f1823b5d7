<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_users_form" model="ir.ui.view">
        <field name="name">usability.default_warehouse.res.users.form</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <group name="preferences" position="after">
                <group string="Warehouse Restrictions" options="{'invisible': [('id','=', 1)]}">
                    <field name="stock_location_ids" widget="many2many_tags"/>
                    <field name="default_picking_type_ids" widget="many2many_tags"
                           options="{'invisible': [('restrict_locations','=', False)]}"/>
                    <field name="restrict_locations" string="Restriction status"/>
                </group>
            </group>
        </field>
    </record>
</odoo>
