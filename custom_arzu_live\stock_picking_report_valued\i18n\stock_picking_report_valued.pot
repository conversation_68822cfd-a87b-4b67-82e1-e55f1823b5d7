# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_picking_report_valued
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Discount</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Subtotal</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Taxes</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Total</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Unit Price</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Untaxed Amount</strong>"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_res_partner
msgid "Contact"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__currency_id
msgid "Currency"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_subtotal
msgid "Price subtotal"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_line
msgid "Related order line"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__currency_id
msgid "Sale Currency"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_unit
msgid "Sale Price Unit"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_tax_id
msgid "Sale Tax"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_discount
msgid "Sale discount (%)"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_tax_description
msgid "Tax Description"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_tax
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_tax
msgid "Taxes"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_total
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_total
msgid "Total"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_untaxed
msgid "Untaxed Amount"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_res_partner__valued_picking
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_res_users__valued_picking
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__valued
msgid "Valued Picking"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,help:stock_picking_report_valued.field_res_partner__valued_picking
#: model:ir.model.fields,help:stock_picking_report_valued.field_res_users__valued_picking
#: model:ir.model.fields,help:stock_picking_report_valued.field_stock_picking__valued
msgid "You can select which partners have valued pickings"
msgstr ""
