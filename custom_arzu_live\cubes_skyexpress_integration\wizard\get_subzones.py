import requests

from odoo import api, fields, models


class GetSubZone(models.Model):
    _name = 'get.sub.zone'

    country_id = fields.Many2one(
        comodel_name='skyex.country',
        string="Country"
    )
    zone_ids = fields.Many2many(
        comodel_name='skyex.zones',
        string="Zones"
    )

    def get_sub_zones(self):
        config = self.env['sky.express.configuration'].search([], limit=1)
        query = """
            query ($input: ListZonesFilterInput) {
              listZonesDropdown(input: $input) {
                id
                code
                name
              }
            }
            """

        headers = {
            "Content-Type": "application/json",
            "Authorization": config.access_token
        }

        for rec in self.zone_ids:
            variables = {
                "input": {
                    "name": rec.name,
                    "parentId": int(rec.api_id),
                    "countryId": int(self.country_id.api_id),
                    "active": True,
                }
            }

            response = requests.post(config.url, json={"query": query, "variables": variables}, headers=headers)
            data = response.json()
            for line in data['data']['listZonesDropdown']:
                if not self.env['skyex.subzones'].search([('api_id', '=', str(line.get('code')))]):
                    id = self.env['skyex.subzones'].create({
                        'api_id': line.get('id'),
                        'name': line.get('name'),
                        'code': line.get('code'),
                        'zone_id': rec.id,
                    })
        return data.get('data', {}).get('listZonesDropdown', [])
