# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_advance_cash_payment
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-05 11:43+0000\n"
"PO-Revision-Date: 2022-01-05 11:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Add Payment"
msgstr "Agregar pago"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Amount:"
msgstr "Monto:"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Apply"
msgstr "Solicitar"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__create_date
msgid "Created on"
msgstr "Creado en"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Enter Details Here"
msgstr "Ingrese los detalles aquí"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__id
msgid "ID"
msgstr "IDENTIFICACIÓN"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__write_uid
msgid "Last Updated by"
msgstr "Actualizado por última vez por"

#. module: pos_advance_cash_payment
#: model:ir.model.fields,field_description:pos_advance_cash_payment.field_pos_create_customer_payment__write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: pos_advance_cash_payment
#. openerp-web
#: code:addons/pos_advance_cash_payment/static/src/xml/pos_extended.xml:0
#, python-format
msgid "Payment Type:"
msgstr "Tipo de pago:"

#. module: pos_advance_cash_payment
#: model:ir.model,name:pos_advance_cash_payment.model_pos_create_customer_payment
msgid "pos.create.customer.payment"
msgstr ""
