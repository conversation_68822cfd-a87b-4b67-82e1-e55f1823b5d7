<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_product_label_document_with_price">
        <t t-call="web.basic_layout">
            <main>
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&amp;display=swap');
                    
                    body {
                        padding: 0 !important;
                        margin: 0 !important;
                        font-family: 'Cairo', 'Arial Unicode MS', 'Tahoma', sans-serif;
                    }
                    .page {
                        padding: 0 !important;
                        margin: 0 !important;
                    }
                    main {
                        padding: 0 !important;
                        margin: 0 !important;
                    }
                    .label-wrapper {
                        padding: 5mm 0 0 0;
                        margin: 0;
                        page-break-inside: avoid;
                        font-family: 'Cairo', 'Arial Unicode MS', 'Tahoma', sans-serif;
                    }
                    <!-- .title {
                        font-size: 10px;
                        font-weight: bold;
                        margin: 0 0 1mm 0;
                        padding: 0;
                        font-family: 'Cairo', 'Arial Unicode MS', 'Tahoma', sans-serif;
                    } -->
                    .product-name {
                        font-size: 8px;
                        margin: 0.5mm 0;
                        padding: 0;
                        font-family: 'Cairo', 'Arial Unicode MS', 'Tahoma', sans-serif;
                        font-weight: normal;
                    }
                    .barcode-section {
                        margin: 0.5mm 0;
                        padding: 0;
                    }
                    .barcode-number {
                        font-size: 14px;
                        margin-top: 0.2mm;
                        padding: 0;
                        font-family: 'Courier New', monospace;
                        font-weight: bold;
                    }
                    .price-section {
                        font-size: 22px;
                        margin-top: 0.5mm;
                        padding: 0;
                        font-weight: normal;
                        direction: rtl;
                        font-family: 'Cairo', 'Arial Unicode MS', 'Tahoma', sans-serif;
                    }
                    .price-label {
                        display: inline-block;
                        margin-left: 2px;
                    }
                </style>
                <t t-foreach="quantity_by_product.keys()" t-as="product_id">
                    <t t-set="product" t-value="env['product.product'].browse(int(product_id))"/>
                    <t t-set="product_price" t-value="price_by_product.get(product_id, product.lst_price)"/>
                    <t t-set="pricelist" t-value="env['product.pricelist'].browse(pricelist_id) if pricelist_id else False"/>
                    <t t-foreach="range(quantity_by_product.get(product_id, 0))" t-as="i">
                        <div class="label-wrapper">
                           <!-- <div class="text-center title">بطاقة صنف</div>  -->
                           <!-- <div class="text-center product-name" t-field="product.name"/> -->
                            <div class="text-center barcode-section">
                                <div t-out="product.default_code" style="padding:0; margin:0;" t-options="{'widget': 'barcode', 'quiet': 0, 'symbology': 'auto', 'img_style': 'width:40mm; height:8mm'}"/>
                                <div class="barcode-number text-center">
                                    <span t-out="product.default_code"/>
                                </div>
                            </div>
                            <div class="text-center price-section">
                                <!-- <span class="price-label">السعر:</span>  -->
                                <span t-out="product_price" t-options='{"widget": "monetary", "display_currency": pricelist.currency_id if pricelist else product.currency_id}'/>
                            </div>
                        </div>
                    </t>
                </t>
            </main>
        </t>
    </template>

    <record id="action_report_product_label_price" model="ir.actions.report">
        <field name="name">Product Label with Price</field>
        <field name="model">product.product</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">elgahad_inventory.report_product_label_document_with_price</field>
        <field name="report_file">elgahad_inventory.report_product_label_document_with_price</field>
        <field name="paperformat_id" ref="paperformat_product_label"/>
        <field name="print_report_name">'Product Label with Price - %s' % object.name</field>
        <field name="binding_model_id" ref="product.model_product_product"/>
        <field name="binding_type">report</field>
    </record>
</odoo> 