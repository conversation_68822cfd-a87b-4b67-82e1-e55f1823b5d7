<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="paperformat_label_sheet_dymo_customized" model="report.paperformat">
        <field name="name">Dymo Label Sheet</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">40</field>
        <field name="page_width">60</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">96</field>
    </record>

    <record id="print_report_label" model="ir.actions.report">
        <field name="name">Print Label</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_order_delivery_operations.report_product_label_template</field>
        <field name="report_file">sale_order_delivery_operations.report_product_label_template</field>
        <field name="print_report_name">'Print Labels - %s' % (object.name)</field>
        <field name="paperformat_id" ref="sale_order_delivery_operations.paperformat_label_sheet_dymo_customized"/>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_product_label_template">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <style>
                    .page {
                        display: flex;
                        align-items: flex-start;
                        justify-content: flex-start;
                        width: 170.08px;   /* 60mm */
                        height: 113.39px;  /* 40mm */
                        font-size: 6px;
                        box-sizing: border-box;
                        padding: 1px;
                        margin: 0;
                        text-align: center;
                        overflow: hidden;
                        max-width: 170.08px;
                        max-height: 113.39px;
                    }

                    .table {
                        width: 100%;
                        max-width: 100%;
                        height: 100%;
                        table-layout: fixed;
                        border: 1px solid black;
                        border-radius: 2px;
                        border-collapse: collapse;
                        margin: 0;
                    }

                    .table td {
                        padding: 0.5px 1px;
                        border: 1px solid black;
                        overflow-wrap: break-word;
                        word-break: break-word;
                        vertical-align: middle;
                        font-size: 6px;
                        line-height: 1.0;
                        height: 25px;
                    }

                    .rtl .table {
                        text-align: right;
                    }

                    .ltr .table {
                        text-align: left;
                    }

                    .fw-bold {
                        font-weight: bold;
                    }
                </style>
                <t t-foreach="docs" t-as="o">
                    <t t-set="is_arabic"
                       t-value="request.env.user.lang == 'ar_SY' or request.env.user.lang.startswith('ar')"/>
                    <div t-attf-class="page #{'rtl' if is_arabic else 'ltr'}"
                         t-attf-dir="#{'rtl' if is_arabic else 'ltr'}">
                        <table class="table">
                            <colgroup>
                                <col style="width: 35%;" />
                                <col style="width: 65%;" />
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td>
                                        <span class="fw-bold">Delivery Operation No:</span>
                                    </td>
                                    <td>
                                        <span t-esc="o.display_name"/>
                                    </td>

                                </tr>
                                <tr>
                                    <td>
                                        <span class="fw-bold">Client Name:</span>
                                    </td>
                                    <td>
                                        <span t-esc="o.partner_id.name"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="fw-bold">Client Phone:</span>
                                    </td>
                                    <td>
                                        <span t-esc="o.phone"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <span class="fw-bold">Client Address:</span>
                                    </td>
                                    <td>
                                        <span t-esc="o.street"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
