<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="paperformat_label_sheet_dymo_customized" model="report.paperformat">
        <field name="name">Dymo Label Sheet</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">40</field>
        <field name="page_width">60</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">96</field>
    </record>

    <record id="print_report_label" model="ir.actions.report">
        <field name="name">Print Label</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_order_delivery_operations.report_product_label_template_v2</field>
        <field name="report_file">sale_order_delivery_operations.report_product_label_template_v2</field>
        <field name="print_report_name">'Print Labels - %s' % (object.name)</field>
        <field name="paperformat_id" ref="sale_order_delivery_operations.paperformat_label_sheet_dymo_customized"/>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_product_label_template_v2">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <style>
                    @page {
                        margin: 0;
                        padding: 0;
                        size: 60mm 40mm;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, sans-serif;
                    }

                    .page {
                        width: 60mm;
                        height: 40mm;
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    .label-table {
                        width: 100%;
                        height: 100%;
                        border: 2px solid black;
                        border-collapse: collapse;
                        table-layout: fixed;
                        margin: 0;
                        padding: 0;
                        font-size: 10pt;
                        font-weight: bold;
                        page-break-inside: avoid;
                    }

                    .label-table td {
                        border: 1px solid black;
                        text-align: center;
                        vertical-align: middle;
                        padding: 1mm;
                        width: 50%;
                        height: 10mm;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                        border-right: 1px solid black;
                        border-bottom: 1px solid black;
                    }

                    .label-table td:last-child {
                        border-right: 2px solid black;
                    }

                    .label-table tr:last-child td {
                        border-bottom: 2px solid black;
                    }

                    .label-table td:first-child {
                        border-left: 2px solid black;
                    }

                    .label-table tr:first-child td {
                        border-top: 2px solid black;
                    }

                    .rtl {
                        direction: rtl;
                        text-align: center;
                    }

                    .ltr {
                        direction: ltr;
                        text-align: center;
                    }
                </style>
                <t t-foreach="docs" t-as="o">
                    <t t-set="is_arabic"
                       t-value="request.env.user.lang == 'ar_SY' or request.env.user.lang.startswith('ar')"/>

                    <!-- Updated Two-Column Table Layout v4 -->
                    <div class="page">
                        <table class="label-table" t-attf-dir="#{'rtl' if is_arabic else 'ltr'}">
                            <tr>
                                <td t-esc="o.display_name"/>
                                <td>رقم عملية التسليم:</td>
                            </tr>
                            <tr>
                                <td t-esc="o.partner_id.name"/>
                                <td>اسم العميل:</td>
                            </tr>
                            <tr>
                                <td t-esc="o.phone"/>
                                <td>هاتف العميل:</td>
                            </tr>
                            <tr>
                                <td t-esc="o.street"/>
                                <td>عنوان العميل:</td>
                            </tr>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
