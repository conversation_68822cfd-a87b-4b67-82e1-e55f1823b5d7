<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="paperformat_label_sheet_dymo_customized" model="report.paperformat">
        <field name="name">Dymo Label Sheet</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">40</field>
        <field name="page_width">60</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="disable_shrinking" eval="True"/>
        <field name="dpi">96</field>
    </record>

    <record id="print_report_label" model="ir.actions.report">
        <field name="name">Print Label</field>
        <field name="model">stock.picking</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">sale_order_delivery_operations.report_product_label_template_v2</field>
        <field name="report_file">sale_order_delivery_operations.report_product_label_template_v2</field>
        <field name="print_report_name">'Print Labels - %s' % (object.name)</field>
        <field name="paperformat_id" ref="sale_order_delivery_operations.paperformat_label_sheet_dymo_customized"/>
        <field name="binding_model_id" ref="stock.model_stock_picking"/>
        <field name="binding_type">report</field>
    </record>

    <template id="report_product_label_template_v2">
        <t t-call="web.html_container">
            <t t-call="web.basic_layout">
                <style>
                    @page {
                        margin: 0;
                        padding: 0;
                        size: 60mm 40mm;
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, sans-serif;
                    }

                    .label-container {
                        width: 60mm;
                        height: 40mm;
                        border: 2px solid black;
                        box-sizing: border-box;
                        display: flex;
                        flex-direction: column;
                        margin: 0;
                        padding: 0;
                        background: white;
                    }

                    .label-row {
                        flex: 1;
                        display: flex;
                        border-bottom: 1px solid black;
                        min-height: 9mm;
                    }

                    .label-row:last-child {
                        border-bottom: none;
                    }

                    .label-cell {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        padding: 1mm;
                        font-size: 10pt;
                        font-weight: bold;
                        box-sizing: border-box;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                        flex: 1;
                    }

                    .label-cell.data-cell {
                        border-right: 1px solid black;
                    }

                    .label-cell.label-text {
                        border-right: none;
                    }

                    .rtl {
                        direction: rtl;
                        text-align: center;
                    }

                    .ltr {
                        direction: ltr;
                        text-align: center;
                    }
                </style>
                <t t-foreach="docs" t-as="o">
                    <t t-set="is_arabic"
                       t-value="request.env.user.lang == 'ar_SY' or request.env.user.lang.startswith('ar')"/>

                    <!-- Updated Two-Column Layout v2 -->
                    <div t-attf-class="label-container #{'rtl' if is_arabic else 'ltr'}"
                         t-attf-dir="#{'rtl' if is_arabic else 'ltr'}">

                        <!-- Row 1: Delivery Operation Number -->
                        <div class="label-row">
                            <div class="label-cell data-cell" t-esc="o.display_name"/>
                            <div class="label-cell label-text">رقم 33333عملية التسليم:</div>
                        </div>

                        <!-- Row 2: Client Name -->
                        <div class="label-row">
                            <div class="label-cell data-cell" t-esc="o.partner_id.name"/>
                            <div class="label-cell label-text">اسم العميل:</div>
                        </div>

                        <!-- Row 3: Client Phone -->
                        <div class="label-row">
                            <div class="label-cell data-cell" t-esc="o.phone"/>
                            <div class="label-cell label-text">هاتف العميل:</div>
                        </div>

                        <!-- Row 4: Client Address -->
                        <div class="label-row">
                            <div class="label-cell data-cell" t-esc="o.street"/>
                            <div class="label-cell label-text">عنوان العميل:</div>
                        </div>

                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
