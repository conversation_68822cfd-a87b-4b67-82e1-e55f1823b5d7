<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="saudi_pos_receipt.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('pos-receipt')]" position="attributes">
            <attribute name="style">font-family:"Arial";</attribute>
        </xpath>

        <!-- Replace the entire receipt content -->
        <xpath expr="//div[hasclass('pos-receipt')]" position="replace">
            <div class="pos-receipt" style="font-family:'Arial'; font-weight:bold;">
                <!-- Header Section -->
                <div class="pos-receipt-contact">
                    <table width="100%">
                        <tr>
                            <td>
                                <t t-if="this.env.services.pos.config.image">
                                    <!-- <img class="pos-receipt-logo" t-att-src="props.data.headerData.company.logo" alt="Logo"/> -->

                                    <img t-if="this.env.services.pos.config.image" t-attf-src="/web/image?model=pos.config&amp;field=image&amp;id={{ this.env.services.pos.config.id }}" class="pos-receipt-logo" />
                                    <br/>
                                </t>
                                <t t-if="!this.env.services.pos.config.image">
                                    <h2 class="pos-receipt-center-align">
                                        
                                        <t t-esc="props.data.headerData.company.name"/>
                                        
                                    </h2>

                                    <br/>
                                </t>
                            </td>
                            <td>
                                <div class="pos-receipt-contact" style="font-size: 15px;">
                                    <!-- <div>
                                        <t t-esc="props.data.headerData.company.partner_id[1]" />
                                    </div> -->
                                    
                                    <t t-if="props.data.headerData.company.phone">
                                        <div>Tel:<t t-esc="props.data.headerData.company.phone"/>
                                        </div>
                                    </t>
                                    <t t-if="props.data.headerData.company.email">
                                        <div>
                                            <t t-esc="props.data.headerData.company.email" />
                                        </div>
                                    </t>
                                    <t t-if="props.data.headerData.company.website">
                                        <div>
                                            <t t-esc="props.data.headerData.company.website" />
                                        </div>
                                    </t>
                                    <t t-if="props.data.headerData.company.vat">
                                        <div>الرقم الضريبي                                            <t t-esc="props.data.headerData.company.vat"/>
                                        </div>
                                    </t>
                                    <t t-if="props.data.headerData.header">
                                        <div style="white-space:pre-line">
                                            <t t-esc="props.data.headerData.header" />
                                        </div>
                                    </t>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

               

                <!-- Order Information -->
                <div style='text-align:center;border-bottom: 1px black;margin-bottom:3px;border: 1pt solid black;'>
                    <div>
                        <strong> فاتورة مبيعات </strong>
                    </div>
                    <div>
                        <strong>
                            <t t-esc="props.data.name" />
                        </strong>
                    </div>
                    <t t-if="props.data.date">
                        <div>
                            <strong>
                                <t t-esc="props.data.date" />
                            </strong>
                        </div>
                    </t>
                </div>
                <br/>

                <!-- Customer Information -->
                <table class='receipt-orderlines'>
                    <colgroup>
                        <col width='35%'/>
                        <col width='65%'/>
                    </colgroup>
                    <t t-if="props.data.customer">
                        <tr>
                            <td>Customer: <br/>
                           العميل </td>
                            <td>
                                <t t-if="props.data.customer.name">
                                    <span>
                                        <t t-esc="props.data.customer.name"/>
                                    </span>
                                </t>
                            </td>
                        </tr>
                        <t t-if="props.data.customer.vat">
                            <tr>
                                <td>VAT: <br/>
                                                الرقم الضريبي </td>
                                <td>
                                    <span>
                                        <t t-esc="props.data.customer.vat or ''"/>
                                    </span>
                                </td>
                            </tr>
                        </t>
                        <t t-if="props.data.customer.phone">
                            <tr>
                                <td>Mobile: <br/>
                                            الهاتف </td>
                                <td>
                                    <span>
                                        <t t-esc="props.data.customer.phone or ''"/>
                                    </span>
                                </td>
                            </tr>
                        </t>
                    </t>
                    <tr t-if="props.data.cashier" style="border-bottom: 0px solid #cccccc;">
                        <td>الموظف : </td>
                        <td>
                            <t t-esc="props.data.cashier"/>
                        </td>
                    </tr>
                </table>
                <br/>

                <!-- Orderlines -->
                <div class="orderlines">
                    <div style="text-align:center;">
                        <span style="font-weight: 800;">الأصناف</span>
                    </div>
                    <table class='receipt-orderlines' dir="rtl">
                        <thead>
                            <colgroup>
                                <col width='10%'/>
                                <col width='45%'/>
                                <col width='15%'/>
                                <col width='15%'/>
                                <col width='15%'/>
                            </colgroup>
                            <tr style="border-bottom: 2px black;border-top: 2px black;">
                                <th style='text-align:center;font-size:11px;border: 1pt solid black;' t-translation="off" width='10%'>الرقم</th>
                                <th style='text-align:center;font-size:11px;border: 1pt solid black;' t-translation="off" width='45%'>الصنف</th>
                                <th style='text-align:center;font-size:11px;border: 1pt solid black;' t-translation="off" width='15%'>الكمية</th>
                                <th style='text-align:center;font-size:11px;border: 1pt solid black;' t-translation="off" width='15%'>سعر الوحدة</th>
                                <th style='text-align:center;font-size:11px;border: 1pt solid black;' t-translation="off" width='15%'>الاجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-set="i" t-value="1"/>
                            <t t-set="q" t-value="0"/>
                            <t t-set="local_total_amount" t-value="0"/>
                            <t t-set="local_total_discount" t-value="0"/>
                            <t t-foreach="props.data.orderlines" t-as="orderline" t-key="orderline.id">
                                <tr style="border-bottom: 1px black;text-align:right;font-size:12px;border: 1pt solid black;">
                                    <t t-if="orderline.is_discount_product">
                                        <t t-set="local_total_discount" t-value="local_total_discount+(orderline.price_numeric || 0)"/>
                                    </t>
                                    <t t-if="!orderline.is_discount_product">
                                        <t t-set="local_total_amount" t-value="local_total_amount+(orderline.price_numeric || 0)"/>
                                        <td style="border: 1pt solid black;">
                                            <span t-esc="i" class="text-center"/>
                                        </td>
                                        <td style="border: 1pt solid black;">
                                            <span>
                                                <t t-if="orderline.product_code">
                                            [                                                    <t t-esc="orderline.product_code"/>
]
                                                </t>

                                                <t t-esc="orderline.productName"/>
                                            </span>

                                        </td>
                                        <td style='text-align:center;border: 1pt solid black;'>
                                            <t t-esc="orderline.qty"/>
                                            <t t-set="q" t-value="q+ (orderline.qty_numeric || 0)"/>
                                        </td>
                                        <td style="border: 1pt solid black;" class='td_discount'>
                                            <t t-set="disc" t-value="orderline.discount_numeric || 0"/>
                                            <t t-if="disc > 0">
                                                <span t-esc="orderline.oldUnitPrice" style=" color: red; text-decoration: line-through;"/>
                                                <br/>
                                                <span t-esc="orderline.unitPrice"/>
                                            </t>
                                            <t t-else="" class="unit_price_line">
                                                <t t-esc="orderline.unitPrice"/>
                                            </t>
                                        </td>
                                        <td style='text-align:right;border: 1pt solid black;'>
                                            <t t-esc="orderline.price"/>
                                        </td>
                                        <t t-set="i" t-value="i+1"/>
                                    </t>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>

                <!-- Totals and Payment Summary -->
                <br/>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">الإجمالي قبل الخصم</td>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">
                            <t t-esc="props.formatCurrency(props.data.total_amount)"/>
                        </td>

                    </tr>
                    <tr>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">الخصم</td>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">
                            <t t-esc="props.formatCurrency(props.data.total_discount)"/>
                        </td>

                    </tr>
                    <tr>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">الصافي</td>
                        <td style="width: 33%;border: 1pt solid black;font-size:13px;text-align:center">
                            <t t-esc="props.formatCurrency(props.data.amount_total)"/>
                        </td>

                    </tr>
                    <tr>
                        <td style="width: 20%;border: 1pt solid black;font-size:13px;text-align:center">المدفوع</td>
                        <td style="width: 20%;border: 1pt solid black;font-size:13px;text-align:center">
                            <t t-esc="props.formatCurrency(props.data.amount_total)"/>
                        </td>
                        <td style="width: 20%;border: 1pt solid black;font-size:10px;text-align:center">عددالاصناف</td>
                        <td style="width: 20%;border: 1pt solid black;font-size:13px;text-align:center">
                            <t t-esc="i-1"/>
                        </td>

                    </tr>
                    <t t-foreach="props.data.paymentlines" t-as="line" t-key="line.cid">
                        <tr>
                            <td style="font-size: bold;;width: 25%;border: 1pt solid black;text-align:center;font-size:13px">
                                <t t-esc="line.name"/>
                            </td>
                            <td style="font-size: bold;;width: 25;border: 1pt solid black;text-align:center;font-size:13px">
                                <t t-esc="props.formatCurrency(line.amount)"/>
                            </td>
                            <td style="font-size: bold;;width: 25;border: 1pt solid black;text-align:center;font-size:13px">
                             وسائل الدفع
                            </td>
                        </tr>
                    </t>
                </table>

                <!-- Footer -->
                <div class="before-footer">
                    <div id="notes" style="margin-top: 10px; text-align: center">

                        <t t-if="this.has_cash_payment">
                            <div>ملاحظه:البضاعة المباعة لا تسترجع وتستبدل خلال 24 ساعة </div>
                        </t>
                        <t t-else="">
                            <div>ملاحظه:البضاعة المباعة لا تسترجع وتستبدل خلال 24 ساعة </div>
                        </t>
                    </div>
                </div>

                <!-- QR Code -->
                <div t-if="props.data.pos_qr_code">
                    <br/>
                    <br/>
                    <div t-attf-id="qrcode_container">
                        <div id="qrcode"></div>
                    </div>
                    <img id="posqrcode" t-att-src="props.data.pos_qr_code" class="pos-receipt-logo"/>
                </div>

                <!-- Footer -->
                <div t-if="props.data.footer" class="pos-receipt-center-align" style="white-space:pre-line">
                    <br/>
                    <t t-esc="props.data.footer" />
                    <br/>
                    <br/>
                </div>

                <!-- Order Info -->
<!--                <br/>-->
<!--                <div class="pos-receipt-order-data">-->
<!--                    <p>Odoo Point of Sale</p>-->
<!--                    <div t-esc="props.data.name" />-->
<!--                    <div t-esc="props.data.date" />-->
<!--                </div>-->
            </div>
        </xpath>
    </t>
</templates>