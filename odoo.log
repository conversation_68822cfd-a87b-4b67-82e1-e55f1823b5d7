2025-08-27 12:05:14,130 39664 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 12:05:14,130 39664 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 12:05:14,130 39664 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_addons', 'c:\\odoo17\\server\\custom_alfnia_testing', 'c:\\odoo17\\server\\custom_arzu_live'] 
2025-08-27 12:05:14,130 39664 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 12:05:14,230 39664 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 12:05:15,827 39664 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 12:05:16,630 39664 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2072, in statics
    manifest = get_manifest(module)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 369, in get_manifest
    return copy.deepcopy(_get_manifest_cached(module, mod_path))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 373, in _get_manifest_cached
    return load_manifest(module, mod_path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 319, in load_manifest
    manifest.update(ast.literal_eval(f.read()))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\_monkeypatches.py", line 78, in literal_eval
    return orig_literal_eval(expr)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 112, in literal_eval
    return _convert(node_or_string)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 101, in _convert
    return dict(zip(map(_convert, node.keys),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 111, in _convert
    return _convert_signed_num(node)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 85, in _convert_signed_num
    return _convert_num(node)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 76, in _convert_num
    _raise_malformed_node(node)
  File "C:\Odoo17\python\Lib\ast.py", line 73, in _raise_malformed_node
    raise ValueError(msg + f': {node!r}')
ValueError: malformed node or string on line 14: <ast.Name object at 0x000002113D929350>
2025-08-27 12:05:16,631 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:16] "GET /web/database/selector HTTP/1.1" 500 - 1 0.010 0.589
2025-08-27 12:05:16,764 39664 ERROR ? odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2202, in __call__
    if self.get_static_file(httprequest.path):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2102, in get_static_file
    if (module not in self.statics or static != 'static' or not resource):
                      ^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 28, in __get__
    value = self.fget(obj)
            ^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 2072, in statics
    manifest = get_manifest(module)
               ^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 369, in get_manifest
    return copy.deepcopy(_get_manifest_cached(module, mod_path))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 373, in _get_manifest_cached
    return load_manifest(module, mod_path)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\modules\module.py", line 319, in load_manifest
    manifest.update(ast.literal_eval(f.read()))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\_monkeypatches.py", line 78, in literal_eval
    return orig_literal_eval(expr)
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 112, in literal_eval
    return _convert(node_or_string)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 101, in _convert
    return dict(zip(map(_convert, node.keys),
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 111, in _convert
    return _convert_signed_num(node)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 85, in _convert_signed_num
    return _convert_num(node)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Odoo17\python\Lib\ast.py", line 76, in _convert_num
    _raise_malformed_node(node)
  File "C:\Odoo17\python\Lib\ast.py", line 73, in _raise_malformed_node
    raise ValueError(msg + f': {node!r}')
ValueError: malformed node or string on line 14: <ast.Name object at 0x000002113D96FB50>
2025-08-27 12:05:16,764 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:16] "GET /web/image/website/1/favicon?unique=183d671 HTTP/1.1" 500 - 1 0.000 0.113
2025-08-27 12:05:18,307 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:18] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.019 0.123
2025-08-27 12:05:48,062 39664 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:05:48] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.015 0.058
2025-08-27 12:07:46,564 38472 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 12:07:46,564 38472 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 12:07:46,564 38472 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_arzu_live'] 
2025-08-27 12:07:46,564 38472 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 12:07:46,740 38472 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 12:07:48,529 38472 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 12:07:49,424 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_costumer_due_amount_payment', defaulting to LGPL-3 
2025-08-27 12:07:49,424 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_customer_payment_customization', defaulting to LGPL-3 
2025-08-27 12:07:49,424 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_internal_transfer_request', defaulting to LGPL-3 
2025-08-27 12:07:49,424 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_inventory_customization', defaulting to LGPL-3 
2025-08-27 12:07:49,424 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_landed_cost_report', defaulting to LGPL-3 
2025-08-27 12:07:49,429 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_product_access', defaulting to LGPL-3 
2025-08-27 12:07:49,430 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'cubes_skyexpress_integration', defaulting to LGPL-3 
2025-08-27 12:07:49,430 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'dvit_account_journal_restrict', defaulting to LGPL-3 
2025-08-27 12:07:49,432 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'pos_hide_product_info', defaulting to LGPL-3 
2025-08-27 12:07:49,440 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'product_modifications', defaulting to LGPL-3 
2025-08-27 12:07:49,440 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 12:07:49,440 38472 WARNING ? odoo.modules.module: Missing `license` key in manifest for 'saudi_pos_receipt', defaulting to LGPL-3 
2025-08-27 12:08:05,663 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,663 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,663 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,663 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,676 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,676 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,681 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,692 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,692 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,692 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,696 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,696 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,696 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,701 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,709 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,709 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,709 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,714 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,716 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,718 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,718 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,718 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,718 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,726 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,726 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,729 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,742 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,742 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,742 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,747 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,748 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,748 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:05,748 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,748 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,756 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,758 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,758 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,765 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,766 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,766 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,770 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,770 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,774 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,780 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,780 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,783 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,785 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,788 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,788 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,791 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,791 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,791 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,796 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,796 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,803 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,805 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,806 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,808 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,810 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,813 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,816 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,818 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,818 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,818 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,824 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,826 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,828 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,830 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,830 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,833 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,837 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,838 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,838 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,838 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,838 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,847 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,849 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,849 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,849 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,855 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,855 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,855 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,855 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,863 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,867 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,867 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,870 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,870 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,870 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=7/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:05,875 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=6/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:06,113 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/database/selector HTTP/1.1" 200 - 231 8.795 8.603
2025-08-27 12:08:06,316 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/src/libs/fontawesome/css/font-awesome.css HTTP/1.1" 304 - 1 0.009 0.171
2025-08-27 12:08:06,333 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/dist/css/bootstrap.css HTTP/1.1" 304 - 1 0.010 0.186
2025-08-27 12:08:06,742 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/dom/data.js HTTP/1.1" 304 - 1 0.008 0.288
2025-08-27 12:08:06,757 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/dom/manipulator.js HTTP/1.1" 304 - 1 0.000 0.311
2025-08-27 12:08:06,768 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/dom/event-handler.js HTTP/1.1" 304 - 1 0.008 0.313
2025-08-27 12:08:06,859 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/base-component.js HTTP/1.1" 304 - 1 0.003 0.224
2025-08-27 12:08:06,859 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/dom/selector-engine.js HTTP/1.1" 304 - 1 0.018 0.388
2025-08-27 12:08:06,887 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:06] "GET /web/static/lib/bootstrap/js/dist/modal.js HTTP/1.1" 304 - 1 0.009 0.231
2025-08-27 12:08:07,189 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:07] "GET /web/static/img/logo2.png HTTP/1.1" 304 - 1 0.013 0.097
2025-08-27 12:08:07,221 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:07] "GET /web/static/src/public/database_manager.js HTTP/1.1" 304 - 1 0.008 0.151
2025-08-27 12:08:07,630 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:07] "GET /web/static/src/libs/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0 HTTP/1.1" 304 - 1 0.010 0.072
2025-08-27 12:08:07,879 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:07] "GET /web/static/img/favicon.ico HTTP/1.1" 304 - 1 0.008 0.062
2025-08-27 12:08:08,933 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:08] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.009 0.035
2025-08-27 12:08:39,097 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:39] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.013 0.085
2025-08-27 12:08:52,980 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,980 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,988 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:52,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,004 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,004 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,004 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,004 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,010 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,012 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,029 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,030 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,031 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,032 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,047 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,049 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,052 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,057 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,066 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,068 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,073 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,073 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:08:53,077 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,082 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,083 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,086 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,088 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,090 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,092 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,092 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,094 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,098 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,100 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,103 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,103 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,105 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,105 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,109 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,110 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,113 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,113 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,116 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,118 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,118 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,118 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,118 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,124 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,124 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,127 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,128 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,130 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,130 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,130 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,132 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,132 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,132 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,132 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,137 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,137 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,137 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,137 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,141 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,141 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,142 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,142 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,142 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,142 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,146 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,148 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,149 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,149 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,149 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,152 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,152 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,152 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,152 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,156 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,156 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,157 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=7/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,158 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=6/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:08:53,270 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:53] "GET /web/database/manager HTTP/1.1" 200 - 231 3.181 8.942
2025-08-27 12:08:56,436 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:08:56] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.010 0.060
2025-08-27 12:09:22,306 38472 INFO ? odoo.service.db: RESTORING DB: arzu_live_2 
2025-08-27 12:10:03,090 38472 INFO arzu_live_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 12:10:03,106 38472 INFO arzu_live_2 odoo.modules.loading: 1 modules loaded in 0.02s, 0 queries (+0 extra) 
2025-08-27 12:10:03,155 38472 INFO arzu_live_2 odoo.modules.loading: loading 160 modules... 
2025-08-27 12:10:05,314 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.cubes_product_access.models.product is not overriding the create method in batch 
2025-08-27 12:10:05,316 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.cubes_product_access.models.product is not overriding the create method in batch 
2025-08-27 12:10:09,816 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.cubes_skyexpress_integration.models.stock_picking is not overriding the create method in batch 
2025-08-27 12:10:09,843 38472 WARNING arzu_live_2 odoo.models: The model sky.express.configuration has no _description 
2025-08-27 12:10:09,843 38472 WARNING arzu_live_2 odoo.models: The model get.sub.zone has no _description 
2025-08-27 12:10:10,702 38472 WARNING arzu_live_2 odoo.models: The model delivery.driver has no _description 
2025-08-27 12:10:10,930 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.res_users is not overriding the create method in batch 
2025-08-27 12:10:10,944 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:10:10,944 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:10:10,947 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:10:11,089 38472 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.cubes_internal_transfer_request.model.internal_transfer_request is not overriding the create method in batch 
2025-08-27 12:10:11,089 38472 WARNING arzu_live_2 odoo.models: The model stock.type.alternative has no _description 
2025-08-27 12:10:11,089 38472 WARNING arzu_live_2 odoo.models: The model internal.transfer.line has no _description 
2025-08-27 12:10:11,089 38472 INFO arzu_live_2 odoo.modules.loading: 160 modules loaded in 7.93s, 0 queries (+0 extra) 
2025-08-27 12:10:11,383 38472 WARNING arzu_live_2 odoo.fields: stock.picking.state: selection=[('draft', 'Draft'), ('waiting', 'Waiting Another Operation'), ('confirmed', 'Waiting'), ('assigned', 'Ready'), ('driver', 'Driver'), ('done', 'Done'), ('cancel', 'Cancelled')] overrides existing selection; use selection_add instead 
2025-08-27 12:10:11,478 38472 WARNING arzu_live_2 odoo.fields: Field product.label.layout.print_format: unknown parameter 'insert_before', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-08-27 12:10:11,520 38472 WARNING arzu_live_2 odoo.fields: Field delivery.driver.name: unknown parameter 'stirng', if this is an actual parameter you may want to override the method _valid_field_parameter on the relevant model in order to allow it 
2025-08-27 12:10:11,608 38472 INFO arzu_live_2 odoo.modules.loading: Modules loaded. 
2025-08-27 12:10:11,630 38472 INFO arzu_live_2 odoo.modules.registry: Registry loaded in 8.572s 
2025-08-27 12:10:11,773 38472 INFO arzu_live_2 odoo.service.db: RESTORE DB: arzu_live_2 
2025-08-27 12:10:11,781 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:11] "POST /web/database/restore HTTP/1.1" 303 - 46 0.975 50.345
2025-08-27 12:10:24,931 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,936 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,944 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,947 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,947 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,952 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,952 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,952 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,952 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,952 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,960 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,960 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,963 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,976 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,976 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,981 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,992 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,992 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:24,997 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,000 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,000 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,000 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,000 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,000 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,009 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,009 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,009 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,013 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,017 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=64/max=64): Closed 0 connections  
2025-08-27 12:10:25,017 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,017 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,017 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,023 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,025 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,027 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,033 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,033 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,033 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,033 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,041 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,047 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,050 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,054 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,059 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,061 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,066 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,070 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,072 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,078 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,083 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,084 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,086 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,088 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,092 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,094 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,099 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,101 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,103 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,106 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,110 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,114 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,115 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,118 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,120 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,122 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,126 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,128 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,133 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,135 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,138 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,138 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,142 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,144 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,147 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,149 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,149 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,151 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,153 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,155 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,157 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,159 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,161 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,163 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,165 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,167 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,169 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=7/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,171 38472 INFO ? odoo.sql_db: ConnectionPool(used=2/count=6/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:10:25,330 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:25] "GET /web/database/manager HTTP/1.1" 200 - 233 3.475 10.058
2025-08-27 12:10:27,356 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:27] "GET /web/service-worker.js HTTP/1.1" 404 - 1 0.036 0.065
2025-08-27 12:10:32,998 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:32] "GET /web?db=arzu_live_2 HTTP/1.1" 302 - 1 0.016 0.047
2025-08-27 12:10:33,343 38472 INFO arzu_live_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-08-27 12:10:33,414 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:33] "GET /web?db=arzu_live_2 HTTP/1.1" 303 - 5 0.028 0.139
2025-08-27 12:10:34,081 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'saudi_pos_receipt', defaulting to LGPL-3 
2025-08-27 12:10:34,108 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_product_access', defaulting to LGPL-3 
2025-08-27 12:10:34,123 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_landed_cost_report', defaulting to LGPL-3 
2025-08-27 12:10:34,139 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_skyexpress_integration', defaulting to LGPL-3 
2025-08-27 12:10:34,147 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'product_modifications', defaulting to LGPL-3 
2025-08-27 12:10:34,238 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_inventory_customization', defaulting to LGPL-3 
2025-08-27 12:10:34,287 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 12:10:34,295 38472 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'cubes_internal_transfer_request', defaulting to LGPL-3 
2025-08-27 12:10:34,767 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:34] "GET /web/login?db=arzu_live_2 HTTP/1.1" 200 - 59 0.097 0.940
2025-08-27 12:10:35,283 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:35] "GET /web/binary/company_logo?dbname=arzu_live_2 HTTP/1.1" 200 - 2 0.026 0.154
2025-08-27 12:10:47,896 38472 INFO arzu_live_2 odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-08-27 12:10:47,905 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/e8b5be2/web.assets_frontend.min.css (id:14379) 
2025-08-27 12:10:47,905 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Deleting attachments [14002, 14001] (matching /web/assets/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/e8b5be2/web.assets_frontend.min.css 
2025-08-27 12:10:48,058 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:48] "GET /web/assets/e8b5be2/web.assets_frontend.min.css HTTP/1.1" 200 - 15 0.199 13.076
2025-08-27 12:10:48,065 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _file_gc could not unlink c:\odoo17\sessions\filestore\arzu_live_2\18/18550e84886a20c3961b4585ce9f602e5ba47521 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 220, in _gc_file_store_unsafe
    os.unlink(self._full_path(fname))
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\18/18550e84886a20c3961b4585ce9f602e5ba47521'
2025-08-27 12:10:48,096 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: filestore gc 2 checked, 0 removed 
2025-08-27 12:10:48,335 38472 INFO arzu_live_2 odoo.addons.base.models.res_users: GC'd 3 user log entries 
2025-08-27 12:10:48,383 38472 INFO arzu_live_2 odoo.models.unlink: User #1 deleted change.password.wizard records with IDs: [25] 
2025-08-27 12:10:48,463 38472 INFO arzu_live_2 odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-08-27 12:10:48,490 38472 INFO arzu_live_2 odoo.models.unlink: User #1 deleted base_import.import records with IDs: [85] 
2025-08-27 12:10:48,492 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:48] "GET /web_enterprise/static/img/background-light.svg HTTP/1.1" 304 - 0 0.000 0.027
2025-08-27 12:10:48,516 38472 INFO arzu_live_2 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [12005, 12006, 12007, 12008, 12009, 12010, 12011, 12012, 12013, 12014, 12015, 12016, 12017, 12018, 12019, 12020, 12021, 12022, 12023, 12024, 12025, 12026, 12027, 12028, 12029, 12030, 12031, 12032, 12033, 12034, 12035, 12036, 12037, 12038, 12039, 12040, 12041, 12042, 12043] 
2025-08-27 12:10:48,546 38472 INFO arzu_live_2 odoo.models.unlink: User #1 deleted bus.presence records with IDs: [162, 169, 173, 174, 175] 
2025-08-27 12:10:48,950 38472 INFO arzu_live_2 odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-08-27 12:10:49,705 38472 INFO arzu_live_2 odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (1.809s). 
2025-08-27 12:10:50,082 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:50] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.034
2025-08-27 12:10:50,472 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:10:50] "GET /web/offline HTTP/1.1" 200 - 13 0.023 0.035
2025-08-27 12:11:19,831 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:19] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.008 0.006
2025-08-27 12:11:28,203 38472 INFO arzu_live_2 odoo.addons.base.models.res_users: Login successful for db:arzu_live_2 login:admin from 127.0.0.1 
2025-08-27 12:11:28,233 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:28] "POST /web/login HTTP/1.1" 303 - 41 0.193 1.643
2025-08-27 12:11:28,682 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 12:11:28,755 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 12:11:28,763 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 12:11:28,771 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 12:11:28,771 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x00000245EBE840E0>, 2, '', ('en_US',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 12:11:30,202 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:30] "GET /web HTTP/1.1" 200 - 77 0.158 1.809
2025-08-27 12:11:31,501 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:31] "GET /web/webclient/translations/0cdad465e96b4dfebec2e06094ec65c8ba6a662c?lang=en_US HTTP/1.1" 200 - 3 0.020 0.017
2025-08-27 12:11:31,515 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:31] "GET /web/webclient/load_menus/2565a381c5964c1190c549e9b6259231c4c0276c262ebfa20c90d2334843b760 HTTP/1.1" 200 - 5 0.019 0.763
2025-08-27 12:11:43,670 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/cfc6a84/web.assets_web.min.css (id:14380) 
2025-08-27 12:11:43,670 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13996] (matching /web/assets/_______/web.assets_web.min.css) because it was replaced with /web/assets/cfc6a84/web.assets_web.min.css 
2025-08-27 12:11:43,748 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:11:43] "GET /web/assets/cfc6a84/web.assets_web.min.css HTTP/1.1" 200 - 14 0.102 13.257
2025-08-27 12:12:31,611 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/3dcf9cd/web.assets_web.min.js (id:14381) 
2025-08-27 12:12:31,618 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Deleting attachments [14377] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/3dcf9cd/web.assets_web.min.js 
2025-08-27 12:12:32,322 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:32] "GET /web/assets/3dcf9cd/web.assets_web.min.js HTTP/1.1" 200 - 13 0.062 61.545
2025-08-27 12:12:33,380 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 4 0.017 0.011
2025-08-27 12:12:33,530 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.035 0.003
2025-08-27 12:12:33,547 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "POST /mail/init_messaging HTTP/1.1" 200 - 53 0.145 0.070
2025-08-27 12:12:33,587 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "GET /web/static/img/odoo-icon-192x192.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:12:33,600 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.005 0.008
2025-08-27 12:12:33,911 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/fe0b8db/bus.websocket_worker_assets.min.js (id:14382) 
2025-08-27 12:12:33,911 38472 INFO arzu_live_2 odoo.addons.base.models.assetsbundle: Deleting attachments [13989] (matching /web/assets/_______/bus.websocket_worker_assets.min.js) because it was replaced with /web/assets/fe0b8db/bus.websocket_worker_assets.min.js 
2025-08-27 12:12:33,962 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:33] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 10 0.024 0.091
2025-08-27 12:12:34,305 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:34] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.008
2025-08-27 12:12:34,329 38472 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-27 12:12:35,359 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:35] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.048
2025-08-27 12:12:35,721 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:35] "GET /web/offline HTTP/1.1" 200 - 2 0.004 0.002
2025-08-27 12:12:57,337 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\d4/d4a1f10fb13b558443ab577b969a83a74754562a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\d4/d4a1f10fb13b558443ab577b969a83a74754562a'
2025-08-27 12:12:57,339 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:57] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 10 0.032 0.035
2025-08-27 12:12:57,605 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:57] "GET /web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2 HTTP/1.1" 304 - 0 0.000 0.004
2025-08-27 12:12:58,537 38472 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:12:58] "GET /web_enterprise/static/img/default_icon_app.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:14:59,351 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\62/627090759f9a66678c28c1bc3c3039b37f40f66e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x00000245EC6FCA40>)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\62/627090759f9a66678c28c1bc3c3039b37f40f66e'
2025-08-27 12:14:59,351 38472 INFO arzu_live_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\arzu_live_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x00000245EC6FCA40>)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\arzu_live_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-08-27 12:14:59,367 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:14:59] "GET /web/session/logout HTTP/1.1" 303 - 8 0.013 0.022
2025-08-27 12:14:59,414 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:14:59] "GET /web HTTP/1.1" 303 - 1 0.000 0.004
2025-08-27 12:14:59,687 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:14:59] "GET /web/login HTTP/1.1" 200 - 4 0.024 0.080
2025-08-27 12:14:59,882 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:14:59] "GET /web/binary/company_logo HTTP/1.1" 200 - 2 0.003 0.131
2025-08-27 12:15:01,425 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:15:01] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.008 0.005
2025-08-27 12:15:01,922 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:15:01] "GET /web/offline HTTP/1.1" 200 - 3 0.005 0.009
2025-08-27 12:15:13,463 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,464 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,471 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,476 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,476 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,476 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,476 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,476 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,480 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,480 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,481 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,482 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,488 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,488 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,491 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,491 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,491 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,496 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,498 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,498 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,501 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,504 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,504 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,508 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,511 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,513 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,515 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,517 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,520 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,521 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,522 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,525 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,526 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,526 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,531 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,533 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,535 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,538 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,539 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,541 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,541 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,543 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,543 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,547 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,547 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,552 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,552 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,554 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,556 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,558 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,559 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,559 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,561 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,561 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,563 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,563 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,563 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,564 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,564 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,564 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,567 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,567 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,568 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,568 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,569 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,569 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,570 38472 INFO arzu_live_2 odoo.sql_db: ConnectionPool(used=3/count=7/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-38472 sslmode=prefer' 
2025-08-27 12:15:13,601 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:15:13] "GET /web/database/manager HTTP/1.1" 200 - 233 2.984 8.853
2025-08-27 12:15:15,832 38472 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:15:15] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.033 0.063
2025-08-27 12:16:11,026 35152 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 12:16:11,026 35152 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 12:16:11,026 35152 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_matrin_live'] 
2025-08-27 12:16:11,026 35152 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 12:16:11,133 35152 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 12:16:12,623 35152 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 12:27:00,193 35152 INFO arzu_live_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 12:27:00,206 35152 INFO arzu_live_2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-27 12:27:00,221 35152 WARNING arzu_live_2 odoo.modules.graph: module pos_analytic_tag: not installable, skipped 
2025-08-27 12:27:00,223 35152 WARNING arzu_live_2 odoo.modules.graph: module cubes_product_access: not installable, skipped 
2025-08-27 12:27:00,223 35152 WARNING arzu_live_2 odoo.modules.graph: module products_to_transfer: not installable, skipped 
2025-08-27 12:27:00,223 35152 WARNING arzu_live_2 odoo.modules.graph: module saudi_pos_receipt: not installable, skipped 
2025-08-27 12:27:00,225 35152 WARNING arzu_live_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 12:27:00,225 35152 WARNING arzu_live_2 odoo.modules.graph: module prevent_details_closing_register: not installable, skipped 
2025-08-27 12:27:00,227 35152 WARNING arzu_live_2 odoo.modules.graph: module cubes_inventory_customization: not installable, skipped 
2025-08-27 12:27:00,227 35152 WARNING arzu_live_2 odoo.modules.graph: module om_data_remove: not installable, skipped 
2025-08-27 12:27:00,227 35152 WARNING arzu_live_2 odoo.modules.graph: module bi_pos_restrict_zero_qty: not installable, skipped 
2025-08-27 12:27:00,229 35152 WARNING arzu_live_2 odoo.modules.graph: module bi_pos_manager_validation: not installable, skipped 
2025-08-27 12:27:00,232 35152 WARNING arzu_live_2 odoo.modules.graph: module pos_access_right_hr: not installable, skipped 
2025-08-27 12:27:00,232 35152 WARNING arzu_live_2 odoo.modules.graph: module pos_zero_quantity_restrict: not installable, skipped 
2025-08-27 12:27:00,232 35152 WARNING arzu_live_2 odoo.modules.graph: module cubes_landed_cost_report: not installable, skipped 
2025-08-27 12:27:00,236 35152 WARNING arzu_live_2 odoo.modules.graph: module elgahad_inventory: not installable, skipped 
2025-08-27 12:27:00,269 35152 WARNING arzu_live_2 odoo.modules.graph: module dvit_warehouse_stock_restrictions: not installable, skipped 
2025-08-27 12:27:00,281 35152 WARNING arzu_live_2 odoo.modules.graph: module product_modifications: not installable, skipped 
2025-08-27 12:27:00,345 35152 WARNING arzu_live_2 odoo.modules.graph: module pos_restrict: not installable, skipped 
2025-08-27 12:27:00,349 35152 WARNING arzu_live_2 odoo.modules.graph: module cubes_skyexpress_integration: not installable, skipped 
2025-08-27 12:27:00,366 35152 WARNING arzu_live_2 odoo.modules.graph: module cubes_internal_transfer_request: not installable, skipped 
2025-08-27 12:27:00,377 35152 WARNING arzu_live_2 odoo.modules.graph: module point_of_sale_logo: not installable, skipped 
2025-08-27 12:27:00,410 35152 WARNING arzu_live_2 odoo.modules.graph: module pos_advance_cash_payment: not installable, skipped 
2025-08-27 12:27:00,418 35152 INFO arzu_live_2 odoo.modules.loading: loading 140 modules... 
2025-08-27 12:27:05,455 35152 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.res_users is not overriding the create method in batch 
2025-08-27 12:27:05,469 35152 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:27:05,474 35152 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:27:05,474 35152 WARNING arzu_live_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 12:27:05,526 35152 INFO arzu_live_2 odoo.modules.loading: 140 modules loaded in 5.11s, 0 queries (+0 extra) 
2025-08-27 12:27:06,023 35152 ERROR arzu_live_2 odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['bi_pos_manager_validation', 'bi_pos_restrict_zero_qty', 'cubes_internal_transfer_request', 'cubes_inventory_customization', 'cubes_landed_cost_report', 'cubes_product_access', 'cubes_skyexpress_integration', 'dvit_warehouse_stock_restrictions', 'elgahad_inventory', 'om_data_remove', 'point_of_sale_logo', 'pos_access_right_hr', 'pos_advance_cash_payment', 'pos_analytic_tag', 'pos_restrict', 'pos_zero_quantity_restrict', 'prevent_details_closing_register', 'product_modifications', 'products_to_transfer', 'saudi_pos_receipt'] 
2025-08-27 12:27:06,025 35152 INFO arzu_live_2 odoo.modules.loading: Modules loaded. 
2025-08-27 12:27:06,051 35152 INFO arzu_live_2 odoo.modules.registry: Registry loaded in 6.004s 
2025-08-27 12:27:06,057 35152 INFO arzu_live_2 odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-08-27 12:27:06,308 35152 INFO arzu_live_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:06] "GET /web?db=matrinlive HTTP/1.1" 302 - 23 0.127 6.139
2025-08-27 12:27:06,440 35152 INFO matrinlive odoo.modules.loading: loading 1 modules... 
2025-08-27 12:27:06,466 35152 INFO matrinlive odoo.modules.loading: 1 modules loaded in 0.03s, 0 queries (+0 extra) 
2025-08-27 12:27:06,473 35152 WARNING matrinlive odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 12:27:06,565 35152 WARNING matrinlive odoo.modules.graph: module warehouse_product_search: not installable, skipped 
2025-08-27 12:27:06,565 35152 WARNING matrinlive odoo.modules.graph: module stock_replenishment_notify: not installable, skipped 
2025-08-27 12:27:06,581 35152 INFO matrinlive odoo.modules.loading: loading 156 modules... 
2025-08-27 12:27:06,597 35152 WARNING matrinlive odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-08-27 12:27:07,385 35152 WARNING matrinlive odoo.api.create: The model odoo.addons.user_warehouse_restriction.models.res_users is not overriding the create method in batch 
2025-08-27 12:27:07,835 35152 INFO matrinlive odoo.modules.loading: 156 modules loaded in 1.25s, 0 queries (+0 extra) 
2025-08-27 12:27:08,418 35152 ERROR matrinlive odoo.modules.loading: Some modules are not loaded, some dependencies or manifest may be missing: ['stock_replenishment_notify', 'warehouse_product_search'] 
2025-08-27 12:27:08,419 35152 INFO matrinlive odoo.modules.loading: Modules loaded. 
2025-08-27 12:27:08,453 35152 INFO matrinlive odoo.modules.registry: Registry loaded in 2.112s 
2025-08-27 12:27:08,453 35152 INFO matrinlive odoo.addons.base.models.ir_http: Generating routing map for key None 
2025-08-27 12:27:08,546 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:08] "GET /web?db=matrinlive HTTP/1.1" 303 - 21 0.176 2.043
2025-08-27 12:27:08,874 35152 WARNING matrinlive odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 12:27:08,919 35152 WARNING matrinlive odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 12:27:09,481 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:09] "GET /web/login?db=matrinlive HTTP/1.1" 200 - 56 0.203 0.718
2025-08-27 12:27:10,993 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:10] "GET /web/assets/9c5193a/web.assets_frontend.min.css HTTP/1.1" 200 - 5 0.026 1.115
2025-08-27 12:27:11,001 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:11] "GET /web/binary/company_logo?dbname=matrinlive HTTP/1.1" 200 - 2 0.012 1.138
2025-08-27 12:27:11,236 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:11] "GET /web/assets/f829da3/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 4 0.005 0.170
2025-08-27 12:27:11,535 35152 INFO matrinlive odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-08-27 12:27:11,667 35152 INFO matrinlive odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-08-27 12:27:11,733 35152 INFO matrinlive odoo.addons.base.models.res_users: GC'd 0 user log entries 
2025-08-27 12:27:11,785 35152 INFO matrinlive odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-08-27 12:27:11,870 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:11] "GET /website/translations/2e319a9d4a87627b32085d1c78e12d3ad7ca4b77 HTTP/1.1" 200 - 12 0.071 0.014
2025-08-27 12:27:11,940 35152 INFO matrinlive odoo.models.unlink: User #1 deleted bus.presence records with IDs: [95] 
2025-08-27 12:27:12,242 35152 INFO matrinlive odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-08-27 12:27:12,754 35152 INFO matrinlive odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (1.219s). 
2025-08-27 12:27:12,885 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:12] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.002
2025-08-27 12:27:22,516 35152 INFO matrinlive odoo.addons.base.models.res_users: Login successful for db:matrinlive login:admin from 127.0.0.1 
2025-08-27 12:27:22,550 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:22] "POST /web/login HTTP/1.1" 303 - 37 0.101 1.413
2025-08-27 12:27:33,940 35152 ERROR matrinlive odoo.addons.base.models.ir_model: Missing model product.search 
2025-08-27 12:27:34,949 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:34] "GET /web HTTP/1.1" 200 - 79 0.260 12.129
2025-08-27 12:27:35,413 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:35] "GET /web/webclient/load_menus/6763dccca977e5e18736a1dafbca8f019446536bfb2adbe152cee1512d21a814 HTTP/1.1" 200 - 1 0.002 0.100
2025-08-27 12:27:35,453 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:35] "GET /web/webclient/translations/eafe270fbe4094783b2d94a4b4cf41b508f470a9?lang=ar_001 HTTP/1.1" 200 - 3 0.019 0.124
2025-08-27 12:27:50,701 35152 WARNING matrinlive odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-08-27 12:27:51,054 35152 INFO matrinlive odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/dfce884/web.assets_web.rtl.min.css (id:2174) 
2025-08-27 12:27:51,063 35152 INFO matrinlive odoo.addons.base.models.assetsbundle: Deleting attachments [2166] (matching /web/assets/_______/web.assets_web.rtl.min.css) because it was replaced with /web/assets/dfce884/web.assets_web.rtl.min.css 
2025-08-27 12:27:51,338 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:27:51] "GET /web/assets/dfce884/web.assets_web.rtl.min.css HTTP/1.1" 200 - 16 0.317 16.048
2025-08-27 12:28:03,099 35152 INFO matrinlive odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/deafe83/web.assets_web.min.js (id:2175) 
2025-08-27 12:28:03,105 35152 INFO matrinlive odoo.addons.base.models.assetsbundle: Deleting attachments [2167] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/deafe83/web.assets_web.min.js 
2025-08-27 12:28:03,585 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:03] "GET /web/assets/deafe83/web.assets_web.min.js HTTP/1.1" 200 - 13 0.116 28.190
2025-08-27 12:28:04,295 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 4 0.016 0.000
2025-08-27 12:28:04,422 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "POST /mail/init_messaging HTTP/1.1" 200 - 55 0.166 0.040
2025-08-27 12:28:04,422 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.018 0.014
2025-08-27 12:28:04,470 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.000 0.000
2025-08-27 12:28:04,526 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.004 0.038
2025-08-27 12:28:04,856 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:04] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.000
2025-08-27 12:28:04,904 35152 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-27 12:28:06,056 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:06] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.004
2025-08-27 12:28:06,415 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:06] "GET /web/offline HTTP/1.1" 200 - 6 0.010 0.006
2025-08-27 12:28:17,469 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:17] "GET /web/image?model=res.users&field=avatar_128&id=7 HTTP/1.1" 200 - 10 0.044 0.039
2025-08-27 12:28:23,699 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:23] "POST /web/action/load HTTP/1.1" 200 - 10 0.037 0.150
2025-08-27 12:28:24,313 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:24] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 120 0.193 0.353
2025-08-27 12:28:24,397 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:24] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 7 0.048 0.003
2025-08-27 12:28:24,667 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:24] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.001 0.006
2025-08-27 12:28:24,985 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:24] "POST /onboarding/sale_quotation HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 12:28:25,288 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:25] "GET /web/static/img/smiling_face.svg HTTP/1.1" 304 - 0 0.000 0.044
2025-08-27 12:28:25,363 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:25] "GET /web/image/res.users/3/avatar_128 HTTP/1.1" 200 - 8 0.010 0.020
2025-08-27 12:28:25,400 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:25] "GET /web/image/res.users/5/avatar_128 HTTP/1.1" 200 - 8 0.001 0.029
2025-08-27 12:28:25,401 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:25] "GET /web/image/res.users/4/avatar_128 HTTP/1.1" 200 - 8 0.012 0.019
2025-08-27 12:28:25,406 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:25] "GET /web/image/res.users/1/avatar_128 HTTP/1.1" 200 - 9 0.028 0.006
2025-08-27 12:28:26,603 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:26] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 20 0.083 0.044
2025-08-27 12:28:27,155 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:27] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 9 0.000 0.045
2025-08-27 12:28:32,012 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:32] "POST /web/dataset/call_kw/sale.order/web_read HTTP/1.1" 200 - 64 0.360 0.050
2025-08-27 12:28:32,454 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:32] "GET /web/bundle/web_editor.backend_assets_wysiwyg?lang=ar_001 HTTP/1.1" 200 - 7 0.000 0.070
2025-08-27 12:28:32,689 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:32] "GET /web/assets/7765696/web_editor.backend_assets_wysiwyg.rtl.min.css HTTP/1.1" 200 - 3 0.000 0.047
2025-08-27 12:28:32,900 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:32] "GET /web/assets/a57178c/web_editor.backend_assets_wysiwyg.min.js HTTP/1.1" 200 - 3 0.007 0.125
2025-08-27 12:28:32,982 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:32] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 8 0.025 0.006
2025-08-27 12:28:33,404 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:33] "GET /web/static/img/openhand.cur HTTP/1.1" 200 - 0 0.000 0.003
2025-08-27 12:28:33,433 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:33] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.087 0.026
2025-08-27 12:28:33,456 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:33] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.073 0.022
2025-08-27 12:28:33,587 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:33] "GET /web/image?field=avatar_128&id=3&model=res.partner&unique=2025-04-09%2013:50:11 HTTP/1.1" 200 - 8 0.016 0.004
2025-08-27 12:28:36,959 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:36] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 15 0.027 0.047
2025-08-27 12:28:37,151 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:37] "POST /onboarding/sale_quotation HTTP/1.1" 200 - 1 0.009 0.003
2025-08-27 12:28:39,953 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:39] "POST /web/dataset/call_kw/sale.order/web_read HTTP/1.1" 200 - 43 0.090 0.060
2025-08-27 12:28:40,451 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:40] "POST /mail/thread/data HTTP/1.1" 200 - 19 0.039 0.044
2025-08-27 12:28:40,484 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:40] "POST /mail/thread/messages HTTP/1.1" 200 - 24 0.033 0.032
2025-08-27 12:28:46,855 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:46] "POST /web/action/load HTTP/1.1" 200 - 8 0.013 0.011
2025-08-27 12:28:47,283 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:47] "POST /web/dataset/call_kw/stock.picking.type/get_views HTTP/1.1" 200 - 36 0.050 0.054
2025-08-27 12:28:47,461 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:47] "POST /web/dataset/call_kw/stock.picking.type/web_search_read HTTP/1.1" 200 - 7 0.000 0.016
2025-08-27 12:28:55,912 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:55] "GET /web/session/logout HTTP/1.1" 303 - 3 0.005 0.013
2025-08-27 12:28:56,205 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:56] "GET /web HTTP/1.1" 303 - 1 0.002 0.004
2025-08-27 12:28:56,719 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:56] "GET /web/login HTTP/1.1" 200 - 4 0.029 0.169
2025-08-27 12:28:56,876 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:56] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.003 0.104
2025-08-27 12:28:58,466 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:58] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.005
2025-08-27 12:28:58,842 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:28:58] "GET /web/offline HTTP/1.1" 200 - 12 0.002 0.027
2025-08-27 12:32:08,843 35152 INFO matrinlive odoo.addons.base.models.res_users: Login successful for db:matrinlive login:admin from 127.0.0.1 
2025-08-27 12:32:08,852 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:08] "POST /web/login HTTP/1.1" 303 - 17 0.016 0.501
2025-08-27 12:32:08,884 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:08] "GET /web HTTP/1.1" 200 - 11 0.017 0.008
2025-08-27 12:32:09,204 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.000 0.005
2025-08-27 12:32:09,568 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 2 0.000 0.023
2025-08-27 12:32:09,603 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "GET /web/image?model=res.users&field=avatar_128&id=7 HTTP/1.1" 304 - 9 0.025 0.028
2025-08-27 12:32:09,603 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.025
2025-08-27 12:32:09,603 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.047 0.039
2025-08-27 12:32:09,951 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.016 0.000
2025-08-27 12:32:09,995 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:09] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.037 0.022
2025-08-27 12:32:11,215 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:11] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 12:32:11,564 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:11] "GET /web/offline HTTP/1.1" 200 - 2 0.000 0.009
2025-08-27 12:32:15,328 35152 ERROR matrinlive odoo.addons.base.models.ir_model: Missing model product.search 
2025-08-27 12:32:15,385 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "GET /web?debug=1 HTTP/1.1" 200 - 31 0.047 0.069
2025-08-27 12:32:15,584 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "GET /web/webclient/load_menus/81d9b5c9993edb2ac18480c7c68df107f90185392f4301780831f9dd2c8354eb HTTP/1.1" 200 - 1 0.002 0.013
2025-08-27 12:32:15,840 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 2 0.007 0.012
2025-08-27 12:32:15,854 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.012 0.021
2025-08-27 12:32:15,894 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.060 0.027
2025-08-27 12:32:15,950 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.000 0.015
2025-08-27 12:32:15,984 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:15] "GET /web/image?model=res.users&field=avatar_128&id=7 HTTP/1.1" 304 - 9 0.014 0.002
2025-08-27 12:32:16,220 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:16] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.000 0.003
2025-08-27 12:32:16,574 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:16] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.012
2025-08-27 12:32:17,791 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:17] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.007
2025-08-27 12:32:19,288 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "GET /web?debug=1 HTTP/1.1" 200 - 11 0.010 0.030
2025-08-27 12:32:19,602 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.000 0.051
2025-08-27 12:32:19,830 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 2 0.000 0.014
2025-08-27 12:32:19,835 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "POST /mail/load_message_failures HTTP/1.1" 200 - 3 0.013 0.004
2025-08-27 12:32:19,850 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.010 0.007
2025-08-27 12:32:19,934 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 3 0.014 0.002
2025-08-27 12:32:19,950 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:19] "GET /web/image?model=res.users&field=avatar_128&id=7 HTTP/1.1" 304 - 9 0.011 0.021
2025-08-27 12:32:20,259 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:20] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.008
2025-08-27 12:32:21,686 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:21] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 12:32:21,947 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:21] "GET /web/session/logout HTTP/1.1" 303 - 3 0.000 0.012
2025-08-27 12:32:22,209 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:22] "GET /web HTTP/1.1" 303 - 1 0.000 0.018
2025-08-27 12:32:22,357 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:22] "GET /web/login HTTP/1.1" 200 - 4 0.021 0.073
2025-08-27 12:32:22,540 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:22] "GET /web/binary/company_logo HTTP/1.1" 304 - 2 0.000 0.007
2025-08-27 12:32:24,034 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:24] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.003
2025-08-27 12:32:24,369 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:32:24] "GET /web/offline HTTP/1.1" 200 - 3 0.000 0.012
2025-08-27 12:33:22,269 35152 INFO matrinlive odoo.addons.base.models.res_users: Login failed for db:matrinlive login:admin4 from 127.0.0.1 
2025-08-27 12:33:22,280 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:22] "POST /web/login HTTP/1.1" 200 - 5 0.017 0.069
2025-08-27 12:33:22,507 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:22] "GET /web/binary/company_logo?dbname=matrinlive HTTP/1.1" 304 - 2 0.000 0.007
2025-08-27 12:33:24,268 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:24] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.000
2025-08-27 12:33:41,887 35152 INFO matrinlive odoo.addons.base.models.res_users: Login failed for db:matrinlive login:admin4 from 127.0.0.1 
2025-08-27 12:33:41,898 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:41] "POST /web/login HTTP/1.1" 200 - 5 0.009 0.077
2025-08-27 12:33:42,138 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:42] "GET /web/binary/company_logo?dbname=matrinlive HTTP/1.1" 304 - 2 0.002 0.010
2025-08-27 12:33:43,860 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:33:43] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.000 0.006
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,870 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,876 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,884 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,887 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,892 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,900 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,900 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,903 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,905 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,907 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,909 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,911 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,915 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,917 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,919 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,919 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,922 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,922 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,925 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,925 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,928 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,930 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,930 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,934 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,934 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,934 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,934 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,939 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,939 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,941 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,941 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,941 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,945 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,947 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,947 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,949 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,950 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,950 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,950 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,954 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,955 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,957 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,959 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,959 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,959 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,963 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,963 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,963 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,965 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,967 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,969 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,969 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,970 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,971 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,971 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,974 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,974 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:38,974 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=7/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:34:39,013 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:34:39] "GET /web/database/manager HTTP/1.1" 200 - 233 3.323 6.699
2025-08-27 12:34:40,624 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:34:40] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.017 0.002
2025-08-27 12:36:37,459 35152 INFO matrinlive odoo.service.db: RESTORING DB: martinlion_2 
2025-08-27 12:37:42,503 35152 INFO martinlion_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 12:37:42,506 35152 INFO martinlion_2 odoo.modules.loading: 1 modules loaded in 0.00s, 0 queries (+0 extra) 
2025-08-27 12:37:42,522 35152 INFO martinlion_2 odoo.modules.loading: loading 204 modules... 
2025-08-27 12:37:46,700 35152 INFO martinlion_2 odoo.modules.loading: 204 modules loaded in 4.18s, 0 queries (+0 extra) 
2025-08-27 12:37:46,961 35152 INFO martinlion_2 odoo.modules.loading: Modules loaded. 
2025-08-27 12:37:46,981 35152 INFO martinlion_2 odoo.modules.registry: Registry loaded in 4.506s 
2025-08-27 12:37:47,072 35152 INFO martinlion_2 odoo.service.db: RESTORE DB: martinlion_2 
2025-08-27 12:37:47,082 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:37:47] "POST /web/database/restore HTTP/1.1" 303 - 44 0.797 69.446
2025-08-27 12:37:58,635 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,635 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,635 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,635 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,635 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,639 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,639 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,639 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,642 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,648 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,648 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,648 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,648 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,648 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,651 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,663 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,667 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,667 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,667 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=64/max=64): Closed 0 connections  
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=63/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=62/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=61/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_67 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=60/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=elit_live_8 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=59/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,669 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=58/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_7 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,676 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=57/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_4 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,676 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=56/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,676 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=55/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=gradi_live_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,679 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=54/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,679 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=53/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,684 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=52/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel16_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,684 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=51/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_15 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,688 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=50/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,690 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=49/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_4 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,690 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=48/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=hotel_16_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,690 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=47/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_ERP host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,695 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=46/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_benghazi host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,696 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=45/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,696 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=44/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,700 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=43/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=kayan_data_main host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,701 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=42/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptous_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,704 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=41/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_tesing host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,704 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=40/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=leptus_testing host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,704 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=39/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moaslive_6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,704 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=38/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,711 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=37/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass2025 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,713 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=36/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,715 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=35/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,717 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=34/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass6 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,718 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=33/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moassLive25_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,722 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=32/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,722 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=31/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,722 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=30/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_5 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,722 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=29/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_7 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,728 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=28/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=moass_live_latest host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,731 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=27/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo16 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,731 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=26/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo17 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,735 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=25/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,737 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=24/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=odoo18_cubes host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,739 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=23/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_1 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,740 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=22/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=pixel_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,742 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=21/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,744 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=20/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=rafeda18live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,744 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=19/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=t18_8 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,744 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=18/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,744 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=17/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_2 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,744 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=16/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcf_live_3 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,751 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=15/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=tcg_live host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,751 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=14/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=ted host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,755 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=13/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test15 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,758 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=12/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test151 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,758 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=11/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,760 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=10/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test1818 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,760 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=9/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,760 35152 INFO matrinlive odoo.sql_db: ConnectionPool(used=3/count=8/max=64): Closed 1 connections to 'user=openpg password=xxx dbname=test_pos_18 host=localhost port=5432 application_name=odoo-35152 sslmode=prefer' 
2025-08-27 12:37:58,801 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:37:58] "GET /web/database/manager HTTP/1.1" 200 - 235 3.166 8.239
2025-08-27 12:38:00,858 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:38:00] "GET /web/service-worker.js HTTP/1.1" 200 - 2 0.007 0.002
2025-08-27 12:38:13,234 35152 INFO martinlion_2 odoo.addons.base.models.ir_cron: Starting job `Base: Auto-vacuum internal data`. 
2025-08-27 12:38:13,302 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: filestore gc 0 checked, 0 removed 
2025-08-27 12:38:13,405 35152 INFO martinlion_2 odoo.addons.base.models.res_users: GC'd 1 user log entries 
2025-08-27 12:38:13,443 35152 INFO martinlion_2 odoo.addons.auth_totp.models.auth_totp: GC'd 0 totp devices entries 
2025-08-27 12:38:13,462 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted bus.bus records with IDs: [23450, 23451, 23452, 23453, 23454, 23455, 23456, 23457, 23458, 23459, 23460, 23461, 23462, 23463] 
2025-08-27 12:38:13,480 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted bus.presence records with IDs: [208, 209, 210] 
2025-08-27 12:38:13,664 35152 INFO martinlion_2 odoo.addons.sms.models.sms_sms: GC'd 0 sms marked for deletion 
2025-08-27 12:38:13,758 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted account.payment.register records with IDs: [267, 268] 
2025-08-27 12:38:13,872 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted stock.return.picking.line records with IDs: [172] 
2025-08-27 12:38:13,886 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted stock.return.picking records with IDs: [63] 
2025-08-27 12:38:13,997 35152 INFO martinlion_2 odoo.models.unlink: User #1 deleted sale.advance.payment.inv records with IDs: [435, 436, 437] 
2025-08-27 12:38:14,054 35152 INFO martinlion_2 odoo.addons.base.models.ir_cron: Job done: `Base: Auto-vacuum internal data` (0.820s). 
2025-08-27 12:38:14,061 35152 INFO martinlion_2 odoo.addons.base.models.ir_cron: Starting job `Publisher: Update Notification`. 
2025-08-27 12:38:15,255 35152 INFO martinlion_2 odoo.addons.base.models.ir_cron: Job done: `Publisher: Update Notification` (1.194s). 
2025-08-27 12:38:15,255 35152 INFO martinlion_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['default'] 
2025-08-27 12:39:32,747 35152 INFO matrinlive werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:32] "GET /web?db=martinlion_2 HTTP/1.1" 302 - 1 0.000 0.010
2025-08-27 12:39:32,869 35152 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 12:39:33,075 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:33] "GET /web?db=martinlion_2 HTTP/1.1" 303 - 8 0.040 0.204
2025-08-27 12:39:33,152 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\23/23fe89f51c8121f053a697a3dd79106f25aeeb52 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('res.lang', <function Lang.get_available at 0x000001684621F920>, (1,))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\23/23fe89f51c8121f053a697a3dd79106f25aeeb52'
2025-08-27 12:39:33,980 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:33] "GET /web/login?db=martinlion_2 HTTP/1.1" 200 - 177 0.295 0.600
2025-08-27 12:39:34,388 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\e7/e73b51bece9c698865e1d13f032f5df83110e248 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\e7/e73b51bece9c698865e1d13f032f5df83110e248'
2025-08-27 12:39:34,436 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\7d/7d491f0c640169793527b37932c246f99e4a1f5b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\7d/7d491f0c640169793527b37932c246f99e4a1f5b'
2025-08-27 12:39:39,567 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/f7abd5b/web.assets_frontend.min.css (id:2709) 
2025-08-27 12:39:39,570 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2685, 2684] (matching /web/assets/1/_______/web.assets_frontend.min.css) because it was replaced with /web/assets/1/f7abd5b/web.assets_frontend.min.css 
2025-08-27 12:39:39,745 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:39] "GET /web/assets/1/f7abd5b/web.assets_frontend.min.css HTTP/1.1" 200 - 18 0.222 5.216
2025-08-27 12:39:40,679 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:40] "GET /web/static/fonts/twitter_x_only.woff HTTP/1.1" 304 - 0 0.000 0.002
2025-08-27 12:39:41,026 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:41] "GET /web/static/img/odoo_logo_tiny.png HTTP/1.1" 200 - 0 0.000 0.008
2025-08-27 12:39:41,037 35152 ERROR martinlion_2 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2206, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1782, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1809, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1926, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\website\models\ir_http.py", line 235, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 757, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\web\controllers\binary.py", line 171, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\odoo17\server\enterprise17\documents\models\ir_binary.py", line 13, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 509, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo17\\sessions\\filestore\\martinlion_2/66/6693cbc909d2466aac107015f597a55ae10ea3ac'
2025-08-27 12:39:41,184 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:41] "GET /web/image/website/1/logo/My%20Website?unique=7305c3e HTTP/1.1" 500 - 5 0.012 0.155
2025-08-27 12:39:41,284 35152 ERROR martinlion_2 odoo.http: Exception during request handling. 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\http.py", line 2206, in __call__
    response = request._serve_db()
               ^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1782, in _serve_db
    return service_model.retrying(self._serve_ir_http, self.env)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\service\model.py", line 133, in retrying
    result = func()
             ^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1809, in _serve_ir_http
    response = self.dispatcher.dispatch(rule.endpoint, args)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 1926, in dispatch
    return self.request.registry['ir.http']._dispatch(endpoint)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\website\models\ir_http.py", line 235, in _dispatch
    response = super()._dispatch(endpoint)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_http.py", line 221, in _dispatch
    result = endpoint(**request.params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 757, in route_wrapper
    result = endpoint(self, *args, **params_ok)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\web\controllers\binary.py", line 171, in content_image
    stream = request.env['ir.binary']._get_image_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 202, in _get_image_stream_from
    stream = self._get_stream_from(
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 126, in _get_stream_from
    stream = self._record_to_stream(record, field_name)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\website\models\ir_binary.py", line 41, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\odoo17\server\enterprise17\documents\models\ir_binary.py", line 13, in _record_to_stream
    return super()._record_to_stream(record, field_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\addons\base\models\ir_binary.py", line 85, in _record_to_stream
    return Stream.from_attachment(field_attachment)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\http.py", line 509, in from_attachment
    stat = os.stat(self.path)
           ^^^^^^^^^^^^^^^^^^
FileNotFoundError: [WinError 3] The system cannot find the path specified: 'c:\\odoo17\\sessions\\filestore\\martinlion_2/d0/d09086a0794cf3070f12e742f27126254b4e2b5a'
2025-08-27 12:39:41,286 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:41] "GET /web/image/website/1/favicon?unique=7305c3e HTTP/1.1" 500 - 4 0.005 0.014
2025-08-27 12:39:42,788 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:42] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.003 0.000
2025-08-27 12:39:48,035 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/1/4f909d3/web.assets_frontend_lazy.min.js (id:2710) 
2025-08-27 12:39:48,048 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2706] (matching /web/assets/1/_______/web.assets_frontend_lazy.min.js) because it was replaced with /web/assets/1/4f909d3/web.assets_frontend_lazy.min.js 
2025-08-27 12:39:48,302 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:48] "GET /web/assets/1/4f909d3/web.assets_frontend_lazy.min.js HTTP/1.1" 200 - 11 0.025 6.735
2025-08-27 12:39:48,711 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:48] "GET /website/translations/2397445fcdd816562d8e5ba9e625299b8f0fcb6d?lang=en_US HTTP/1.1" 200 - 3 0.000 0.016
2025-08-27 12:39:49,741 35152 INFO martinlion_2 odoo.addons.base.models.res_users: Login successful for db:martinlion_2 login:admin from 127.0.0.1 
2025-08-27 12:39:49,779 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:49] "POST /web/login HTTP/1.1" 303 - 44 0.087 0.755
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 12:39:52,654 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 12:39:52,662 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 12:39:52,668 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 12:39:52,668 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 12:39:52,668 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 12:39:53,129 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:53] "GET /web HTTP/1.1" 200 - 100 0.207 3.131
2025-08-27 12:39:53,579 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:53] "GET /web/webclient/translations/e360f26eff7920050f864329329125020bc0384d?lang=ar_001 HTTP/1.1" 200 - 3 0.001 0.118
2025-08-27 12:39:53,617 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:39:53] "GET /web/webclient/load_menus/4a0947f3a18d3a945e6cdf1e74d7f3f4bed8b9e42b55de6461f22627b5dbb57b HTTP/1.1" 200 - 5 0.076 0.082
2025-08-27 12:40:00,731 35152 WARNING martinlion_2 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-08-27 12:40:00,879 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/56dfc33/web.assets_web.rtl.min.css (id:2711) 
2025-08-27 12:40:00,880 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2682] (matching /web/assets/_______/web.assets_web.rtl.min.css) because it was replaced with /web/assets/56dfc33/web.assets_web.rtl.min.css 
2025-08-27 12:40:01,098 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:01] "GET /web/assets/56dfc33/web.assets_web.rtl.min.css HTTP/1.1" 200 - 31 0.277 7.365
2025-08-27 12:40:07,058 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/c744f42/web.assets_web.min.js (id:2712) 
2025-08-27 12:40:07,064 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2707] (matching /web/assets/_______/web.assets_web.min.js) because it was replaced with /web/assets/c744f42/web.assets_web.min.js 
2025-08-27 12:40:07,651 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:07] "GET /web/assets/c744f42/web.assets_web.min.js HTTP/1.1" 200 - 30 0.179 14.015
2025-08-27 12:40:07,948 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/fe0b8db/bus.websocket_worker_assets.min.js (id:2713) 
2025-08-27 12:40:07,950 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2169] (matching /web/assets/_______/bus.websocket_worker_assets.min.js) because it was replaced with /web/assets/fe0b8db/bus.websocket_worker_assets.min.js 
2025-08-27 12:40:07,980 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:07] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 10 0.015 0.041
2025-08-27 12:40:08,355 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.002 0.024
2025-08-27 12:40:08,377 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 22 0.044 0.042
2025-08-27 12:40:08,380 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.050 0.009
2025-08-27 12:40:08,434 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.132 0.051
2025-08-27 12:40:08,698 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "POST /mail/load_message_failures HTTP/1.1" 200 - 17 0.037 0.024
2025-08-27 12:40:08,781 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:40:08,784 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:08] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 200 - 10 0.016 0.006
2025-08-27 12:40:10,636 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:10] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 12:40:10,912 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:10] "GET /web/offline HTTP/1.1" 200 - 6 0.014 0.014
2025-08-27 12:40:19,419 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:19] "POST /web/action/load HTTP/1.1" 200 - 10 0.016 0.034
2025-08-27 12:40:19,984 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:19] "POST /web/dataset/call_kw/sale.order/get_views HTTP/1.1" 200 - 129 0.083 0.151
2025-08-27 12:40:20,051 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:20] "POST /web/dataset/call_kw/sale.order/web_read_group HTTP/1.1" 200 - 10 0.029 0.006
2025-08-27 12:40:20,303 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:20] "POST /web/dataset/call_kw/res.users/has_group HTTP/1.1" 200 - 2 0.000 0.000
2025-08-27 12:40:20,343 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:20] "POST /onboarding/sale_quotation HTTP/1.1" 200 - 6 0.013 0.011
2025-08-27 12:40:22,518 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:22] "POST /web/dataset/call_kw/sale.order/web_search_read HTTP/1.1" 200 - 18 0.060 0.044
2025-08-27 12:40:22,780 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:40:22,783 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:22] "GET /web/image/res.users/2/avatar_128 HTTP/1.1" 200 - 9 0.016 0.005
2025-08-27 12:40:24,752 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:24] "POST /web/dataset/call_kw/sale.order/web_read HTTP/1.1" 200 - 84 0.293 0.106
2025-08-27 12:40:24,856 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:24] "GET /web/bundle/web_editor.backend_assets_wysiwyg?lang=ar_001 HTTP/1.1" 200 - 7 0.007 0.023
2025-08-27 12:40:25,784 35152 WARNING martinlion_2 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-08-27 12:40:25,819 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/2568c60/web_editor.backend_assets_wysiwyg.rtl.min.css (id:2714) 
2025-08-27 12:40:25,819 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2664] (matching /web/assets/_______/web_editor.backend_assets_wysiwyg.rtl.min.css) because it was replaced with /web/assets/2568c60/web_editor.backend_assets_wysiwyg.rtl.min.css 
2025-08-27 12:40:25,854 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:25] "GET /web/assets/2568c60/web_editor.backend_assets_wysiwyg.rtl.min.css HTTP/1.1" 200 - 16 0.021 0.654
2025-08-27 12:40:25,923 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:25] "POST /web/dataset/call_kw/ir.ui.view/render_public_asset HTTP/1.1" 200 - 7 0.011 0.014
2025-08-27 12:40:26,394 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:26] "POST /mail/thread/data HTTP/1.1" 200 - 32 0.040 0.052
2025-08-27 12:40:26,435 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:26] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.072 0.032
2025-08-27 12:40:26,597 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:40:26,603 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:26] "GET /web/image?field=avatar_128&id=3&model=res.partner&unique=2025-08-24%2013:49:46 HTTP/1.1" 200 - 8 0.009 0.026
2025-08-27 12:40:27,456 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:27] "POST /web/dataset/call_button HTTP/1.1" 200 - 16 0.014 0.027
2025-08-27 12:40:27,982 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:27] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 95 0.044 0.170
2025-08-27 12:40:28,188 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:28] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 38 0.053 0.105
2025-08-27 12:40:28,302 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:28] "POST /web/dataset/call_kw/stock.picking.type/check_access_rights HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 12:40:28,596 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:28] "POST /mail/thread/data HTTP/1.1" 200 - 22 0.035 0.029
2025-08-27 12:40:28,714 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:28] "POST /mail/thread/messages HTTP/1.1" 200 - 21 0.045 0.017
2025-08-27 12:40:28,785 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\36/36ba5ef36880a809f9bdddfe35f12fcf34406a6e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\36/36ba5ef36880a809f9bdddfe35f12fcf34406a6e'
2025-08-27 12:40:28,801 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:28] "GET /web/image?field=avatar_128&id=2&model=res.partner&unique=2025-04-21%2019:18:18 HTTP/1.1" 200 - 7 0.004 0.032
2025-08-27 12:40:33,835 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:33] "POST /web/action/load HTTP/1.1" 200 - 9 0.019 0.012
2025-08-27 12:40:34,069 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:34] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.000 0.009
2025-08-27 12:40:34,175 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:34] "GET /web/static/img/spin.svg HTTP/1.1" 304 - 0 0.000 0.023
2025-08-27 12:40:37,121 35152 WARNING martinlion_2 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-08-27 12:40:37,136 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/059d187/web.report_assets_pdf.rtl.min.css (id:2715) 
2025-08-27 12:40:37,147 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2494] (matching /web/assets/_______/web.report_assets_pdf.rtl.min.css) because it was replaced with /web/assets/059d187/web.report_assets_pdf.rtl.min.css 
2025-08-27 12:40:37,191 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:37] "GET /web/assets/059d187/web.report_assets_pdf.rtl.min.css HTTP/1.1" 200 - 11 0.042 0.180
2025-08-27 12:40:37,385 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a0/a06736cd64a3356081d52776fdebcd436b850e0a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a0/a06736cd64a3356081d52776fdebcd436b850e0a'
2025-08-27 12:40:42,060 35152 WARNING martinlion_2 odoo.addons.base.models.assetsbundle: You need https://rtlcss.com/ to convert css file to right to left compatiblity. Use: npm install -g rtlcss 
2025-08-27 12:40:42,216 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Generating a new asset bundle attachment /web/assets/d324596/web.report_assets_common.rtl.min.css (id:2716) 
2025-08-27 12:40:42,220 35152 INFO martinlion_2 odoo.addons.base.models.assetsbundle: Deleting attachments [2679] (matching /web/assets/_______/web.report_assets_common.rtl.min.css) because it was replaced with /web/assets/d324596/web.report_assets_common.rtl.min.css 
2025-08-27 12:40:42,561 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:42] "GET /web/assets/d324596/web.report_assets_common.rtl.min.css HTTP/1.1" 200 - 19 0.391 5.200
2025-08-27 12:40:42,606 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:42] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.001
2025-08-27 12:40:44,714 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:44] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.008
2025-08-27 12:40:44,719 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:44] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.013
2025-08-27 12:40:44,719 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:44] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.013
2025-08-27 12:40:44,723 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:44] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.018
2025-08-27 12:40:47,635 35152 INFO martinlion_2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: stock.picking, records [3155]. 
2025-08-27 12:40:47,643 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:47] "POST /report/download HTTP/1.1" 200 - 57 0.102 13.372
2025-08-27 12:40:47,664 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:47] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.004 0.008
2025-08-27 12:40:48,102 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:48] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 27 0.046 0.070
2025-08-27 12:40:48,463 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:40:48] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.016 0.015
2025-08-27 12:56:57,889 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:57] "POST /web/action/load HTTP/1.1" 200 - 9 0.010 0.046
2025-08-27 12:56:58,020 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.041 0.128
2025-08-27 12:56:58,254 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 9 0.021 0.026
2025-08-27 12:56:58,451 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 11 0.066 0.154
2025-08-27 12:56:58,455 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.044 0.080
2025-08-27 12:56:58,483 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.028 0.100
2025-08-27 12:56:58,884 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:58] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 27 0.043 0.047
2025-08-27 12:56:59,909 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:56:59] "POST /web/action/load HTTP/1.1" 200 - 9 0.031 0.007
2025-08-27 12:57:00,389 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 46 0.041 0.109
2025-08-27 12:57:00,506 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.007 0.002
2025-08-27 12:57:00,737 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.014 0.003
2025-08-27 12:57:00,782 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 45 0.044 0.065
2025-08-27 12:57:00,838 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "POST /web/dataset/call_kw/ir.module.module/web_read_group HTTP/1.1" 200 - 3 0.016 0.101
2025-08-27 12:57:00,873 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:00] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.003 0.021
2025-08-27 12:57:01,357 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.005 0.191
2025-08-27 12:57:01,357 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.013 0.183
2025-08-27 12:57:01,357 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.024 0.172
2025-08-27 12:57:01,388 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.015 0.196
2025-08-27 12:57:01,418 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.017 0.211
2025-08-27 12:57:01,426 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.009 0.243
2025-08-27 12:57:01,724 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.016 0.032
2025-08-27 12:57:01,856 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:01] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.004 0.176
2025-08-27 12:57:02,248 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /pos_restrict_product_stock/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.009
2025-08-27 12:57:02,438 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /payment_razorpay/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:02,438 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /account_restrict_journal/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:02,468 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /sale_order_automation/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:02,468 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 12:57:02,477 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 12:57:02,477 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /deltatech_stock_negative/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.038
2025-08-27 12:57:02,477 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 12:57:02,485 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 12:57:02,485 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 12:57:02,488 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 12:57:02,489 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 12:57:02,491 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 12:57:02,491 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 12:57:02,493 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 12:57:02,494 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 12:57:02,496 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 12:57:02,496 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 12:57:02,499 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 12:57:02,499 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 12:57:02,499 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 12:57:02,499 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-08-27 12:57:02,504 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 12:57:02,504 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 12:57:02,545 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /web?debug=1 HTTP/1.1" 200 - 37 0.072 0.234
2025-08-27 12:57:02,569 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /account/static/description/l10n.png HTTP/1.1" 200 - 0 0.000 0.015
2025-08-27 12:57:02,778 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /web/webclient/load_menus/7f25338a15bb78786d5423d6abcae48bc3e4e32cf74eb71a4fe58411819eb993 HTTP/1.1" 200 - 4 0.009 0.016
2025-08-27 12:57:02,819 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.029 0.010
2025-08-27 12:57:02,857 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 9 0.013 0.024
2025-08-27 12:57:02,941 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:57:02,941 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:02] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.027 0.049
2025-08-27 12:57:03,029 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:03] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.147 0.102
2025-08-27 12:57:03,122 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:03] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.024 0.048
2025-08-27 12:57:04,890 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:04] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.002 0.001
2025-08-27 12:57:05,787 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:05] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.008
2025-08-27 12:57:06,044 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/get_views HTTP/1.1" 200 - 4 0.003 0.026
2025-08-27 12:57:06,142 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 1 0.000 0.008
2025-08-27 12:57:06,388 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_read_group HTTP/1.1" 200 - 3 0.000 0.028
2025-08-27 12:57:06,420 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.011 0.018
2025-08-27 12:57:06,471 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 45 0.073 0.038
2025-08-27 12:57:06,756 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.008 0.043
2025-08-27 12:57:06,783 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.029
2025-08-27 12:57:06,806 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.001 0.088
2025-08-27 12:57:06,824 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.021 0.097
2025-08-27 12:57:06,831 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.000 0.046
2025-08-27 12:57:06,837 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:06] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.035 0.096
2025-08-27 12:57:07,085 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.004 0.016
2025-08-27 12:57:07,194 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.002 0.098
2025-08-27 12:57:07,689 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "GET /sh_journal_restrict/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:07,689 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "GET /account/static/description/l10n.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:07,711 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "GET /base/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.022
2025-08-27 12:57:07,711 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "GET /sale_management/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-08-27 12:57:07,720 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:07] "GET /pos_restaurant/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-08-27 12:57:08,046 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /account/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-08-27 12:57:08,222 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /crm/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.176
2025-08-27 12:57:08,242 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /website/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.155
2025-08-27 12:57:08,287 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /stock/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.045
2025-08-27 12:57:08,289 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "POST /web/action/load HTTP/1.1" 200 - 8 0.173 0.077
2025-08-27 12:57:08,605 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /account_accountant/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:08,655 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /point_of_sale/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.020
2025-08-27 12:57:08,655 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /purchase/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.030
2025-08-27 12:57:08,699 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /project/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.057
2025-08-27 12:57:08,738 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "POST /web/dataset/call_kw/base.module.update/get_views HTTP/1.1" 200 - 9 0.038 0.095
2025-08-27 12:57:08,966 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "GET /delivery_fedex_rest/static/description/icon.png HTTP/1.1" 404 - 132 0.259 1.017
2025-08-27 12:57:08,973 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:08] "POST /web/dataset/call_kw/base.module.update/onchange HTTP/1.1" 200 - 2 0.002 0.017
2025-08-27 12:57:09,018 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /website_sale/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.004
2025-08-27 12:57:09,062 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /mass_mailing/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-08-27 12:57:09,066 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /mrp/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.010
2025-08-27 12:57:09,071 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /timesheet_grid/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-08-27 12:57:09,277 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /hr_expense/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:09,288 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /web_studio/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-08-27 12:57:09,344 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /documents/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:09,393 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /hr_holidays/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-08-27 12:57:09,393 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /hr_recruitment/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-08-27 12:57:09,401 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /hr/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.008
2025-08-27 12:57:09,606 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /data_recycle/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-08-27 12:57:09,615 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /delivery_starshipit/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.009
2025-08-27 12:57:09,654 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /delivery_ups_rest/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:09,710 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /frontdesk/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.006
2025-08-27 12:57:09,717 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /knowledge/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.012
2025-08-27 12:57:09,719 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /maintenance/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.008
2025-08-27 12:57:09,929 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "GET /room/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.007
2025-08-27 12:57:09,944 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:09] "POST /web/dataset/call_kw/base.module.update/web_save HTTP/1.1" 200 - 5 0.010 0.012
2025-08-27 12:57:09,988 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user admin #2 via 127.0.0.1 
2025-08-27 12:57:10,055 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /whatsapp/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.015
2025-08-27 12:57:10,055 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /sign/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.015
2025-08-27 12:57:10,055 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /helpdesk/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.015
2025-08-27 12:57:10,248 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /sale_subscription/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:10,271 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /quality_control/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.024
2025-08-27 12:57:10,375 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /website_slides/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.007
2025-08-27 12:57:10,390 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /planning/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-08-27 12:57:10,404 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /hide_cost_price/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.013
2025-08-27 12:57:10,580 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /hide_menu_user/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-08-27 12:57:10,590 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /om_mass_confirm_cancel/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-08-27 12:57:10,710 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /user_warehouse_restriction/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.010
2025-08-27 12:57:10,720 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /pos_hide_cost_price_and_margin/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.020
2025-08-27 12:57:10,898 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:10] "GET /delivery_usps_rest/static/description/icon.png HTTP/1.1" 404 - 20 0.061 0.120
2025-08-27 12:57:13,917 35152 INFO martinlion_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['templates'] 
2025-08-27 12:57:13,928 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:13] "POST /web/dataset/call_button HTTP/1.1" 200 - 2663 2.427 1.513
2025-08-27 12:57:19,976 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:19] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 45 0.079 0.042
2025-08-27 12:57:20,037 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 4 0.004 0.177
2025-08-27 12:57:20,604 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /website_event/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.017
2025-08-27 12:57:20,641 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /contacts/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.020
2025-08-27 12:57:20,641 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /mail/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.041
2025-08-27 12:57:20,655 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /mrp_plm/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.014
2025-08-27 12:57:20,839 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "POST /web/dataset/call_kw/ir.module.module/search_panel_select_range HTTP/1.1" 200 - 45 0.199 0.056
2025-08-27 12:57:20,989 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /calendar/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.018
2025-08-27 12:57:20,989 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /industry_fsm/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.007
2025-08-27 12:57:20,994 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:20] "GET /sale_renting/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.022
2025-08-27 12:57:21,037 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "POST /web/dataset/call_kw/ir.module.module/web_search_read HTTP/1.1" 200 - 3 0.042 0.045
2025-08-27 12:57:21,171 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /social/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.001
2025-08-27 12:57:21,289 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /delivery_dhl_rest/static/description/icon.png HTTP/1.1" 404 - 117 0.211 0.907
2025-08-27 12:57:21,308 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /hr_appraisal/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-08-27 12:57:21,321 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /fleet/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:21,328 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /approvals/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.008
2025-08-27 12:57:21,363 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /marketing_automation/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.003
2025-08-27 12:57:21,509 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /account_consolidation/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.004
2025-08-27 12:57:21,601 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /im_livechat/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:21,628 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /appointment/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:21,639 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /survey/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.003
2025-08-27 12:57:21,644 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /repair/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.002
2025-08-27 12:57:21,674 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /hr_referral/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:21,828 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /hr_attendance/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.007
2025-08-27 12:57:21,920 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /mass_mailing_sms/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.005
2025-08-27 12:57:21,956 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /iot/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:21,960 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /project_todo/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:21,963 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /stock_barcode/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.011
2025-08-27 12:57:21,991 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:21] "GET /hr_skills/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-08-27 12:57:22,153 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /voip/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:22,238 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /hr_payroll/static/description/icon.png HTTP/1.1" 304 - 0 0.000 0.000
2025-08-27 12:57:22,266 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /lunch/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:22,277 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_easypost/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:22,277 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /website_hr_recruitment/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:22,306 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_sendcloud/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-08-27 12:57:22,477 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_shiprocket/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.005
2025-08-27 12:57:22,576 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /sale_amazon/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:22,576 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /sale_ebay/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:22,595 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_bpost/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.008
2025-08-27 12:57:22,595 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /hr_contract/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.002
2025-08-27 12:57:22,739 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_fedex_rest/static/description/icon.png HTTP/1.1" 404 - 19 0.015 0.103
2025-08-27 12:57:22,790 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_dhl/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 12:57:22,908 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_fedex/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.006
2025-08-27 12:57:22,918 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_immediate_upgrade on ['Sale Order and Delivery Operations'] to user admin #2 via 127.0.0.1 
2025-08-27 12:57:22,922 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_upgrade on ['Sale Order and Delivery Operations'] to user admin #2 via 127.0.0.1 
2025-08-27 12:57:22,922 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on ['Sale Order and Delivery Operations'] to user admin #2 via 127.0.0.1 
2025-08-27 12:57:22,925 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:22] "GET /delivery_usps/static/description/icon.png HTTP/1.1" 200 - 0 0.000 0.017
2025-08-27 12:57:23,265 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:23] "GET /delivery_usps_rest/static/description/icon.png HTTP/1.1" 404 - 19 0.033 0.112
2025-08-27 12:57:26,446 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.button_install on [] to user admin #2 via 127.0.0.1 
2025-08-27 12:57:27,599 35152 INFO martinlion_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 12:57:27,609 35152 INFO martinlion_2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-27 12:57:27,642 35152 INFO martinlion_2 odoo.modules.loading: updating modules list 
2025-08-27 12:57:27,642 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: ALLOW access to module.update_list on [] to user __system__ #1 via 127.0.0.1 
2025-08-27 12:57:30,853 35152 INFO martinlion_2 odoo.modules.loading: loading 204 modules... 
2025-08-27 12:57:31,107 35152 INFO martinlion_2 odoo.modules.loading: Loading module sale_order_delivery_operations (187/204) 
2025-08-27 12:57:32,205 35152 INFO martinlion_2 odoo.modules.registry: module sale_order_delivery_operations: creating or updating database tables 
2025-08-27 12:57:32,941 35152 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/sale_view_order_form.xml 
2025-08-27 12:57:33,084 35152 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_form.xml 
2025-08-27 12:57:33,148 35152 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_internal_search.xml 
2025-08-27 12:57:33,241 35152 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/report/print_labels_report_template.xml 
2025-08-27 12:57:33,298 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: module sale_order_delivery_operations: loading translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 12:57:33,298 35152 INFO martinlion_2 odoo.tools.translate: loading base translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 12:57:33,441 35152 INFO martinlion_2 odoo.modules.loading: Module sale_order_delivery_operations loaded in 2.33s, 189 queries (+189 other) 
2025-08-27 12:57:33,459 35152 INFO martinlion_2 odoo.modules.loading: 204 modules loaded in 2.61s, 189 queries (+189 extra) 
2025-08-27 12:57:35,151 35152 INFO martinlion_2 odoo.modules.registry: verifying fields for every extended model 
2025-08-27 12:57:37,621 35152 INFO martinlion_2 odoo.modules.loading: Modules loaded. 
2025-08-27 12:57:37,661 35152 INFO martinlion_2 odoo.modules.registry: Registry loaded in 10.115s 
2025-08-27 12:57:37,661 35152 INFO martinlion_2 odoo.addons.base.models.ir_module: getting next ir.actions.todo() 
2025-08-27 12:57:37,677 35152 INFO martinlion_2 odoo.modules.registry: Registry changed, signaling through the database 
2025-08-27 12:57:37,677 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:37] "POST /web/dataset/call_button HTTP/1.1" 200 - 7092 7.694 7.081
2025-08-27 12:57:38,012 35152 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 12:57:38,423 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 12:57:38,431 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 12:57:38,431 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 12:57:38,431 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 12:57:38,431 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 12:57:38,437 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 12:57:38,447 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 12:57:38,447 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 12:57:38,447 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 12:57:38,447 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-08-27 12:57:38,447 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 12:57:38,455 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001683D9780E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 12:57:39,570 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:39] "GET /web HTTP/1.1" 200 - 113 0.138 1.424
2025-08-27 12:57:40,027 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:40] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 24 0.204 0.036
2025-08-27 12:57:40,053 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:40] "POST /mail/init_messaging HTTP/1.1" 200 - 57 0.092 0.199
2025-08-27 12:57:40,179 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:40] "POST /mail/load_message_failures HTTP/1.1" 200 - 17 0.013 0.057
2025-08-27 12:57:40,426 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:57:40,428 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:40] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 17 0.012 0.038
2025-08-27 12:57:40,889 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:40] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.008 0.007
2025-08-27 12:57:42,051 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:42] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.011
2025-08-27 12:57:45,156 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "GET /web HTTP/1.1" 200 - 16 0.016 0.035
2025-08-27 12:57:45,389 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "POST /web/action/load HTTP/1.1" 200 - 9 0.018 0.020
2025-08-27 12:57:45,456 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "POST /mail/init_messaging HTTP/1.1" 200 - 33 0.049 0.048
2025-08-27 12:57:45,714 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 9 0.003 0.031
2025-08-27 12:57:45,826 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "POST /mail/load_message_failures HTTP/1.1" 200 - 15 0.056 0.049
2025-08-27 12:57:45,904 35152 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 12:57:45,917 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:45] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 9 0.068 0.072
2025-08-27 12:57:46,149 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:46] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 111 0.184 0.268
2025-08-27 12:57:46,533 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:46] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 7 0.022 0.022
2025-08-27 12:57:46,602 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:46] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 43 0.050 0.112
2025-08-27 12:57:46,987 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:46] "POST /web/dataset/call_kw/stock.picking.type/check_access_rights HTTP/1.1" 200 - 1 0.006 0.000
2025-08-27 12:57:47,322 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:47] "POST /mail/thread/data HTTP/1.1" 200 - 27 0.032 0.049
2025-08-27 12:57:47,404 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:47] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.052 0.031
2025-08-27 12:57:47,890 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:47] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.004 0.006
2025-08-27 12:57:51,855 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:51] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.001
2025-08-27 12:57:52,104 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:52] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.003 0.014
2025-08-27 12:57:54,928 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:54] "GET /web/assets/d324596/web.report_assets_common.rtl.min.css HTTP/1.1" 200 - 3 0.001 0.020
2025-08-27 12:57:54,937 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:54] "GET /web/assets/059d187/web.report_assets_pdf.rtl.min.css HTTP/1.1" 200 - 3 0.018 0.015
2025-08-27 12:57:55,038 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:55] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.010
2025-08-27 12:57:57,163 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:57] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.010
2025-08-27 12:57:57,171 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:57] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.021
2025-08-27 12:57:57,171 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:57] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.016
2025-08-27 12:57:57,171 35152 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 12:57:57] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.018
2025-08-27 12:58:00,162 35152 INFO martinlion_2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: stock.picking, records [3155]. 
2025-08-27 12:58:00,170 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:58:00] "POST /report/download HTTP/1.1" 200 - 58 0.059 7.935
2025-08-27 12:58:00,206 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:58:00] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.009 0.007
2025-08-27 12:58:00,605 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:58:00] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 28 0.035 0.074
2025-08-27 12:58:00,996 35152 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 12:58:00] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.014 0.064
2025-08-27 12:59:58,535 39144 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 12:59:58,535 39144 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 12:59:58,535 39144 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_matrin_live'] 
2025-08-27 12:59:58,535 39144 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 12:59:58,702 39144 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 13:00:00,464 39144 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 13:00:00,577 39144 INFO martinlion_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 13:00:00,585 39144 INFO martinlion_2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-27 13:00:00,588 39144 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:00:00,605 39144 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:00:00,722 39144 INFO martinlion_2 odoo.modules.loading: loading 204 modules... 
2025-08-27 13:00:00,740 39144 WARNING martinlion_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-08-27 13:00:03,305 39144 WARNING martinlion_2 odoo.api.create: The model odoo.addons.user_warehouse_restriction.models.res_users is not overriding the create method in batch 
2025-08-27 13:00:04,154 39144 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.res_users is not overriding the create method in batch 
2025-08-27 13:00:04,158 39144 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:00:04,158 39144 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:00:04,158 39144 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:00:04,174 39144 INFO martinlion_2 odoo.modules.loading: Loading module sale_order_delivery_operations (187/204) 
2025-08-27 13:00:05,281 39144 INFO martinlion_2 odoo.modules.registry: module sale_order_delivery_operations: creating or updating database tables 
2025-08-27 13:00:05,438 39144 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/sale_view_order_form.xml 
2025-08-27 13:00:05,721 39144 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_form.xml 
2025-08-27 13:00:05,787 39144 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_internal_search.xml 
2025-08-27 13:00:05,832 39144 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/report/print_labels_report_template.xml 
2025-08-27 13:00:05,910 39144 INFO martinlion_2 odoo.addons.base.models.ir_module: module sale_order_delivery_operations: loading translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:00:05,910 39144 INFO martinlion_2 odoo.tools.translate: loading base translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:00:06,071 39144 INFO martinlion_2 odoo.modules.loading: Module sale_order_delivery_operations loaded in 1.90s, 189 queries (+189 other) 
2025-08-27 13:00:06,447 39144 INFO martinlion_2 odoo.modules.loading: 204 modules loaded in 5.72s, 189 queries (+189 extra) 
2025-08-27 13:00:07,865 39144 INFO martinlion_2 odoo.modules.registry: verifying fields for every extended model 
2025-08-27 13:00:07,971 39144 INFO martinlion_2 odoo.modules.loading: Modules loaded. 
2025-08-27 13:00:07,987 39144 INFO martinlion_2 odoo.modules.registry: Registry loaded in 7.513s 
2025-08-27 13:00:08,059 39144 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:00:08,090 39144 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:00:08,266 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:08] "GET /web/service-worker.js HTTP/1.1" 200 - 6 0.062 7.488
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 13:00:13,216 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 13:00:13,228 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x0000014A661B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 13:00:13,323 39144 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:00:13,348 39144 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:00:13,768 39144 INFO martinlion_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'templates'] 
2025-08-27 13:00:13,768 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:13] "GET /web HTTP/1.1" 200 - 891 1.457 11.839
2025-08-27 13:00:14,743 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:14] "POST /mail/init_messaging HTTP/1.1" 200 - 58 0.106 0.029
2025-08-27 13:00:14,826 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:14] "POST /mail/load_message_failures HTTP/1.1" 200 - 20 0.035 0.020
2025-08-27 13:00:15,064 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:15] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.000 0.000
2025-08-27 13:00:15,064 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:15] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.000 0.000
2025-08-27 13:00:15,444 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:15] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 21 0.261 0.544
2025-08-27 13:00:15,458 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:15] "POST /web/action/load HTTP/1.1" 200 - 9 0.007 1.169
2025-08-27 13:00:16,445 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:16] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.000 0.010
2025-08-27 13:00:16,485 39144 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-27 13:00:18,377 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:18] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 13:00:25,896 39144 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 13:00:25,896 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:25] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 14 0.056 0.018
2025-08-27 13:00:26,530 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:26] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 110 0.143 0.222
2025-08-27 13:00:27,293 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:27] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 43 0.157 0.248
2025-08-27 13:00:27,364 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:27] "POST /web/dataset/call_kw/stock.picking.type/check_access_rights HTTP/1.1" 200 - 1 0.000 0.006
2025-08-27 13:00:27,819 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:27] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.051 0.063
2025-08-27 13:00:27,906 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:27] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.146 0.042
2025-08-27 13:00:30,006 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:30] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.006
2025-08-27 13:00:30,333 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:30] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.003 0.007
2025-08-27 13:00:33,313 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:33] "GET /web/assets/d324596/web.report_assets_common.rtl.min.css HTTP/1.1" 200 - 3 0.002 0.136
2025-08-27 13:00:33,339 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:33] "GET /web/assets/059d187/web.report_assets_pdf.rtl.min.css HTTP/1.1" 200 - 3 0.025 0.139
2025-08-27 13:00:33,396 39144 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:33] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:00:35,475 39144 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:35] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.017
2025-08-27 13:00:35,482 39144 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:35] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.022
2025-08-27 13:00:35,487 39144 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:35] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.025
2025-08-27 13:00:35,488 39144 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:35] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.024
2025-08-27 13:00:38,485 39144 INFO martinlion_2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: stock.picking, records [3155]. 
2025-08-27 13:00:38,499 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:38] "POST /report/download HTTP/1.1" 200 - 58 0.101 7.807
2025-08-27 13:00:38,542 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:38] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.010 0.011
2025-08-27 13:00:38,917 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:38] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 28 0.023 0.052
2025-08-27 13:00:39,326 39144 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:00:39] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.040 0.036
2025-08-27 13:03:19,932 29560 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 13:03:19,932 29560 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 13:03:19,932 29560 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_matrin_live'] 
2025-08-27 13:03:19,932 29560 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 13:03:20,143 29560 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 13:03:21,868 29560 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 13:03:22,159 29560 INFO martinlion_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 13:03:22,171 29560 INFO martinlion_2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-27 13:03:22,176 29560 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:03:22,188 29560 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:03:22,283 29560 INFO martinlion_2 odoo.modules.loading: loading 204 modules... 
2025-08-27 13:03:22,305 29560 WARNING martinlion_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-08-27 13:03:24,738 29560 WARNING martinlion_2 odoo.api.create: The model odoo.addons.user_warehouse_restriction.models.res_users is not overriding the create method in batch 
2025-08-27 13:03:25,507 29560 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.res_users is not overriding the create method in batch 
2025-08-27 13:03:25,507 29560 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:03:25,507 29560 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:03:25,507 29560 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:03:25,521 29560 INFO martinlion_2 odoo.modules.loading: Loading module sale_order_delivery_operations (187/204) 
2025-08-27 13:03:26,621 29560 INFO martinlion_2 odoo.modules.registry: module sale_order_delivery_operations: creating or updating database tables 
2025-08-27 13:03:26,771 29560 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/sale_view_order_form.xml 
2025-08-27 13:03:27,053 29560 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_form.xml 
2025-08-27 13:03:27,123 29560 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_internal_search.xml 
2025-08-27 13:03:27,155 29560 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/report/print_labels_report_template.xml 
2025-08-27 13:03:27,223 29560 INFO martinlion_2 odoo.addons.base.models.ir_module: module sale_order_delivery_operations: loading translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:03:27,223 29560 INFO martinlion_2 odoo.tools.translate: loading base translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:03:27,362 29560 INFO martinlion_2 odoo.modules.loading: Module sale_order_delivery_operations loaded in 1.84s, 189 queries (+189 other) 
2025-08-27 13:03:27,664 29560 INFO martinlion_2 odoo.modules.loading: 204 modules loaded in 5.38s, 189 queries (+189 extra) 
2025-08-27 13:03:28,766 29560 INFO martinlion_2 odoo.modules.registry: verifying fields for every extended model 
2025-08-27 13:03:28,831 29560 INFO martinlion_2 odoo.modules.loading: Modules loaded. 
2025-08-27 13:03:28,839 29560 INFO martinlion_2 odoo.modules.registry: Registry loaded in 6.797s 
2025-08-27 13:03:28,895 29560 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:03:28,935 29560 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:03:29,089 29560 INFO martinlion_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'templates'] 
2025-08-27 13:03:29,128 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:29] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 789 0.897 6.192
2025-08-27 13:03:29,738 29560 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-27 13:03:35,767 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 13:03:35,776 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 13:03:35,784 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 13:03:35,784 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 13:03:35,788 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001B39DE440E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 13:03:36,047 29560 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:03:36,106 29560 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:03:37,256 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:37] "GET /web HTTP/1.1" 200 - 109 0.314 12.892
2025-08-27 13:03:37,633 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:37] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.014 0.014
2025-08-27 13:03:38,121 29560 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 13:03:38,121 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:38] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.081 0.034
2025-08-27 13:03:38,121 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:38] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.010 0.067
2025-08-27 13:03:38,261 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:38] "POST /mail/init_messaging HTTP/1.1" 200 - 58 0.264 0.066
2025-08-27 13:03:38,757 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:38] "POST /mail/load_message_failures HTTP/1.1" 200 - 20 0.211 0.095
2025-08-27 13:03:38,867 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:38] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.017 0.094
2025-08-27 13:03:41,122 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:41] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.016
2025-08-27 13:03:41,655 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:41] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 21 0.144 3.532
2025-08-27 13:03:41,744 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:41] "POST /web/action/load HTTP/1.1" 200 - 9 0.032 4.108
2025-08-27 13:03:42,489 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:42] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 110 0.153 0.264
2025-08-27 13:03:42,964 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:42] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 43 0.161 0.287
2025-08-27 13:03:43,360 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:43] "POST /web/dataset/call_kw/stock.picking.type/check_access_rights HTTP/1.1" 200 - 1 0.000 0.005
2025-08-27 13:03:43,718 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:43] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.030 0.080
2025-08-27 13:03:43,839 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:03:43] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.081 0.052
2025-08-27 13:04:41,255 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:41] "POST /web/action/load HTTP/1.1" 200 - 9 0.016 0.017
2025-08-27 13:04:41,593 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:41] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.003 0.019
2025-08-27 13:04:44,529 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:44] "GET /web/assets/d324596/web.report_assets_common.rtl.min.css HTTP/1.1" 200 - 3 0.013 0.016
2025-08-27 13:04:44,540 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:44] "GET /web/assets/059d187/web.report_assets_pdf.rtl.min.css HTTP/1.1" 200 - 3 0.012 0.027
2025-08-27 13:04:44,596 29560 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:44] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:04:46,742 29560 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:46] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:04:46,742 29560 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:46] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:04:46,755 29560 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:46] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.015
2025-08-27 13:04:46,764 29560 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:46] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.022
2025-08-27 13:04:49,781 29560 INFO martinlion_2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: stock.picking, records [3155]. 
2025-08-27 13:04:49,795 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:49] "POST /report/download HTTP/1.1" 200 - 58 0.095 7.862
2025-08-27 13:04:49,839 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:49] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.010 0.006
2025-08-27 13:04:50,241 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:50] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 28 0.034 0.065
2025-08-27 13:04:50,632 29560 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:04:50] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.032 0.043
2025-08-27 13:09:26,976 26136 INFO ? odoo: Odoo version 17.0-20241115 
2025-08-27 13:09:26,976 26136 INFO ? odoo: Using configuration file at C:\odoo17\server\odoo.conf 
2025-08-27 13:09:26,976 26136 INFO ? odoo: addons paths: ['C:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\sessions\\addons\\17.0', 'c:\\odoo17\\server\\odoo\\addons', 'c:\\odoo17\\server\\enterprise17', 'c:\\odoo17\\server\\custom_matrin_live'] 
2025-08-27 13:09:26,976 26136 INFO ? odoo: database: openpg@localhost:5432 
2025-08-27 13:09:27,152 26136 INFO ? odoo.addons.base.models.ir_actions_report: Will use the Wkhtmltopdf binary at C:\odoo17\thirdparty\wkhtmltopdf.exe 
2025-08-27 13:09:28,811 26136 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-F0S8PNU:8017 
2025-08-27 13:09:29,164 26136 INFO martinlion_2 odoo.modules.loading: loading 1 modules... 
2025-08-27 13:09:29,174 26136 INFO martinlion_2 odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-08-27 13:09:29,186 26136 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:09:29,195 26136 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:09:29,259 26136 INFO martinlion_2 odoo.modules.loading: loading 204 modules... 
2025-08-27 13:09:29,275 26136 WARNING martinlion_2 odoo.addons.attachment_indexation.models.ir_attachment: Attachment indexation of PDF documents is unavailable because the 'pdfminer' Python library cannot be found on the system. You may install it from https://pypi.org/project/pdfminer.six/ (e.g. `pip3 install pdfminer.six`) 
2025-08-27 13:09:31,624 26136 WARNING martinlion_2 odoo.api.create: The model odoo.addons.user_warehouse_restriction.models.res_users is not overriding the create method in batch 
2025-08-27 13:09:32,349 26136 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.res_users is not overriding the create method in batch 
2025-08-27 13:09:32,349 26136 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:09:32,357 26136 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:09:32,357 26136 WARNING martinlion_2 odoo.api.create: The model odoo.addons.restrictions.models.sale_order is not overriding the create method in batch 
2025-08-27 13:09:32,374 26136 INFO martinlion_2 odoo.modules.loading: Loading module sale_order_delivery_operations (187/204) 
2025-08-27 13:09:33,357 26136 INFO martinlion_2 odoo.modules.registry: module sale_order_delivery_operations: creating or updating database tables 
2025-08-27 13:09:33,494 26136 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/sale_view_order_form.xml 
2025-08-27 13:09:33,762 26136 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_form.xml 
2025-08-27 13:09:33,844 26136 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/views/stock_view_picking_internal_search.xml 
2025-08-27 13:09:33,890 26136 INFO martinlion_2 odoo.modules.loading: loading sale_order_delivery_operations/report/print_labels_report_template.xml 
2025-08-27 13:09:33,958 26136 INFO martinlion_2 odoo.addons.base.models.ir_module: module sale_order_delivery_operations: loading translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:09:33,958 26136 INFO martinlion_2 odoo.tools.translate: loading base translation file c:\odoo17\server\custom_matrin_live\sale_order_delivery_operations\i18n\ar_001.po for language ar_001 
2025-08-27 13:09:34,107 26136 INFO martinlion_2 odoo.modules.loading: Module sale_order_delivery_operations loaded in 1.73s, 189 queries (+189 other) 
2025-08-27 13:09:34,474 26136 INFO martinlion_2 odoo.modules.loading: 204 modules loaded in 5.21s, 189 queries (+189 extra) 
2025-08-27 13:09:35,834 26136 INFO martinlion_2 odoo.modules.registry: verifying fields for every extended model 
2025-08-27 13:09:35,919 26136 INFO martinlion_2 odoo.modules.loading: Modules loaded. 
2025-08-27 13:09:35,950 26136 INFO martinlion_2 odoo.modules.registry: Registry loaded in 6.990s 
2025-08-27 13:09:36,023 26136 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:09:36,046 26136 INFO martinlion_2 odoo.addons.base.models.ir_http: Generating routing map for key 1 
2025-08-27 13:09:36,243 26136 INFO martinlion_2 odoo.modules.registry: Caches invalidated, signaling through the database: ['default', 'templates'] 
2025-08-27 13:09:36,323 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:36] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 789 1.031 6.334
2025-08-27 13:09:37,063 26136 INFO ? odoo.addons.bus.models.bus: Bus.loop listen imbus on db postgres 
2025-08-27 13:09:43,121 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\fe/fea170a89175533bc09cf0b0ba93ae37e99ea077'
2025-08-27 13:09:43,123 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d92280f55c4634419639d9ea28f781543c5c8210 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d92280f55c4634419639d9ea28f781543c5c8210'
2025-08-27 13:09:43,123 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ca/cad9804f210a8c1e26c8ebab07b8056b9d5ec66b'
2025-08-27 13:09:43,123 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\aa/aa2cad94340900f9e642750f42295ca97ab0ec81 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\aa/aa2cad94340900f9e642750f42295ca97ab0ec81'
2025-08-27 13:09:43,123 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d9/d91cb0023dd0de9111309aa17eddcd5caff2fa91'
2025-08-27 13:09:43,133 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\84/84a4575345063f9255f79c023cdd80b6b6eff5f2 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\84/84a4575345063f9255f79c023cdd80b6b6eff5f2'
2025-08-27 13:09:43,133 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f9/f9cf2c8b67d3ebc89ca4d180ef207a248c4e3130'
2025-08-27 13:09:43,133 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ba/badd31c33040c28edfbfc56c47f2f75f7e05369a'
2025-08-27 13:09:43,133 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\c2/c2f49a19f3cf050243e62b22cde5072cca84fcaa'
2025-08-27 13:09:43,138 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\67/670e9ff9033932ebe066245e9ac5c3382cde45f4 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\67/670e9ff9033932ebe066245e9ac5c3382cde45f4'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\51/517a97a04c19cdd67572ff8a0ef89fad5a2c278e'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c96f0109a19f3ff4c0a7486431a79da8c42d8de'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\1c/1c16002d10f6155661a6ae83417fee29e281fb7b 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\1c/1c16002d10f6155661a6ae83417fee29e281fb7b'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\a4/a4511b76524b49a86d44d27508dd20f056bfe827 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\a4/a4511b76524b49a86d44d27508dd20f056bfe827'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\f3/f31bdf173e5748b875ca5e389e1edba54b4bff25'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\74/744fc247b813c44f4cf02357706e88b283cb0f97 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\74/744fc247b813c44f4cf02357706e88b283cb0f97'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\ac/ac2a935c445b4a44ecb11e8b692d41661f9ecae1'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\3d/3d7a7360fe4a73a90dd3b4ba554a9254ed61e864'
2025-08-27 13:09:43,140 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\tools\cache.py", line 99, in lookup
    r = d[key]
        ~^^^^^
  File "C:\Odoo17\python\Lib\site-packages\decorator.py", line 232, in fun
    return caller(func, *(extras + args), **kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\odoo17\server\odoo\tools\lru.py", line 34, in __getitem__
    a = self.d[obj]
        ~~~~~~^^^^^
KeyError: ('ir.ui.menu', <function IrUiMenu.load_menus at 0x000001DC605B40E0>, 2, '1', ('ar_001',))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\b7/b73a9deb85def8a1a00e4d545c4f79747d78f487'
2025-08-27 13:09:43,503 26136 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'restrictions', defaulting to LGPL-3 
2025-08-27 13:09:43,539 26136 WARNING martinlion_2 odoo.modules.module: Missing `license` key in manifest for 'elmaktab_elrakami_receipt_printout', defaulting to LGPL-3 
2025-08-27 13:09:44,674 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:44] "GET /web HTTP/1.1" 200 - 110 0.342 15.304
2025-08-27 13:09:45,122 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:45] "GET /bus/websocket_worker_bundle?v=17.0-1 HTTP/1.1" 304 - 4 0.011 0.018
2025-08-27 13:09:45,573 26136 INFO martinlion_2 odoo.addons.base.models.ir_attachment: _read_file reading c:\odoo17\sessions\filestore\martinlion_2\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388 
Traceback (most recent call last):
  File "C:\odoo17\server\odoo\addons\base\models\ir_attachment.py", line 128, in _file_read
    with open(full_path, 'rb') as f:
         ^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: [Errno 2] No such file or directory: 'c:\\odoo17\\sessions\\filestore\\martinlion_2\\d0/d08acdb1f9cd17c5fa286a4db7eb4337db745388'
2025-08-27 13:09:45,573 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:45] "GET /web/image?model=res.users&field=avatar_128&id=2 HTTP/1.1" 304 - 10 0.026 0.025
2025-08-27 13:09:45,625 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:45] "GET /websocket?version=17.0-1 HTTP/1.1" 101 - 1 0.003 0.036
2025-08-27 13:09:45,681 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:45] "POST /mail/init_messaging HTTP/1.1" 200 - 58 0.187 0.078
2025-08-27 13:09:46,059 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:46] "GET /web/manifest.webmanifest HTTP/1.1" 200 - 9 0.019 0.044
2025-08-27 13:09:46,140 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:46] "POST /mail/load_message_failures HTTP/1.1" 200 - 20 0.073 0.072
2025-08-27 13:09:48,612 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:48] "GET /web/service-worker.js HTTP/1.1" 200 - 1 0.000 0.016
2025-08-27 13:09:48,907 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:48] "POST /web/dataset/call_kw/res.users/systray_get_activities HTTP/1.1" 200 - 21 0.150 3.281
2025-08-27 13:09:48,995 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:48] "POST /web/action/load HTTP/1.1" 200 - 9 0.013 3.888
2025-08-27 13:09:49,708 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:49] "POST /web/dataset/call_kw/stock.picking/get_views HTTP/1.1" 200 - 110 0.124 0.245
2025-08-27 13:09:50,207 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:50] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 43 0.187 0.269
2025-08-27 13:09:50,624 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:50] "POST /web/dataset/call_kw/stock.picking.type/check_access_rights HTTP/1.1" 200 - 1 0.000 0.000
2025-08-27 13:09:50,974 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:50] "POST /mail/thread/data HTTP/1.1" 200 - 26 0.062 0.037
2025-08-27 13:09:51,057 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:51] "POST /mail/thread/messages HTTP/1.1" 200 - 26 0.048 0.034
2025-08-27 13:09:54,247 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:54] "POST /web/action/load HTTP/1.1" 200 - 9 0.004 0.028
2025-08-27 13:09:54,570 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:54] "POST /report/check_wkhtmltopdf HTTP/1.1" 200 - 1 0.000 0.006
2025-08-27 13:09:57,573 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:57] "GET /web/assets/d324596/web.report_assets_common.rtl.min.css HTTP/1.1" 200 - 3 0.012 0.021
2025-08-27 13:09:57,591 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:57] "GET /web/assets/059d187/web.report_assets_pdf.rtl.min.css HTTP/1.1" 200 - 3 0.022 0.029
2025-08-27 13:09:57,659 26136 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:57] "GET /web/static/fonts/lato/Lato-Bol-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:09:59,757 26136 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:59] "GET /web/static/fonts/lato/Lato-Lig-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:09:59,757 26136 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:59] "GET /web/static/fonts/lato/Lato-Bla-webfont.woff HTTP/1.1" 200 - 0 0.000 0.000
2025-08-27 13:09:59,773 26136 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:59] "GET /web/static/fonts/lato/Lato-Reg-webfont.woff HTTP/1.1" 200 - 0 0.000 0.015
2025-08-27 13:09:59,775 26136 INFO ? werkzeug: 127.0.0.1 - - [27/Aug/2025 13:09:59] "GET /web/static/fonts/lato/Lato-Hai-webfont.woff HTTP/1.1" 200 - 0 0.000 0.017
2025-08-27 13:10:02,743 26136 INFO martinlion_2 odoo.addons.base.models.ir_actions_report: The PDF report has been generated for model: stock.picking, records [3155]. 
2025-08-27 13:10:02,757 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:10:02] "POST /report/download HTTP/1.1" 200 - 58 0.091 7.827
2025-08-27 13:10:02,806 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:10:02] "POST /mail/thread/messages HTTP/1.1" 200 - 4 0.011 0.011
2025-08-27 13:10:03,219 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:10:03] "POST /web/dataset/call_kw/stock.picking/web_read HTTP/1.1" 200 - 28 0.032 0.094
2025-08-27 13:10:03,619 26136 INFO martinlion_2 werkzeug: 127.0.0.1 - - [27/Aug/2025 13:10:03] "POST /mail/thread/data HTTP/1.1" 200 - 20 0.027 0.048
