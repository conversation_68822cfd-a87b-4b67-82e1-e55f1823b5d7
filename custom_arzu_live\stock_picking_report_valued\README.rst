=====================
Valued Picking Report
=====================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:3bc5f58e46ae4d40f917aa1b88e9b698ba1a4d35d1eddd234434f2e39c173fda
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fstock--logistics--reporting-lightgray.png?logo=github
    :target: https://github.com/OCA/stock-logistics-reporting/tree/17.0/stock_picking_report_valued
    :alt: OCA/stock-logistics-reporting
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/stock-logistics-reporting-17-0/stock-logistics-reporting-17-0-stock_picking_report_valued
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/stock-logistics-reporting&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

Add amount information to Delivery Slip report. You can select at
partner level if picking list report must be valued or not. If the
picking is done it's valued with quantity done, otherwise the picking is
valued with reserved quantity.

**Table of contents**

.. contents::
   :local:

Configuration
=============

1. Go to *Sales > Orders > Customers > (select one of your choice) >
   Sales & Purchases*.
2. Set *Valued picking* field on.

Usage
=====

To get the stock picking valued report:

1. Create a Sales Order with storable products a *Valued picking* able
   customer.
2. Confirm the Sale Order.
3. Click on *Delivery* stat button.
4. Go to *Print > Delivery Slip*.

Known issues / Roadmap
======================

-  If the picking is not reserved, values aren't computed.

Changelog
=========

This module includes the features from the module
*stock_picking_report_valued_delivery*:
https://github.com/OCA/stock-logistics-reporting/pull/285

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/stock-logistics-reporting/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/stock-logistics-reporting/issues/new?body=module:%20stock_picking_report_valued%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Tecnativa

Contributors
------------

-  `Avanzosc <http://www.avanzosc.es>`__:

   -  Oihane Crucelaegui

-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Pedro M. Baeza
   -  Antonio Espinosa
   -  Carlos Dauden
   -  David Vidal
   -  Luis M. Ontalba
   -  Ernesto Tejeda
   -  Sergio Teruel

-  `GreenIce <https://www.greenice.com>`__:

   -  Fernando La Chica <<EMAIL>>

-  `Landoo <https://www.landoo.es>`__:

   -  Aritz Olea <<EMAIL>>

-  `Studio73 <https://www.studio73.es>`__:

   -  Miguel Gandia <<EMAIL>>

-  `Trobz <https://trobz.com>`__:

   -  Nguyen Minh Chien <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/stock-logistics-reporting <https://github.com/OCA/stock-logistics-reporting/tree/17.0/stock_picking_report_valued>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
