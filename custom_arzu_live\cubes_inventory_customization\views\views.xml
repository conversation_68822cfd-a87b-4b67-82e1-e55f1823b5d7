<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Inherit Form View to Modify it -->
        <record id="stock_picking_form_inherit" model="ir.ui.view">
            <field name="name">Stock Picking Inherit</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='location_id']" position="after">
                    <field name="driver_id"/>
                </xpath>
                <xpath expr="//button[@name='do_print_picking']" position="attributes">

                </xpath>
                <xpath expr="//header" position="replace">
                    <header>
                        <button name="action_confirm" invisible="state != 'draft'" string="Mark as Todo" type="object"
                                class="oe_highlight" groups="base.group_user" data-hotkey="q"/>
                        <button name="action_assign" invisible="not show_check_availability" string="Check Availability"
                                type="object" class="oe_highlight" groups="base.group_user" data-hotkey="w"/>
                        <button type="object" string="Driver"
                                invisible="state in ['cancel', 'driver','done', 'waiting']" name="action_driver"
                                class="oe_highlight"/>
                        <button name="button_validate"
                                invisible="state in ('draft', 'confirmed', 'done', 'assigned', 'cancel')"
                                string="Validate" type="object" class="oe_highlight" groups="stock.group_stock_user"
                                data-hotkey="v"/>
                        <button name="button_validate"
                                invisible="state in ('waiting', 'assigned', 'done', 'assigned','cancel')"
                                string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate"
                                data-hotkey="v"/>
                        <widget name="signature" string="Sign" highlight="1"
                                invisible="not id or picking_type_code != 'outgoing' or state != 'done'"
                                full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                        <widget name="signature" string="Sign"
                                invisible="not id or picking_type_code != 'outgoing' or state == 'done'"
                                full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
                        <button name="do_print_picking" string="Print" groups="stock.group_stock_user" type="object"
                                invisible="state != 'assigned'" data-hotkey="o"/>
                        <button string="Print Labels" type="object" name="action_open_label_type"/>
                        <button name="523" string="Print" invisible="state != 'done'" type="action"
                                groups="base.group_user" data-hotkey="o"/>
                        <button name="541" string="Return" invisible="state != 'done'" type="action"
                                groups="base.group_user" data-hotkey="k"/>
                        <field name="state" widget="statusbar" invisible="picking_type_code != 'incoming'"
                               statusbar_visible="draft,assigned,done"/>
                        <field name="state" widget="statusbar" invisible="picking_type_code == 'incoming'"
                               statusbar_visible="draft,confirmed,assigned,done"/>
                        <button name="action_cancel"
                                invisible="state not in ('assigned', 'confirmed', 'draft', 'waiting')" string="Cancel"
                                groups="base.group_user" type="object" data-hotkey="x"/>
                    </header>

                </xpath>

            </field>
        </record>

    </data>
</odoo>