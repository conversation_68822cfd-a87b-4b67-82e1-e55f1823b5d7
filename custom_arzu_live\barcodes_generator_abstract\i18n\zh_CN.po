# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes_generator_abstract
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: Automatically generated\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__generate_type
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_type
msgid ""
"Allow to generate barcode, including a number  (a base) in the final "
"barcode.\n"
"\n"
" - 'Base Set Manually' : User should set manually the value of the barcode "
"base\n"
" - 'Base managed by Sequence': System will generate the base via a sequence"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_automate
msgid "Automatic Generation"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_base
msgid "Barcode Base"
msgstr ""

#. module: barcodes_generator_abstract
#: model_terms:ir.ui.view,arch_db:barcodes_generator_abstract.view_barcode_rule_form
msgid "Barcode Generation"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.ui.menu,name:barcodes_generator_abstract.menu_barcode_rule
msgid "Barcode Nomenclatures"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model,name:barcodes_generator_abstract.model_barcode_rule
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_rule_id
msgid "Barcode Rule"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__sequence
msgid "Base managed by Sequence"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__manual
msgid "Base set Manually"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_automate
msgid ""
"Check this to automatically generate a base and a barcode if this rule is "
"selected."
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model,name:barcodes_generator_abstract.model_barcode_generate_mixin
msgid "Generate Barcode Mixin"
msgstr ""

#. module: barcodes_generator_abstract
#: model:res.groups,name:barcodes_generator_abstract.generate_barcode
msgid "Generate Barcodes"
msgstr ""

#. module: barcodes_generator_abstract
#. odoo-python
#: code:addons/barcodes_generator_abstract/models/barcode_generate_mixin.py:0
#, python-format
msgid ""
"Generate Base can be used only with barcode rule with 'Generate Type' set to "
"'Base managed by Sequence'"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_model
msgid "Generate Model"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__generate_type
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_type
msgid "Generate Type"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__sequence_id
msgid "Generation Sequence"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_model
msgid "If 'Generate Type' is set, mention the model related to this rule."
msgstr ""

#. module: barcodes_generator_abstract
#: model_terms:ir.ui.view,arch_db:barcodes_generator_abstract.view_barcode_rule_form
msgid ""
"If you leave the sequence field blank, a sequence will be created "
"automatically when the barcode rule is saved, based on the padding of the "
"barcode."
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__no
msgid "No generation"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__padding
msgid "Padding"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_rule_id
msgid "Select a rule to generate a barcode"
msgstr ""

#. module: barcodes_generator_abstract
#. odoo-python
#: code:addons/barcodes_generator_abstract/models/barcode_rule.py:0
#, python-format
msgid "Sequence - %s"
msgstr ""

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_base
msgid ""
"This value is used to generate barcode according to the setting of the "
"barcode rule."
msgstr ""
