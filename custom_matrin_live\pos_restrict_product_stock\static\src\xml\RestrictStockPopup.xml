<?xml version="1.0" encoding="UTF-8"?>
<template id="template" xml:space="preserve">
    <!--This template is used for registering a popup for restrict product from ordering-->
    <t t-name="RestrictStockPopup" owl="1">
        <div class="popup popup-textinput">
                <div class="modal-header btn-lg btn-primary">
            <h4 class="modal-title text-white text-center" >Product Out of Stock</h4>
                </div>
            <div class="modal-body popup-textarea-wrap">
                <span style="color:#811331"><t t-esc="props.body"/></span>
                is out of stock.
                <br/>
                Click order, if you still want to add this product.
            </div>
            <div class="footer footer-flex modal-footer">
                <div class="button cancel btn btn-lg btn-primary text-danger"
                     t-on-click="cancel">
                      Cancel
                </div>
                <div class="button confirmbtn btn btn-lg btn-primary" t-on-click="_OrderProduct">
                      Order
                </div>
            </div>
        </div>
    </t>
        </template>
