<section class="oe_container">
    <div class="oe_row oe_spaced">
        <div class="oe_span12">
            <h2 class="oe_slogan">Warehouse Restriction</h2>
            <h3 class="oe_slogan">Warehouse and Stock Location Restriction on Users</h3>
        </div>
        <div class="row">
            <div class="col-xs-12 col-md-12">
                    <img class="oe_picture oe_screenshot"src="user_button.png">
            </div>
            <div style="width:80%; margin-left: 10%; margin-right: 10%;">
        After successful installation it will show 2 fields under User -> Preferences -> Menu Customization, Restrict Location select button and Default Warehouse Operations button. 
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-md-12">
                <img class="oe_picture oe_screenshot" src="warehouse.png">
            </div>
            <div style="width:80%; margin-left: 10%; margin-right: 10%;">
        To restrict the user to limited warehouses, admin can add allowed Warehouses under Default Warehouse Operations. This will restric the user to other warehouses.
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-md-12">
                <img class="oe_picture oe_screenshot" src="stock.png">
            </div>
            <div style="width:80%; margin-left: 10%; margin-right: 10%;">
        On selecting Restrict Location option, it will add another tab Allowed Stock Locations after Preferences where the admin can add allowed Stock Location for the User.
            </div>
        </div>

        <div class="row">
        <br />
            <div class="col-xs-12 col-md-12">
                <img class="oe_picture oe_screenshot" src="stock_restrict.png">
            </div>
            <div style="width:80%; margin-left: 10%; margin-right: 10%;">
        Note : This restrictions are created using rule and access rights, so it will have no effect on the Adminstrator.
            </div>
        </div>

    </div>
</section>


