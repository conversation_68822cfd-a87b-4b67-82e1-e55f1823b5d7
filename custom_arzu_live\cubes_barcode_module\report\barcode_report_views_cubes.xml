<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Dynamic Barcode Label Data template -->
    <template id="sh_barcode_cubes_report_doc">
        <t t-call="web.basic_layout">
            <style>tr,td,table,th,tbody,thead { border:0;}
            </style>
            <t t-foreach="data_list" t-as="data">
                <div class="page">
                    <table width="100%" style="height:100px;">
                        <tbody>
                            <tr>
                                <td style="padding:0px;font-size:12px;text-align:center;" colspan="2">
                                    <span t-esc="data['name']"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding:0px;text-align:left;" colspan="2">
                                    <div style="background-color:blue; width: 130px;height: 30px; overflow: hidden;">
                                        <img style="margin-top: -0px; margin-left: -34px;" t-att-src="data['barcode']"
                                             width="198px"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding:0px;font-size:10px;text-align:center;" colspan="2">
                                    <span t-esc="data['barcode_no']"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding:0px;font-size:11px;text-align:center;" width="50%">
                                    <span t-esc="data['color']"/>
                                </td>
                                <td style="padding:0px;font-size:11px;text-align:center;" width="50%">
                                    <span t-esc="data['size']"/>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding:0px;font-size:11px;text-align:center;" width="50%">
                                    <span t-esc="'{:,.0f} د.ل'.format(round(data['price'],2))"/>
                                </td>
                                <td style="padding:0px;font-size:10px;text-align:center;" width="50%">
                                    <span t-esc="data['ref']"/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p style="page-break-after: always;"></p>
            </t>
        </t>
    </template>
    <!-- Dynamic Barcode Label Data Print Report Action -->
    <record id="sh_barcode_cubes_report_action" model="ir.actions.report">
        <field name="name">Product Dynamic Barcode Label</field>
        <field name="model">report.cubes_barcode_module.sh_barcode_cubes_report_doc</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">cubes_barcode_module.sh_barcode_cubes_report_doc</field>
        <field name="print_report_name">'Product Dynamic Barcode Label'</field>
        <field name="paperformat_id" ref="cubes_barcode_module.paperformat_standard_cubes_bc"/>
    </record>
</odoo>
