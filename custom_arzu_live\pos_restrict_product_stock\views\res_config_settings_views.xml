<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--This record for adding some fields to configuration settings for showing quantity and restricting out-of-stock product -->
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">
            res.config.settings.view.form.inherit.pos.restrict.product.stock
        </field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id"
               ref="point_of_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
             <xpath expr="//block[@id='pos_interface_section']" position="after">
                <div class="row mt16 o_settings_container">
                    <h2>Stock Configuration</h2>
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_left_pane">
                            <field name="is_display_stock" readonly="False"/>
                        </div>
                        <div class="o_setting_right_pane">
                            <label for="is_display_stock"/>
                            <div class="text-muted">
                                Display the quantity of the product based on
                                the
                                stock type.
                            </div>
                        </div>
                        <br/>
                        <div>
                            <div class="o_setting_left_pane">
                                <field name="is_restrict_product"
                                       readonly="False"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="is_restrict_product"/>
                                <div class="text-muted">
                                    Restrict the ordering of out of stock
                                    products based on the display quantity.
                                </div>
                            </div>
                            <br/>
                            <div class="o_setting_right_pane"
                                 invisible="not is_display_stock or not is_restrict_product">
                                <label for="stock_type"/>
                                <div class="content-group">
                                    <div class="mt16">
                                        <field name="stock_type"
                                               class="o_light_label"
                                               widget="radio"
                                               readonly="False"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
