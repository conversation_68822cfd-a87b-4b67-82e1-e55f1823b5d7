===============================
Stock Quantity History Location
===============================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:ca7e9630df386d8675dc9260019cba9271a37ccbdea7fbecb649c2ca4120ae82
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fstock--logistics--reporting-lightgray.png?logo=github
    :target: https://github.com/OCA/stock-logistics-reporting/tree/17.0/stock_quantity_history_location
    :alt: OCA/stock-logistics-reporting
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/stock-logistics-reporting-17-0/stock-logistics-reporting-17-0-stock_quantity_history_location
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/stock-logistics-reporting&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module allows to run an Inventory report or Inventory Valuation
report by location, for a past date or for current date.

**Table of contents**

.. contents::
   :local:

Usage
=====

First, you need to activate Storage Locations. To do so, go to Inventory
/ Configuration / Settings / Warehouse and activate it.

To use this module, go to:

-  *Inventory / Reporting / Locations*

-  Select Inventory at Date & Location

-  Filter by location

-  **Optionally: Mark if you want to include child location**

-  Choose a moment in time:

   -  Current Inventory
   -  At a Specific Date

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/stock-logistics-reporting/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/stock-logistics-reporting/issues/new?body=module:%20stock_quantity_history_location%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* ForgeFlow

Contributors
------------

-  Jordi Ballester Alomar <<EMAIL>>
-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Ernesto Tejeda

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-luisg123v| image:: https://github.com/luisg123v.png?size=40px
    :target: https://github.com/luisg123v
    :alt: luisg123v
.. |maintainer-rolandojduartem| image:: https://github.com/rolandojduartem.png?size=40px
    :target: https://github.com/rolandojduartem
    :alt: rolandojduartem

Current `maintainers <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-luisg123v| |maintainer-rolandojduartem| 

This module is part of the `OCA/stock-logistics-reporting <https://github.com/OCA/stock-logistics-reporting/tree/17.0/stock_quantity_history_location>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
