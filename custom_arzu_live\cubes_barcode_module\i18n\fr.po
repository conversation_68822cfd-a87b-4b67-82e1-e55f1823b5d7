# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cubes_barcode_module
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-12-10 07:02+0000\n"
"PO-Revision-Date: 2020-12-10 07:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cubes_barcode_module
#: model:ir.actions.report,print_report_name:cubes_barcode_module.sh_barcode_report_action
msgid "'Product Dynamic Barcode Label'"
msgstr "'Étiquette de code à barres dynamique du produit'"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__after
msgid "After"
msgstr "Après"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__barcode
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_barcode_report_doc
msgid "Barcode"
msgstr "code à barre"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_field_id
msgid "Barcode Field"
msgstr "Champ de code à barres"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_height
msgid "Barcode Height"
msgstr "Hauteur du code à barres"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__quantity
msgid "Barcode Quantity"
msgstr "Nombre de code à barres"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_report_cubes_barcode_module_sh_barcode_report_doc
msgid "Barcode Report"
msgstr "Rapport de code à barres"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_width
msgid "Barcode Width"
msgstr "Largeur du code à barres"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_print_barcode_or_qr
msgid "Barcode/QR Type"
msgstr "Type de code à barres / QR"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__before
msgid "Before"
msgstr "Avant"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Cancel"
msgstr "Annuler"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__center
msgid "Center"
msgstr "Centre"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code128
msgid "Code 128"
msgstr "Code 128"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code11
msgid "Code11"
msgstr "Code11"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__company_id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__company_id
msgid "Company"
msgstr "Société"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_date
msgid "Created on"
msgstr "Créé sur"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__currency_id
msgid "Currency"
msgstr "Monnaie"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_currency_position
msgid "Currency Symbol Position"
msgstr "Position du symbole monétaire"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__display_name
msgid "Display Name"
msgstr "Afficher un nom"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label"
msgstr "Étiquette de code à barres dynamique"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label Line"
msgstr "Ligne d'étiquette de code à barres dynamique"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__lable_id
msgid "Dynamic Label"
msgstr "Étiquette dynamique"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template
msgid "Dynamic Label Template"
msgstr "Modèle d'étiquette dynamique"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_print_line_ids
msgid "Dynamic Lable Line"
msgstr "Ligne Lable dynamique"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print
msgid "Dynamic Lable Print"
msgstr "Impression de la location dynamique"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print_line
msgid "Dynamic Lable Print Line"
msgstr "Ligne d'impression à la location dynamique"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.account_move_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_product_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_template_action
#: model:ir.actions.act_window,name:cubes_barcode_module.purchase_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sale_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_action_product_label_wizard
#: model:ir.actions.act_window,name:cubes_barcode_module.stock_picking_action
msgid "Dynamic Product Label"
msgstr "Étiquette de produit dynamique"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_product_lable_template_action
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_lable_template_tree_view
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Product Label Template"
msgstr "Modèle d'étiquette de produit dynamique"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean13
msgid "EAN-13"
msgstr "Ean-13"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean8
msgid "EAN-8"
msgstr "Ean-8"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended39
msgid "Extended39"
msgstr "Extended39"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended93
msgid "Extended93"
msgstr "Extended93"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__fim
msgid "FIM"
msgstr "Fim"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_field_id
msgid "Field"
msgstr "Champ"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__ttype
msgid "Field Type"
msgstr "Type de champ"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_bold
msgid "Font Bold"
msgstr "Caractères gras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_color
msgid "Font Color"
msgstr "Couleur de la police"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_italic
msgid "Font Italic"
msgstr "Polices italique"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_size
msgid "Font Size"
msgstr "Taille de police"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_underline
msgid "Font Underline"
msgstr "Police soulignée"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__i2of5
msgid "I2of5"
msgstr "I2of5"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__id
msgid "ID"
msgstr "identifiant"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_label_display
msgid "Is Label Display ?"
msgstr "L'étiquette est-elle affichée?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_is_price_field
msgid "Is Price Field ?"
msgstr "Est le champ de prix?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__is_valid
msgid "Is Valid ?"
msgstr "Est valable ?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_template_id
msgid "Label Print Template"
msgstr "Modèle d'impression d'étiquettes"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__landscape
msgid "Landscape"
msgstr "Paysage"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line____last_update
msgid "Last Modified on"
msgstr "Dernière modification sur"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__left
msgid "Left"
msgstr "La gauche"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__msi
msgid "MSI"
msgstr "MSI"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_margin
msgid "Margin"
msgstr "Marge"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__name
msgid "Name"
msgstr "Nom"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "PDF Page Configuration"
msgstr "Configuration de la page PDF"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_height
msgid "PDF Page Height"
msgstr "PDF page Hauteur"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_width
msgid "PDF Page Width"
msgstr "PDF Largeur de la page"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__postnet
msgid "POSTNET"
msgstr "Postnet"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_orientation
msgid "Page Orientation"
msgstr "Orientation de la page"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__paperformat_id
msgid "Paper Format"
msgstr "Format de papier"

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid ""
"Please select fields which you have defined in dynamic barcode lines, If you"
" have not added pls add field first in dynamic barcode label lines."
msgstr ""
"Veuillez sélectionner les champs que vous avez définis dans les lignes de codes-barres dynamiques, si vous"
" n'ont pas ajouté pls ajouter le champ d'abord dans les lignes d'étiquette de code à barres dynamiques. "

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__portrait
msgid "Portrait"
msgstr "Portrait"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_position
msgid "Position"
msgstr "Position"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Print"
msgstr "Imprimer"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__product_id
msgid "Product"
msgstr "Produit"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product & Barcode Quantity"
msgstr "Quantité de produits et de code à barres"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_main
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_sub
msgid "Product Barcode Label"
msgstr "Étiquette de code à barres de produit"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template
#: model:res.groups,name:cubes_barcode_module.group_product_barcode_label_template
msgid "Product Barcode Label Template"
msgstr "Modèle d'étiquette de code à barres de produit"

#. module: cubes_barcode_module
#: model:ir.actions.report,name:cubes_barcode_module.sh_barcode_report_action
msgid "Product Dynamic Barcode Label"
msgstr "Étiquette de code à barres dynamique de produit"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product Dynamic Label Print"
msgstr "Étiquette dynamique de produit Imprimer"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__qr
msgid "QR"
msgstr "Qr"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__right
msgid "Right"
msgstr "Droit"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard39
msgid "Standard39"
msgstr "Standard39"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard93
msgid "Standard93"
msgstr "Standard93"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__template_id
msgid "Template"
msgstr "Modèle"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_template_line_ids
msgid "Template Line"
msgstr "Ligne de gabarit"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template_line
msgid "Template Lines"
msgstr "Lignes de modèle"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_type
msgid "Type of Barcode"
msgstr "Type de code à barres"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__upca
msgid "UPCA"
msgstr "Upca"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__usps_4state
msgid "USPS_4State"
msgstr "USPS_4State"

#. module: cubes_barcode_module
#: model:ir.model.fields,help:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Used to handle lines."
msgstr "Utilisé pour gérer des lignes."

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid "You can not Add Duplicate Fields"
msgstr "Vous ne pouvez pas ajouter de champs en double"
