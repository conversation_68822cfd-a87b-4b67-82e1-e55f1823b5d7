# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import base64

from odoo import fields, api, models


class PosConfig(models.Model):
    _inherit = 'pos.config'

    is_print_partner = fields.<PERSON><PERSON>an('Print Partner in Receipt')
    enable_double_printing = fields.Boolean('Enable Double Printing', default=False, 
                                          help='When enabled, receipts will be printed twice automatically')


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _loader_params_product_product(self):
        result = super()._loader_params_product_product()
        result['search_params']['fields'].append('is_discount_product')
        return result

    # def _loader_params_pos_config(self):
    #     result = super()._loader_params_pos_config()
    #     result['search_params']['fields'].extend(['enable_double_printing'])
    #     return result
