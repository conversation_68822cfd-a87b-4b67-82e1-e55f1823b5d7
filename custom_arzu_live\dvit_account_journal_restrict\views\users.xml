<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_users_form_inherit" model="ir.ui.view">
        <field name="name">view_users_form_inherit</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='preferences']" position="after">
                <page string="Journal Restrictions"
                      options="{'invisible': [('id','=', 1)]}">
                    <group>
                        <field name="journal_ids"
                               widget="many2many_tags"
                               domain="[('company_id','=',company_id)]"
                               context="{'default_company_id':company_id}"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
