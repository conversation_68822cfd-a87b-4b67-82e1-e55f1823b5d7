# -*- coding: utf-8 -*-
# from odoo import http


# class CubesSkyexpressIntegration(http.Controller):
#     @http.route('/cubes_skyexpress_integration/cubes_skyexpress_integration', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/cubes_skyexpress_integration/cubes_skyexpress_integration/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('cubes_skyexpress_integration.listing', {
#             'root': '/cubes_skyexpress_integration/cubes_skyexpress_integration',
#             'objects': http.request.env['cubes_skyexpress_integration.cubes_skyexpress_integration'].search([]),
#         })

#     @http.route('/cubes_skyexpress_integration/cubes_skyexpress_integration/objects/<model("cubes_skyexpress_integration.cubes_skyexpress_integration"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('cubes_skyexpress_integration.object', {
#             'object': obj
#         })

