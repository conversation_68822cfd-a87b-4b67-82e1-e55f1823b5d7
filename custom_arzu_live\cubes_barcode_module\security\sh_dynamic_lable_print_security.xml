<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_product_barcode_label_template" model="res.groups">
        <field name="name">Product Barcode Label Template</field>
        <field name="category_id" ref="base.module_category_hidden" />
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]" />
    </record>
    <record model="ir.rule" id="product_dynamic_label_comp_rule">
        <field name="name">Product Dynamic Label multi-company</field>
        <field name="model_id" ref="model_sh_dynamic_template" />
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>
    <record model="ir.rule" id="product_dynamic_label_line_comp_rule">
        <field name="name">Product Dynamic Label line multi-company</field>
        <field name="model_id" ref="model_sh_dynamic_template_line" />
        <field name="domain_force">[('company_id', 'in', company_ids)]</field>
    </record>
</odoo>
