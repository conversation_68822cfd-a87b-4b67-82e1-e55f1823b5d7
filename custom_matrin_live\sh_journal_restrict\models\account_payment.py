# -*- coding: utf-8 -*-

from odoo import api, fields, models
from odoo.osv import expression

class AccountPayment(models.Model):
    _inherit = 'account.payment'

    user_journal_ids = fields.Many2many('account.journal',compute="_compute_user_journal_ids")

    @api.depends('partner_id')
    def _compute_user_journal_ids(self):
        for rec in self:
            if self.env.user.has_group('sh_journal_restrict.restrict_journal_for_user_group'):
                if self.env.user.journal_ids:
                    rec.user_journal_ids = self.env.user.journal_ids.ids
                else:
                    rec.user_journal_ids = False
            else:
                journal = self.env['account.journal'].search([])
                if journal:
                    rec.user_journal_ids = journal.ids
                else:
                    rec.user_journal_ids = False


