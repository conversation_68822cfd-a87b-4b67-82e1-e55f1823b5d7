<?xml version="1.0" encoding="UTF-8" ?>
<!--
Copyright (C) 2016-Today: GRAP (http://www.grap.coop)
Copyright (C) 2016-Today GRAP (http://www.lalouve.net)
<AUTHOR> LE GAL (https://twitter.com/legalsylvain)
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<odoo>

    <record id="view_product_product_form" model="ir.ui.view">
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view" />
        <field name="arch" type="xml">
            <field name="barcode" position="before">
                <field
                    name="barcode_rule_id"
                    options="{'no_create': True}"
                    domain="[('generate_model', '=', 'product.product')]"
                    groups="barcodes_generator_abstract.generate_barcode"
                    colspan="2"
                    widget="selection"
                />
                <field name="generate_type" invisible="1" />
                <label
                    for="barcode_base"
                    invisible="not barcode_rule_id"
                    groups="barcodes_generator_abstract.generate_barcode"
                />
                <div
                    name="div_barcode_base"
                    class="o_row"
                    invisible="not barcode_rule_id"
                    groups="barcodes_generator_abstract.generate_barcode"
                >
                    <field
                        name="barcode_base"
                        readonly="generate_type != 'manual'"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                    <button
                        name="generate_base"
                        type="object"
                        string="Generate Base"
                        help="Generate Base (Using Sequence)"
                        invisible="generate_type != 'sequence' or barcode_base != 0"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                </div>
            </field>

            <field name="barcode" position="attributes">
                <attribute name="invisible">True</attribute>
            </field>
            <field name="barcode" position="after">
                <label for="barcode" />
                <div name="div_barcode" class="o_row">
                    <field
                        name="barcode"
                        class="oe_inline"
                        readonly="generate_type == 'sequence'"
                    />
                    <button
                        name="generate_barcode"
                        type="object"
                        string="Generate Barcode"
                        help="Generate Barcode (Using Barcode Rule)"
                        invisible="not barcode_rule_id or barcode_base == 0"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                </div>
            </field>

        </field>
    </record>

    <record id="product_variant_easy_edit_view" model="ir.ui.view">
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_variant_easy_edit_view" />
        <field name="arch" type="xml">
            <field name="barcode" position="before">
                <field
                    name="barcode_rule_id"
                    options="{'no_create': True}"
                    domain="[('generate_model', '=', 'product.product')]"
                    groups="barcodes_generator_abstract.generate_barcode"
                    colspan="2"
                    widget="selection"
                />
                <field name="generate_type" invisible="1" />
                <label
                    for="barcode_base"
                    invisible="not barcode_rule_id"
                    groups="barcodes_generator_abstract.generate_barcode"
                />
                <div
                    name="div_barcode_base"
                    class="o_row"
                    invisible="not barcode_rule_id"
                    groups="barcodes_generator_abstract.generate_barcode"
                >
                    <field
                        name="barcode_base"
                        readonly="generate_type != 'manual'"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                    <button
                        name="generate_base"
                        type="object"
                        string="Generate Base"
                        help="Generate Base (Using Sequence)"
                        invisible="generate_type != 'sequence' or barcode_base != 0"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                </div>
            </field>

            <field name="barcode" position="attributes">
                <attribute name="invisible">True</attribute>
            </field>
            <field name="barcode" position="after">
                <label
                    for="barcode"
                    groups="barcodes_generator_abstract.generate_barcode"
                />
                <div name="div_barcode" class="o_row">
                    <field
                        name="barcode"
                        class="oe_inline"
                        readonly="generate_type == 'sequence'"
                    />
                    <button
                        name="generate_barcode"
                        type="object"
                        string="Generate Barcode"
                        help="Generate Barcode (Using Barcode Rule)"
                        invisible="not barcode_rule_id or barcode_base == 0"
                        groups="barcodes_generator_abstract.generate_barcode"
                        class="oe_inline"
                    />
                </div>
            </field>
        </field>
    </record>

</odoo>
