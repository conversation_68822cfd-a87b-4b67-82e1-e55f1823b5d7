# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
import json
from odoo import api, fields, models, _

class ProductProduct(models.Model):
    _inherit = 'product.product'
    
    qty_in_location = fields.Char(compute='_set_qty_in_location', store=False)

    def _set_qty_in_location(self):
        QUANT = self.env['stock.quant'].sudo()
        locations = QUANT.search([]).mapped('location_id')
        qty_in_locations = {}
        for rec in self:
            for location in locations:
                qty_on_hand = QUANT.search([
                    ('product_id', '=', rec.id),
                    ('location_id', '=', location.id)
                ], limit=1).inventory_quantity_auto_apply
                
                qty_in_locations[location.id] = qty_on_hand
            rec.qty_in_location = json.dumps(qty_in_locations)