from odoo import api, fields, models, _


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    first_move = fields.Boolean(default=False, copy=False)
    internal_request_id = fields.Many2one('internal.transfer.request')

    def button_validate(self):
        res = super(StockPicking, self).button_validate()
        if self.first_move and self.internal_request_id:
            # create second move and validate it
            picking_type = self.env['stock.picking.type'].sudo().browse(
                self.internal_request_id.destination_type_id.picking_type_id)
            self.internal_request_id.second_picking_id = self.env['stock.picking'].sudo().create({
                'location_id': self.internal_request_id.source_picking_type_id.default_location_dest_id.id,
                'location_dest_id': picking_type.default_location_dest_id.id,
                'company_id': self.env.company.id,
                'partner_id': self.internal_request_id.partner_id.id if self.internal_request_id.partner_id else False,
                'picking_type_id': self.internal_request_id.source_picking_type_id.id,
                'internal_request_id': self.internal_request_id.id
            })
            # create third move
            self.internal_request_id.third_picking_id = self.env['stock.picking'].sudo().create({
                'location_id': picking_type.default_location_dest_id.id,
                'location_dest_id': picking_type.default_location_src_id.id,
                'partner_id': self.internal_request_id.partner_id.id if self.internal_request_id.partner_id else False,
                'company_id': self.env.company.id,
                'picking_type_id': picking_type.id,
                'internal_request_id': self.internal_request_id.id
            })

            for line in self.internal_request_id.line_ids:
                self.env['stock.move'].sudo().create({
                    'name': line.product_id.name,
                    'product_id': line.product_id.id,
                    'product_uom_qty': line.qty,
                    'quantity': line.qty,
                    'picking_id': self.internal_request_id.second_picking_id.id,
                    'product_uom': line.product_uom.id,
                    'location_id': self.internal_request_id.second_picking_id.location_id.id,
                    'location_dest_id': self.internal_request_id.second_picking_id.location_dest_id.id,
                })
                self.env['stock.move'].sudo().create({
                    'name': line.product_id.name,
                    'product_id': line.product_id.id,
                    'product_uom_qty': line.qty,
                    'quantity': line.qty,
                    'picking_id': self.internal_request_id.third_picking_id.id,
                    'product_uom': line.product_uom.id,
                    'location_id': self.internal_request_id.third_picking_id.location_id.id,
                    'location_dest_id': self.internal_request_id.third_picking_id.location_dest_id.id,
                })
            self.internal_request_id.second_picking_id.sudo().action_confirm()
            # self.internal_request_id.third_picking_id.sudo().action_confirm()
            self.internal_request_id.second_picking_id.sudo().button_validate()

        return res
