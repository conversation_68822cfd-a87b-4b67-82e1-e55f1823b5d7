<?xml version="1.0" encoding="UTF-8"?>
<odoo>
	<record id="sh_product_template_view_tree_inherit" model="ir.ui.view">
        <field name="name">product.template.tree.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
        	<field name="list_price" position="after">
        		<field name="sh_price_include_tax" optional="show" widget="monetary" options="{'currency_field': 'currency_id', 'field_digits': True}"/>
        	</field>
        	<field name="standard_price" position="after">
        		<field name="sh_cost_include_tax" optional="show" options="{'currency_field': 'cost_currency_id', 'field_digits': True}"/>
        	</field>
        </field>
    </record>
    <record id="sh_product_product_view_tree_inherit" model="ir.ui.view">
        <field name="name">product.product.tree.inherit</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_product_tree_view"/>
        <field name="arch" type="xml">
        	<field name="lst_price" position="after">
        		<field name="sh_price_include_tax" optional="show" widget="monetary"/>
        		<field name="sh_qr_code_image" invisible="1"/>
        	</field>
        	<field name="standard_price" position="after">
        		<field name="sh_cost_include_tax" optional="show" widget="monetary"/>
        	</field>
        </field>
    </record>
    <record id="sh_product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">sh.product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='pricing']" position="after">
                <field name="sh_price_include_tax" widget="monetary" options="{'currency_field': 'currency_id', 'field_digits': True}"/>
                <field name="sh_cost_include_tax" widget='monetary' options="{'currency_field': 'cost_currency_id', 'field_digits': True}" groups="base.group_user" invisible="[('product_variant_count', '>', 1), ('is_product_variant', '=', False)]"/>
            </xpath>
            <field name="company_id" position="before">
            	<field name="sh_qr_code_image" invisible="1"/>
            </field>
        </field>
    </record>
</odoo>