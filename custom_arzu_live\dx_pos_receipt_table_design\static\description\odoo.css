@import url("https://fonts.googleapis.com/css?family=Roboto:400,400i,700,700i&display=swap");

/* <inline asset> defined in bundle 'web.assets_frontend' */
@charset "UTF-8"; 

/* /web/static/lib/bootstrap/scss/_functions.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/src/scss/bs_mixins_overrides.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/src/scss/utils.scss defined in bundle 'web.assets_frontend' */
  .o_nocontent_help .o_empty_folder_image:before{content: ""; display: block; margin: auto; background-size: cover;}.o_nocontent_help .o_empty_folder_image:before{width: 120px; height: 80px; margin-top: 30px; margin-bottom: 30px; background: transparent url(/web/static/src/img/empty_folder.svg) no-repeat center;}

/* /web/static/src/scss/primary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /web_editor/static/src/scss/web_editor.variables.scss defined in bundle 'web.assets_frontend' */
 

/* /mail/static/src/scss/variables.scss defined in bundle 'web.assets_frontend' */
 

/* /portal/static/src/scss/primary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/primary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/options/user_values.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/options/colors/user_color_palette.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/options/colors/user_theme_color_palette.scss defined in bundle 'web.assets_frontend' */
 

/* /website_sale/static/src/scss/primary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /account/static/src/scss/variables.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /web_editor/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/src/scss/secondary_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/user_custom_bootstrap_overridden.scss defined in bundle 'web.assets_frontend' */
 

/* /website/static/src/scss/bootstrap_overridden.scss defined in bundle 'web.assets_frontend' */
 

/* /portal/static/src/scss/bootstrap_overridden.scss defined in bundle 'web.assets_frontend' */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/src/scss/bootstrap_overridden_frontend.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/lib/bootstrap/scss/_variables.scss defined in bundle 'web.assets_frontend' */
 

/* /web/static/src/scss/import_bootstrap.scss defined in bundle 'web.assets_frontend' */
 :root{--blue: #007bff; --indigo: #6610f2; --purple: #6f42c1; --pink: #e83e8c; --red: #dc3545; --orange: #fd7e14; --yellow: #ffc107; --green: #28a745; --teal: #20c997; --cyan: #17a2b8; --white: #FFFFFF; --gray: #6c757d; --gray-dark: #343a40; --body: white; --menu: #F9F9F9; --footer: #F9F9F9; --primary: #00A09D; --secondary: #875A7B; --success: #28a745; --info: #17a2b8; --warning: #ffc107; --danger: #dc3545; --light: #f6f9f9; --dark: #141f1e; --alpha: #00A09D; --beta: #875A7B; --gamma: #5C5B80; --delta: #5B899E; --epsilon: #E46F78; --breakpoint-xs: 0; --breakpoint-sm: 576px; --breakpoint-md: 768px; --breakpoint-lg: 992px; --breakpoint-xl: 1200px; --font-family-sans-serif: "Roboto", "Odoo Unicode Support Noto", sans-serif; --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}*, *::before, *::after{box-sizing: border-box;}html{font-family: sans-serif; line-height: 1.15; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: transparent;}article, aside, figcaption, figure, footer, header, hgroup, main, nav, section{display: block;}body{margin: 0; font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #212529; text-align: left; background-color: white;}[tabindex="-1"]:focus{outline: 0 !important;}hr{box-sizing: content-box; height: 0; overflow: visible;}h1, h2, h3, h4, h5, h6{margin-top: 0; margin-bottom: 0.5rem;}p{margin-top: 0; margin-bottom: 1rem;}abbr[title], abbr[data-original-title]{text-decoration: underline; text-decoration: underline dotted; cursor: help; border-bottom: 0; text-decoration-skip-ink: none;}address{margin-bottom: 1rem; font-style: normal; line-height: inherit;}ol, ul, dl{margin-top: 0; margin-bottom: 1rem;}ol ol, ul ul, ol ul, ul ol{margin-bottom: 0;}dt{font-weight: 700;}dd{margin-bottom: .5rem; margin-left: 0;}blockquote{margin: 0 0 1rem;}b, strong{font-weight: bolder;}small{font-size: 80%;}sub, sup{position: relative; font-size: 75%; line-height: 0; vertical-align: baseline;}sub{bottom: -.25em;}sup{top: -.5em;}a{color: #00A09D; text-decoration: none; background-color: transparent;}a:hover{color: #005452; text-decoration: underline;}a:not([href]):not([tabindex]){color: inherit; text-decoration: none;}a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus{color: inherit; text-decoration: none;}a:not([href]):not([tabindex]):focus{outline: 0;}pre, code, kbd, samp{font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 1em;}pre{margin-top: 0; margin-bottom: 1rem; overflow: auto;}figure{margin: 0 0 1rem;}img{vertical-align: middle; border-style: none;}svg{overflow: hidden; vertical-align: middle;}table{border-collapse: collapse;}caption{padding-top: 0.75rem; padding-bottom: 0.75rem; color: #6c757d; text-align: left; caption-side: bottom;}th{text-align: inherit;}label{display: inline-block; margin-bottom: 0.5rem;}button{border-radius: 0;}button:focus{outline: 1px dotted; outline: 5px auto -webkit-focus-ring-color;}input, button, select, optgroup, textarea{margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}button, input{overflow: visible;}button, select{text-transform: none;}select{word-wrap: normal;}button, [type="button"], [type="reset"], [type="submit"]{-webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled){cursor: pointer;}button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{padding: 0; border-style: none;}input[type="radio"], input[type="checkbox"]{box-sizing: border-box; padding: 0;}input[type="date"], input[type="time"], input[type="datetime-local"], input[type="month"]{-webkit--webkit-appearance: listbox; -moz-appearance: listbox; appearance: listbox;}textarea{overflow: auto; resize: vertical;}fieldset{min-width: 0; padding: 0; margin: 0; border: 0;}legend{display: block; width: 100%; max-width: 100%; padding: 0; margin-bottom: .5rem; font-size: 1.5rem; line-height: inherit; color: inherit; white-space: normal;}@media (max-width: 1200px){legend{font-size: calc(1.275rem + 0.3vw) ;}}progress{vertical-align: baseline;}[type="number"]::-webkit-inner-spin-button, [type="number"]::-webkit-outer-spin-button{height: auto;}[type="search"]{outline-offset: -2px; -webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}[type="search"]::-webkit-search-decoration{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}::-webkit-file-upload-button{font: inherit; -webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}output{display: inline-block;}summary{display: list-item; cursor: pointer;}template{display: none;}[hidden]{display: none !important;}h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6{margin-bottom: 0.5rem; font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif; font-weight: 500; line-height: 1.2;}h1, .h1{font-size: 2.1875rem;}@media (max-width: 1200px){h1, .h1{font-size: calc(1.34375rem + 1.125vw) ;}}h2, .h2{font-size: 1.75rem;}@media (max-width: 1200px){h2, .h2{font-size: calc(1.3rem + 0.6vw) ;}}h3, .h3{font-size: 1.53125rem;}@media (max-width: 1200px){h3, .h3{font-size: calc(1.278125rem + 0.3375vw) ;}}h4, .h4{font-size: 1.3125rem;}@media (max-width: 1200px){h4, .h4{font-size: calc(1.25625rem + 0.075vw) ;}}h5, .h5{font-size: 1.09375rem;}h6, .h6{font-size: 0.875rem;}.lead{font-size: 1.09375rem; font-weight: 300;}.display-1{font-size: 6rem; font-weight: 300; line-height: 1.2;}@media (max-width: 1200px){.display-1{font-size: calc(1.725rem + 5.7vw) ;}}.display-2{font-size: 5.5rem; font-weight: 300; line-height: 1.2;}@media (max-width: 1200px){.display-2{font-size: calc(1.675rem + 5.1vw) ;}}.display-3{font-size: 4.5rem; font-weight: 300; line-height: 1.2;}@media (max-width: 1200px){.display-3{font-size: calc(1.575rem + 3.9vw) ;}}.display-4{font-size: 3.5rem; font-weight: 300; line-height: 1.2;}@media (max-width: 1200px){.display-4{font-size: calc(1.475rem + 2.7vw) ;}}hr{margin-top: 1rem; margin-bottom: 1rem; border: 0; border-top: 1px solid rgba(0, 0, 0, 0.1);}small, .small{font-size: 80%; font-weight: 400;}mark, .mark{padding: 0.2em; background-color: #fcf8e3;}.list-unstyled{padding-left: 0; list-style: none;}.list-inline{padding-left: 0; list-style: none;}.list-inline-item{display: inline-block;}.list-inline-item:not(:last-child){margin-right: 0.5rem;}.initialism{font-size: 90%; text-transform: uppercase;}.blockquote{margin-bottom: 1rem; font-size: 1.09375rem;}.blockquote-footer{display: block; font-size: 80%; color: #6c757d;}.blockquote-footer::before{content: "\2014\00A0";}.img-fluid{max-width: 100%; height: auto;}.img-thumbnail{padding: 0.25rem; background-color: white; border: 1px solid #dee2e6; border-radius: 0.25rem; max-width: 100%; height: auto;}.figure{display: inline-block;}.figure-img{margin-bottom: 0.5rem; line-height: 1;}.figure-caption{font-size: 90%; color: #6c757d;}code{font-size: 87.5%; color: #e83e8c; word-break: break-word;}a > code{color: inherit;}kbd{padding: 0.2rem 0.4rem; font-size: 87.5%; color: #FFFFFF; background-color: #212529; border-radius: 0.2rem;}kbd kbd{padding: 0; font-size: 100%; font-weight: 700;}pre{display: block; font-size: 87.5%; color: #212529;}pre code{font-size: inherit; color: inherit; word-break: normal;}.pre-scrollable{max-height: 340px; overflow-y: scroll;}.container{width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;}@media (min-width: 576px){.container{max-width: 540px;}}@media (min-width: 768px){.container{max-width: 720px;}}@media (min-width: 992px){.container{max-width: 960px;}}@media (min-width: 1200px){.container{max-width: 1140px;}}.container-fluid{width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;}.row{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-right: -15px; margin-left: -15px;}.no-gutters{margin-right: 0; margin-left: 0;}.no-gutters > .col, .no-gutters > [class*="col-"]{padding-right: 0; padding-left: 0;}.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col, .col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm, .col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md, .col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg, .col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl, .col-xl-auto{position: relative; width: 100%; padding-right: 15px; padding-left: 15px;}.col{flex-basis: 0;flex-grow: 1;max-width: 100%;}.col-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto; max-width: 100%;}.col-1{flex: 0 0 8.33333333%; max-width: 8.33333333%;}.col-2{flex: 0 0 16.66666667%; max-width: 16.66666667%;}.col-3{flex: 0 0 25%; max-width: 25%;}.col-4{flex: 0 0 33.33333333%; max-width: 33.33333333%;}.col-5{flex: 0 0 41.66666667%; max-width: 41.66666667%;}.col-6{flex: 0 0 50%; max-width: 50%;}.col-7{flex: 0 0 58.33333333%; max-width: 58.33333333%;}.col-8{flex: 0 0 66.66666667%; max-width: 66.66666667%;}.col-9{flex: 0 0 75%; max-width: 75%;}.col-10{flex: 0 0 83.33333333%; max-width: 83.33333333%;}.col-11{flex: 0 0 91.66666667%; max-width: 91.66666667%;}.col-12{flex: 0 0 100%; max-width: 100%;}.order-first{order: -1;}.order-last{order: 13;}.order-0{order: 0;}.order-1{order: 1;}.order-2{order: 2;}.order-3{order: 3;}.order-4{order: 4;}.order-5{order: 5;}.order-6{order: 6;}.order-7{order: 7;}.order-8{order: 8;}.order-9{order: 9;}.order-10{order: 10;}.order-11{order: 11;}.order-12{order: 12;}.offset-1{margin-left: 8.33333333%;}.offset-2{margin-left: 16.66666667%;}.offset-3{margin-left: 25%;}.offset-4{margin-left: 33.33333333%;}.offset-5{margin-left: 41.66666667%;}.offset-6{margin-left: 50%;}.offset-7{margin-left: 58.33333333%;}.offset-8{margin-left: 66.66666667%;}.offset-9{margin-left: 75%;}.offset-10{margin-left: 83.33333333%;}.offset-11{margin-left: 91.66666667%;}@media (min-width: 576px){.col-sm{flex-basis: 0; flex-grow: 1; max-width: 100%;}.col-sm-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto; max-width: 100%;}.col-sm-1{flex: 0 0 8.33333333%; max-width: 8.33333333%;}.col-sm-2{flex: 0 0 16.66666667%; max-width: 16.66666667%;}.col-sm-3{flex: 0 0 25%; max-width: 25%;}.col-sm-4{flex: 0 0 33.33333333%; max-width: 33.33333333%;}.col-sm-5{flex: 0 0 41.66666667%; max-width: 41.66666667%;}.col-sm-6{flex: 0 0 50%; max-width: 50%;}.col-sm-7{flex: 0 0 58.33333333%; max-width: 58.33333333%;}.col-sm-8{flex: 0 0 66.66666667%; max-width: 66.66666667%;}.col-sm-9{flex: 0 0 75%; max-width: 75%;}.col-sm-10{flex: 0 0 83.33333333%; max-width: 83.33333333%;}.col-sm-11{flex: 0 0 91.66666667%; max-width: 91.66666667%;}.col-sm-12{flex: 0 0 100%; max-width: 100%;}.order-sm-first{order: -1;}.order-sm-last{order: 13;}.order-sm-0{order: 0;}.order-sm-1{order: 1;}.order-sm-2{order: 2;}.order-sm-3{order: 3;}.order-sm-4{order: 4;}.order-sm-5{order: 5;}.order-sm-6{order: 6;}.order-sm-7{order: 7;}.order-sm-8{order: 8;}.order-sm-9{order: 9;}.order-sm-10{order: 10;}.order-sm-11{order: 11;}.order-sm-12{order: 12;}.offset-sm-0{margin-left: 0;}.offset-sm-1{margin-left: 8.33333333%;}.offset-sm-2{margin-left: 16.66666667%;}.offset-sm-3{margin-left: 25%;}.offset-sm-4{margin-left: 33.33333333%;}.offset-sm-5{margin-left: 41.66666667%;}.offset-sm-6{margin-left: 50%;}.offset-sm-7{margin-left: 58.33333333%;}.offset-sm-8{margin-left: 66.66666667%;}.offset-sm-9{margin-left: 75%;}.offset-sm-10{margin-left: 83.33333333%;}.offset-sm-11{margin-left: 91.66666667%;}}@media (min-width: 768px){.col-md{flex-basis: 0; flex-grow: 1; max-width: 100%;}.col-md-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto; max-width: 100%;}.col-md-1{flex: 0 0 8.33333333%; max-width: 8.33333333%;}.col-md-2{flex: 0 0 16.66666667%; max-width: 16.66666667%;}.col-md-3{flex: 0 0 25%; max-width: 25%;}.col-md-4{flex: 0 0 33.33333333%; max-width: 33.33333333%;}.col-md-5{flex: 0 0 41.66666667%; max-width: 41.66666667%;}.col-md-6{flex: 0 0 50%; max-width: 50%;}.col-md-7{flex: 0 0 58.33333333%; max-width: 58.33333333%;}.col-md-8{flex: 0 0 66.66666667%; max-width: 66.66666667%;}.col-md-9{flex: 0 0 75%; max-width: 75%;}.col-md-10{flex: 0 0 83.33333333%; max-width: 83.33333333%;}.col-md-11{flex: 0 0 91.66666667%; max-width: 91.66666667%;}.col-md-12{flex: 0 0 100%; max-width: 100%;}.order-md-first{order: -1;}.order-md-last{order: 13;}.order-md-0{order: 0;}.order-md-1{order: 1;}.order-md-2{order: 2;}.order-md-3{order: 3;}.order-md-4{order: 4;}.order-md-5{order: 5;}.order-md-6{order: 6;}.order-md-7{order: 7;}.order-md-8{order: 8;}.order-md-9{order: 9;}.order-md-10{order: 10;}.order-md-11{order: 11;}.order-md-12{order: 12;}.offset-md-0{margin-left: 0;}.offset-md-1{margin-left: 8.33333333%;}.offset-md-2{margin-left: 16.66666667%;}.offset-md-3{margin-left: 25%;}.offset-md-4{margin-left: 33.33333333%;}.offset-md-5{margin-left: 41.66666667%;}.offset-md-6{margin-left: 50%;}.offset-md-7{margin-left: 58.33333333%;}.offset-md-8{margin-left: 66.66666667%;}.offset-md-9{margin-left: 75%;}.offset-md-10{margin-left: 83.33333333%;}.offset-md-11{margin-left: 91.66666667%;}}@media (min-width: 992px){.col-lg{flex-basis: 0; flex-grow: 1; max-width: 100%;}.col-lg-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto; max-width: 100%;}.col-lg-1{flex: 0 0 8.33333333%; max-width: 8.33333333%;}.col-lg-2{flex: 0 0 16.66666667%; max-width: 16.66666667%;}.col-lg-3{flex: 0 0 25%; max-width: 25%;}.col-lg-4{flex: 0 0 33.33333333%;max-width: 33.33333333%;}.col-lg-5{flex: 0 0 41.66666667%; max-width: 41.66666667%;}.col-lg-6{flex: 0 0 50%; max-width: 50%;}.col-lg-7{flex: 0 0 58.33333333%; max-width: 58.33333333%;}.col-lg-8{flex: 0 0 66.66666667%; max-width: 66.66666667%;}.col-lg-9{flex: 0 0 75%; max-width: 75%;}.col-lg-10{flex: 0 0 83.33333333%; max-width: 83.33333333%;}.col-lg-11{flex: 0 0 91.66666667%; max-width: 91.66666667%;}.col-lg-12{flex: 0 0 100%; max-width: 100%;}.order-lg-first{order: -1;}.order-lg-last{order: 13;}.order-lg-0{order: 0;}.order-lg-1{order: 1;}.order-lg-2{order: 2;}.order-lg-3{order: 3;}.order-lg-4{order: 4;}.order-lg-5{order: 5;}.order-lg-6{order: 6;}.order-lg-7{order: 7;}.order-lg-8{order: 8;}.order-lg-9{order: 9;}.order-lg-10{order: 10;}.order-lg-11{order: 11;}.order-lg-12{order: 12;}.offset-lg-0{margin-left: 0;}.offset-lg-1{margin-left: 8.33333333%;}.offset-lg-2{margin-left: 16.66666667%;}.offset-lg-3{margin-left: 25%;}.offset-lg-4{margin-left: 33.33333333%;}.offset-lg-5{margin-left: 41.66666667%;}.offset-lg-6{margin-left: 50%;}.offset-lg-7{margin-left: 58.33333333%;}.offset-lg-8{margin-left: 66.66666667%;}.offset-lg-9{margin-left: 75%;}.offset-lg-10{margin-left: 83.33333333%;}.offset-lg-11{margin-left: 91.66666667%;}}@media (min-width: 1200px){.col-xl{flex-basis: 0; flex-grow: 1; max-width: 100%;}.col-xl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto; max-width: 100%;}.col-xl-1{flex: 0 0 8.33333333%; max-width: 8.33333333%;}.col-xl-2{flex: 0 0 16.66666667%; max-width: 16.66666667%;}.col-xl-3{flex: 0 0 25%; max-width: 25%;}.col-xl-4{flex: 0 0 33.33333333%; max-width: 33.33333333%;}.col-xl-5{flex: 0 0 41.66666667%; max-width: 41.66666667%;}.col-xl-6{flex: 0 0 50%; max-width: 50%;}.col-xl-7{flex: 0 0 58.33333333%; max-width: 58.33333333%;}.col-xl-8{flex: 0 0 66.66666667%; max-width: 66.66666667%;}.col-xl-9{flex: 0 0 75%; max-width: 75%;}.col-xl-10{flex: 0 0 83.33333333%; max-width: 83.33333333%;}.col-xl-11{flex: 0 0 91.66666667%; max-width: 91.66666667%;}.col-xl-12{flex: 0 0 100%; max-width: 100%;}.order-xl-first{order: -1;}.order-xl-last{order: 13;}.order-xl-0{order: 0;}.order-xl-1{order: 1;}.order-xl-2{order: 2;}.order-xl-3{order: 3;}.order-xl-4{order: 4;}.order-xl-5{order: 5;}.order-xl-6{order: 6;}.order-xl-7{order: 7;}.order-xl-8{order: 8;}.order-xl-9{order: 9;}.order-xl-10{order: 10;}.order-xl-11{order: 11;}.order-xl-12{order: 12;}.offset-xl-0{margin-left: 0;}.offset-xl-1{margin-left: 8.33333333%;}.offset-xl-2{margin-left: 16.66666667%;}.offset-xl-3{margin-left: 25%;}.offset-xl-4{margin-left: 33.33333333%;}.offset-xl-5{margin-left: 41.66666667%;}.offset-xl-6{margin-left: 50%;}.offset-xl-7{margin-left: 58.33333333%;}.offset-xl-8{margin-left: 66.66666667%;}.offset-xl-9{margin-left: 75%;}.offset-xl-10{margin-left: 83.33333333%;}.offset-xl-11{margin-left: 91.66666667%;}}.table{width: 100%; margin-bottom: 1rem; color: #212529;}.table th, .table td{padding: 0.75rem; vertical-align: top; border-top: 1px solid #dee2e6;}.table thead th{vertical-align: bottom; border-bottom: 2px solid #dee2e6;}.table tbody + tbody{border-top: 2px solid #dee2e6;}.table-sm th, .table-sm td{padding: 0.3rem;}.table-bordered{border: 1px solid #dee2e6;}.table-bordered th, .table-bordered td{border: 1px solid #dee2e6;}.table-bordered thead th, .table-bordered thead td{border-bottom-width: 2px;}.table-borderless th, .table-borderless td, .table-borderless thead th, .table-borderless tbody + tbody{border: 0;}.table-striped tbody tr:nth-of-type(odd){background-color: rgba(0, 0, 0, 0.05);}.table-hover tbody tr:hover{color: #212529; background-color: rgba(0, 0, 0, 0.075);}.table-primary, .table-primary > th, .table-primary > td{background-color: #b8e4e4;}.table-primary th, .table-primary td, .table-primary thead th, .table-primary tbody + tbody{border-color: #7acecc;}.table-hover .table-primary:hover{background-color: #a6dddd;}.table-hover .table-primary:hover > td, .table-hover .table-primary:hover > th{background-color: #a6dddd;}.table-secondary, .table-secondary > th, .table-secondary > td{background-color: #ddd1da;}.table-secondary th, .table-secondary td, .table-secondary thead th, .table-secondary tbody + tbody{border-color: #c1a9ba;}.table-hover .table-secondary:hover{background-color: #d2c2ce;}.table-hover .table-secondary:hover > td, .table-hover .table-secondary:hover > th{background-color: #d2c2ce;}.table-success, .table-success > th, .table-success > td{background-color: #c3e6cb;}.table-success th, .table-success td, .table-success thead th, .table-success tbody + tbody{border-color: #8fd19e;}.table-hover .table-success:hover{background-color: #b1dfbb;}.table-hover .table-success:hover > td, .table-hover .table-success:hover > th{background-color: #b1dfbb;}.table-info, .table-info > th, .table-info > td{background-color: #bee5eb;}.table-info th, .table-info td, .table-info thead th, .table-info tbody + tbody{border-color: #86cfda;}.table-hover .table-info:hover{background-color: #abdde5;}.table-hover .table-info:hover > td, .table-hover .table-info:hover > th{background-color: #abdde5;}.table-warning, .table-warning > th, .table-warning > td{background-color: #ffeeba;}.table-warning th, .table-warning td, .table-warning thead th, .table-warning tbody + tbody{border-color: #ffdf7e;}.table-hover .table-warning:hover{background-color: #ffe8a1;}.table-hover .table-warning:hover > td, .table-hover .table-warning:hover > th{background-color: #ffe8a1;}.table-danger, .table-danger > th, .table-danger > td{background-color: #f5c6cb;}.table-danger th, .table-danger td, .table-danger thead th, .table-danger tbody + tbody{border-color: #ed969e;}.table-hover .table-danger:hover{background-color: #f1b0b7;}.table-hover .table-danger:hover > td, .table-hover .table-danger:hover > th{background-color: #f1b0b7;}.table-light, .table-light > th, .table-light > td{background-color: #fcfdfd;}.table-light th, .table-light td, .table-light thead th, .table-light tbody + tbody{border-color: #fafcfc;}.table-hover .table-light:hover{background-color: #edf3f3;}.table-hover .table-light:hover > td, .table-hover .table-light:hover > th{background-color: #edf3f3;}.table-dark, .table-dark > th, .table-dark > td{background-color: #bdc0c0;}.table-dark th, .table-dark td, .table-dark thead th, .table-dark tbody + tbody{border-color: #858a8a;}.table-hover .table-dark:hover{background-color: #b0b4b4;}.table-hover .table-dark:hover > td, .table-hover .table-dark:hover > th{background-color: #b0b4b4;}.table-alpha, .table-alpha > th, .table-alpha > td{background-color: #b8e4e4;}.table-alpha th, .table-alpha td, .table-alpha thead th, .table-alpha tbody + tbody{border-color: #7acecc;}.table-hover .table-alpha:hover{background-color: #a6dddd;}.table-hover .table-alpha:hover > td, .table-hover .table-alpha:hover > th{background-color: #a6dddd;}.table-beta, .table-beta > th, .table-beta > td{background-color: #ddd1da;}.table-beta th, .table-beta td, .table-beta thead th, .table-beta tbody + tbody{border-color: #c1a9ba;}.table-hover .table-beta:hover{background-color: #d2c2ce;}.table-hover .table-beta:hover > td, .table-hover .table-beta:hover > th{background-color: #d2c2ce;}.table-gamma, .table-gamma > th, .table-gamma > td{background-color: #d1d1db;}.table-gamma th, .table-gamma td, .table-gamma thead th, .table-gamma tbody + tbody{border-color: #aaaabd;}.table-hover .table-gamma:hover{background-color: #c3c3d0;}.table-hover .table-gamma:hover > td, .table-hover .table-gamma:hover > th{background-color: #c3c3d0;}.table-delta, .table-delta > th, .table-delta > td{background-color: #d1dee4;}.table-delta th, .table-delta td, .table-delta thead th, .table-delta tbody + tbody{border-color: #aac2cd;}.table-hover .table-delta:hover{background-color: #c1d2db;}.table-hover .table-delta:hover > td, .table-hover .table-delta:hover > th{background-color: #c1d2db;}.table-epsilon, .table-epsilon > th, .table-epsilon > td{background-color: #f7d7d9;}.table-epsilon th, .table-epsilon td, .table-epsilon thead th, .table-epsilon tbody + tbody{border-color: #f1b4b9;}.table-hover .table-epsilon:hover{background-color: #f3c2c5;}.table-hover .table-epsilon:hover > td, .table-hover .table-epsilon:hover > th{background-color: #f3c2c5;}.table-active, .table-active > th, .table-active > td{background-color: rgba(0, 0, 0, 0.075);}.table-hover .table-active:hover{background-color: rgba(0, 0, 0, 0.075);}.table-hover .table-active:hover > td, .table-hover .table-active:hover > th{background-color: rgba(0, 0, 0, 0.075);}.table .thead-dark th{color: #FFFFFF; background-color: #343a40; border-color: #454d55;}.table .thead-light th{color: #495057; background-color: #e9ecef; border-color: #dee2e6;}.table-dark{color: #FFFFFF; background-color: #343a40;}.table-dark th, .table-dark td, .table-dark thead th{border-color: #454d55;}.table-dark.table-bordered{border: 0;}.table-dark.table-striped tbody tr:nth-of-type(odd){background-color: rgba(255, 255, 255, 0.05);}.table-dark.table-hover tbody tr:hover{color: #FFFFFF; background-color: rgba(255, 255, 255, 0.075);}@media (max-width: 575.98px){.table-responsive-sm{display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;}.table-responsive-sm > .table-bordered{border: 0;}}@media (max-width: 767.98px){.table-responsive-md{display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;}.table-responsive-md > .table-bordered{border: 0;}}@media (max-width: 991.98px){.table-responsive-lg{display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;}.table-responsive-lg > .table-bordered{border: 0;}}@media (max-width: 1199.98px){.table-responsive-xl{display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;}.table-responsive-xl > .table-bordered{border: 0;}}.table-responsive{display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;}.table-responsive > .table-bordered{border: 0;}.form-control{display: block; width: 100%; height: calc(1.5em + 0.75rem + 2px); padding: 0.375rem 0.75rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid #ced4da; border-radius: 0.25rem; transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control{transition: none;}}.form-control::-ms-expand{background-color: transparent; border: 0;}.form-control:focus{color: #495057; background-color: #FFFFFF; border-color: #21fffb; outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.form-control::placeholder{color: #6c757d; opacity: 1;}.form-control:disabled, .o_wysiwyg_loader, .form-control[readonly]{background-color: #e9ecef; opacity: 1;}select.form-control:focus::-ms-value{color: #495057; background-color: #FFFFFF;}.form-control-file, .form-control-range{display: block; width: 100%;}.col-form-label{padding-top: calc(0.375rem + 1px); padding-bottom: calc(0.375rem + 1px); margin-bottom: 0; font-size: inherit; line-height: 1.5;}.col-form-label-lg{padding-top: calc(0.5rem + 1px); padding-bottom: calc(0.5rem + 1px); font-size: 1.09375rem; line-height: 1.5;}.col-form-label-sm{padding-top: calc(0.25rem + 1px); padding-bottom: calc(0.25rem + 1px); font-size: 0.75rem; line-height: 1.5;}.form-control-plaintext{display: block; width: 100%; padding-top: 0.375rem; padding-bottom: 0.375rem; margin-bottom: 0; line-height: 1.5; color: #212529; background-color: transparent; border: solid transparent; border-width: 1px 0;}.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg{padding-right: 0; padding-left: 0;}.form-control-sm{height: calc(1.5em + 0.5rem + 2px); padding: 0.25rem 0.5rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;}.form-control-lg{height: calc(1.5em + 1rem + 2px); padding: 0.5rem 1rem; font-size: 1.09375rem; line-height: 1.5; border-radius: 0.3rem;}select.form-control[size], select.form-control[multiple]{height: auto;}textarea.form-control{height: auto;}.form-group{margin-bottom: 1rem;}.form-text{display: block; margin-top: 0.25rem;}.form-row{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-right: -5px; margin-left: -5px;}.form-row > .col, .form-row > [class*="col-"]{padding-right: 5px; padding-left: 5px;}.form-check{position: relative; display: block; padding-left: 1.25rem;}.form-check-input{position: absolute; margin-top: 0.3rem; margin-left: -1.25rem;}.form-check-input:disabled ~ .form-check-label{color: #6c757d;}.form-check-label{margin-bottom: 0;}.form-check-inline{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; align-items: center; padding-left: 0; margin-right: 0.75rem;}.form-check-inline .form-check-input{position: static; margin-top: 0; margin-right: 0.3125rem; margin-left: 0;}.valid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 80%; color: #28a745;}.valid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.75rem; line-height: 1.5; color: #FFFFFF; background-color: rgba(40, 167, 69, 0.9); border-radius: 0.25rem;}.was-validated .form-control:valid, .form-control.is-valid{border-color: #28a745; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: center right calc(0.375em + 0.1875rem); background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:valid:focus, .form-control.is-valid:focus{border-color: #28a745; box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.was-validated .form-control:valid ~ .valid-feedback, .was-validated .form-control:valid ~ .valid-tooltip, .form-control.is-valid ~ .valid-feedback, .form-control.is-valid ~ .valid-tooltip{display: block;}.was-validated textarea.form-control:valid, textarea.form-control.is-valid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .custom-select:valid, .custom-select.is-valid{border-color: #28a745; padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem); background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #FFFFFF no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .custom-select:valid:focus, .custom-select.is-valid:focus{border-color: #28a745; box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.was-validated .custom-select:valid ~ .valid-feedback, .was-validated .custom-select:valid ~ .valid-tooltip, .custom-select.is-valid ~ .valid-feedback, .custom-select.is-valid ~ .valid-tooltip{display: block;}.was-validated .form-control-file:valid ~ .valid-feedback, .was-validated .form-control-file:valid ~ .valid-tooltip, .form-control-file.is-valid ~ .valid-feedback, .form-control-file.is-valid ~ .valid-tooltip{display: block;}.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label{color: #28a745;}.was-validated .form-check-input:valid ~ .valid-feedback, .was-validated .form-check-input:valid ~ .valid-tooltip, .form-check-input.is-valid ~ .valid-feedback, .form-check-input.is-valid ~ .valid-tooltip{display: block;}.was-validated .custom-control-input:valid ~ .custom-control-label, .custom-control-input.is-valid ~ .custom-control-label{color: #28a745;}.was-validated .custom-control-input:valid ~ .custom-control-label::before, .custom-control-input.is-valid ~ .custom-control-label::before{border-color: #28a745;}.was-validated .custom-control-input:valid ~ .valid-feedback, .was-validated .custom-control-input:valid ~ .valid-tooltip, .custom-control-input.is-valid ~ .valid-feedback, .custom-control-input.is-valid ~ .valid-tooltip{display: block;}.was-validated .custom-control-input:valid:checked ~ .custom-control-label::before, .custom-control-input.is-valid:checked ~ .custom-control-label::before{border-color: #34ce57; background-color: #34ce57;}.was-validated .custom-control-input:valid:focus ~ .custom-control-label::before, .custom-control-input.is-valid:focus ~ .custom-control-label::before{box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before{border-color: #28a745;}.was-validated .custom-file-input:valid ~ .custom-file-label, .custom-file-input.is-valid ~ .custom-file-label{border-color: #28a745;}.was-validated .custom-file-input:valid ~ .valid-feedback, .was-validated .custom-file-input:valid ~ .valid-tooltip, .custom-file-input.is-valid ~ .valid-feedback, .custom-file-input.is-valid ~ .valid-tooltip{display: block;}.was-validated .custom-file-input:valid:focus ~ .custom-file-label, .custom-file-input.is-valid:focus ~ .custom-file-label{border-color: #28a745; box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);}.invalid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 80%; color: #dc3545;}.invalid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.75rem; line-height: 1.5; color: #FFFFFF; background-color: rgba(220, 53, 69, 0.9); border-radius: 0.25rem;}.was-validated .form-control:invalid, .form-control.is-invalid{border-color: #dc3545; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E"); background-repeat: no-repeat; background-position: center right calc(0.375em + 0.1875rem); background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.was-validated .form-control:invalid ~ .invalid-feedback, .was-validated .form-control:invalid ~ .invalid-tooltip, .form-control.is-invalid ~ .invalid-feedback, .form-control.is-invalid ~ .invalid-tooltip{display: block;}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .custom-select:invalid, .custom-select.is-invalid{border-color: #dc3545; padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem); background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #FFFFFF no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.was-validated .custom-select:invalid ~ .invalid-feedback, .was-validated .custom-select:invalid ~ .invalid-tooltip, .custom-select.is-invalid ~ .invalid-feedback, .custom-select.is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-control-file:invalid ~ .invalid-feedback, .was-validated .form-control-file:invalid ~ .invalid-tooltip, .form-control-file.is-invalid ~ .invalid-feedback, .form-control-file.is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label{color: #dc3545;}.was-validated .form-check-input:invalid ~ .invalid-feedback, .was-validated .form-check-input:invalid ~ .invalid-tooltip, .form-check-input.is-invalid ~ .invalid-feedback, .form-check-input.is-invalid ~ .invalid-tooltip{display: block;}.was-validated .custom-control-input:invalid ~ .custom-control-label, .custom-control-input.is-invalid ~ .custom-control-label{color: #dc3545;}.was-validated .custom-control-input:invalid ~ .custom-control-label::before, .custom-control-input.is-invalid ~ .custom-control-label::before{border-color: #dc3545;}.was-validated .custom-control-input:invalid ~ .invalid-feedback, .was-validated .custom-control-input:invalid ~ .invalid-tooltip, .custom-control-input.is-invalid ~ .invalid-feedback, .custom-control-input.is-invalid ~ .invalid-tooltip{display: block;}.was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before, .custom-control-input.is-invalid:checked ~ .custom-control-label::before{border-color: #e4606d; background-color: #e4606d;}.was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before, .custom-control-input.is-invalid:focus ~ .custom-control-label::before{box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before, .custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before{border-color: #dc3545;}.was-validated .custom-file-input:invalid ~ .custom-file-label, .custom-file-input.is-invalid ~ .custom-file-label{border-color: #dc3545;}.was-validated .custom-file-input:invalid ~ .invalid-feedback, .was-validated .custom-file-input:invalid ~ .invalid-tooltip, .custom-file-input.is-invalid ~ .invalid-feedback, .custom-file-input.is-invalid ~ .invalid-tooltip{display: block;}.was-validated .custom-file-input:invalid:focus ~ .custom-file-label, .custom-file-input.is-invalid:focus ~ .custom-file-label{border-color: #dc3545; box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);}.form-inline{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap; align-items: center;}.form-inline .form-check{width: 100%;}@media (min-width: 576px){.form-inline label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; margin-bottom: 0;}.form-inline .form-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; -webkit-flex-flow: row wrap; flex-flow: row wrap; align-items: center; margin-bottom: 0;}.form-inline .form-control{display: inline-block; width: auto; vertical-align: middle;}.form-inline .form-control-plaintext{display: inline-block;}.form-inline .input-group, .form-inline .custom-select{width: auto;}.form-inline .form-check{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: auto; padding-left: 0;}.form-inline .form-check-input{position: relative; flex-shrink: 0; margin-top: 0; margin-right: 0.25rem; margin-left: 0;}.form-inline .custom-control{align-items: center; justify-content: center;}.form-inline .custom-control-label{margin-bottom: 0;}}.btn{display: inline-block; font-weight: 400; color: #212529; text-align: center; vertical-align: middle; user-select: none; background-color: transparent; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.25rem; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.btn{transition: none;}}.btn:hover{color: #212529; text-decoration: none;}.btn:focus, .btn.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.btn.disabled, .btn:disabled{opacity: 0.65;}a.btn.disabled, fieldset:disabled a.btn{pointer-events: none;}.btn-primary{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-primary:hover{color: #FFFFFF; background-color: #007a77; border-color: #006d6b;}.btn-primary:focus, .btn-primary.focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.btn-primary.disabled, .btn-primary:disabled{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-primary:not(:disabled):not(.disabled):active, .btn-primary:not(:disabled):not(.disabled).active, .show > .btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #006d6b; border-color: #00605e;}.btn-primary:not(:disabled):not(.disabled):active:focus, .btn-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.btn-secondary{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-secondary:hover{color: #FFFFFF; background-color: #704b66; border-color: #68465f;}.btn-secondary:focus, .btn-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(153, 115, 143, 0.5);}.btn-secondary.disabled, .btn-secondary:disabled{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle{color: #FFFFFF; background-color: #68465f; border-color: #614158;}.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(153, 115, 143, 0.5);}.btn-success{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-success:hover{color: #FFFFFF; background-color: #218838; border-color: #1e7e34;}.btn-success:focus, .btn-success.focus{box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);}.btn-success.disabled, .btn-success:disabled{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-success:not(:disabled):not(.disabled):active, .btn-success:not(:disabled):not(.disabled).active, .show > .btn-success.dropdown-toggle{color: #FFFFFF; background-color: #1e7e34; border-color: #1c7430;}.btn-success:not(:disabled):not(.disabled):active:focus, .btn-success:not(:disabled):not(.disabled).active:focus, .show > .btn-success.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);}.btn-info{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-info:hover{color: #FFFFFF; background-color: #138496; border-color: #117a8b;}.btn-info:focus, .btn-info.focus{box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);}.btn-info.disabled, .btn-info:disabled{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-info:not(:disabled):not(.disabled):active, .btn-info:not(:disabled):not(.disabled).active, .show > .btn-info.dropdown-toggle{color: #FFFFFF; background-color: #117a8b; border-color: #10707f;}.btn-info:not(:disabled):not(.disabled):active:focus, .btn-info:not(:disabled):not(.disabled).active:focus, .show > .btn-info.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);}.btn-warning{color: #212529; background-color: #ffc107; border-color: #ffc107;}.btn-warning:hover{color: #212529; background-color: #e0a800; border-color: #d39e00;}.btn-warning:focus, .btn-warning.focus{box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);}.btn-warning.disabled, .btn-warning:disabled{color: #212529; background-color: #ffc107; border-color: #ffc107;}.btn-warning:not(:disabled):not(.disabled):active, .btn-warning:not(:disabled):not(.disabled).active, .show > .btn-warning.dropdown-toggle{color: #212529; background-color: #d39e00; border-color: #c69500;}.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-warning.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);}.btn-danger{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-danger:hover{color: #FFFFFF; background-color: #c82333; border-color: #bd2130;}.btn-danger:focus, .btn-danger.focus{box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);}.btn-danger.disabled, .btn-danger:disabled{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-danger:not(:disabled):not(.disabled):active, .btn-danger:not(:disabled):not(.disabled).active, .show > .btn-danger.dropdown-toggle{color: #FFFFFF; background-color: #bd2130; border-color: #b21f2d;}.btn-danger:not(:disabled):not(.disabled):active:focus, .btn-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-danger.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);}.btn-light{color: #212529; background-color: #f6f9f9; border-color: #f6f9f9;}.btn-light:hover{color: #212529; background-color: #dfeae9; border-color: #d7e4e4;}.btn-light:focus, .btn-light.focus{box-shadow: 0 0 0 0.2rem rgba(214, 217, 218, 0.5);}.btn-light.disabled, .btn-light:disabled{color: #212529; background-color: #f6f9f9; border-color: #f6f9f9;}.btn-light:not(:disabled):not(.disabled):active, .btn-light:not(:disabled):not(.disabled).active, .show > .btn-light.dropdown-toggle{color: #212529; background-color: #d7e4e4; border-color: #d0dfdf;}.btn-light:not(:disabled):not(.disabled):active:focus, .btn-light:not(:disabled):not(.disabled).active:focus, .show > .btn-light.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(214, 217, 218, 0.5);}.btn-dark{color: #FFFFFF; background-color: #141f1e; border-color: #141f1e;}.btn-dark:hover{color: #FFFFFF; background-color: #050808; border-color: black;}.btn-dark:focus, .btn-dark.focus{box-shadow: 0 0 0 0.2rem rgba(56, 64, 64, 0.5);}.btn-dark.disabled, .btn-dark:disabled{color: #FFFFFF; background-color: #141f1e; border-color: #141f1e;}.btn-dark:not(:disabled):not(.disabled):active, .btn-dark:not(:disabled):not(.disabled).active, .show > .btn-dark.dropdown-toggle{color: #FFFFFF; background-color: black; border-color: black;}.btn-dark:not(:disabled):not(.disabled):active:focus, .btn-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-dark.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(56, 64, 64, 0.5);}.btn-alpha{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-alpha:hover{color: #FFFFFF; background-color: #007a77; border-color: #006d6b;}.btn-alpha:focus, .btn-alpha.focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.btn-alpha.disabled, .btn-alpha:disabled{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-alpha:not(:disabled):not(.disabled):active, .btn-alpha:not(:disabled):not(.disabled).active, .show > .btn-alpha.dropdown-toggle{color: #FFFFFF; background-color: #006d6b; border-color: #00605e;}.btn-alpha:not(:disabled):not(.disabled):active:focus, .btn-alpha:not(:disabled):not(.disabled).active:focus, .show > .btn-alpha.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(38, 174, 172, 0.5);}.btn-beta{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-beta:hover{color: #FFFFFF; background-color: #704b66; border-color: #68465f;}.btn-beta:focus, .btn-beta.focus{box-shadow: 0 0 0 0.2rem rgba(153, 115, 143, 0.5);}.btn-beta.disabled, .btn-beta:disabled{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-beta:not(:disabled):not(.disabled):active, .btn-beta:not(:disabled):not(.disabled).active, .show > .btn-beta.dropdown-toggle{color: #FFFFFF; background-color: #68465f; border-color: #614158;}.btn-beta:not(:disabled):not(.disabled):active:focus, .btn-beta:not(:disabled):not(.disabled).active:focus, .show > .btn-beta.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(153, 115, 143, 0.5);}.btn-gamma{color: #FFFFFF; background-color: #5C5B80; border-color: #5C5B80;}.btn-gamma:hover{color: #FFFFFF; background-color: #4c4b6a; border-color: #474662;}.btn-gamma:focus, .btn-gamma.focus{box-shadow: 0 0 0 0.2rem rgba(116, 116, 147, 0.5);}.btn-gamma.disabled, .btn-gamma:disabled{color: #FFFFFF; background-color: #5C5B80; border-color: #5C5B80;}.btn-gamma:not(:disabled):not(.disabled):active, .btn-gamma:not(:disabled):not(.disabled).active, .show > .btn-gamma.dropdown-toggle{color: #FFFFFF; background-color: #474662; border-color: #41415b;}.btn-gamma:not(:disabled):not(.disabled):active:focus, .btn-gamma:not(:disabled):not(.disabled).active:focus, .show > .btn-gamma.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(116, 116, 147, 0.5);}.btn-delta{color: #FFFFFF; background-color: #5B899E; border-color: #5B899E;}.btn-delta:hover{color: #FFFFFF; background-color: #4d7486; border-color: #486d7e;}.btn-delta:focus, .btn-delta.focus{box-shadow: 0 0 0 0.2rem rgba(116, 155, 173, 0.5);}.btn-delta.disabled, .btn-delta:disabled{color: #FFFFFF; background-color: #5B899E; border-color: #5B899E;}.btn-delta:not(:disabled):not(.disabled):active, .btn-delta:not(:disabled):not(.disabled).active, .show > .btn-delta.dropdown-toggle{color: #FFFFFF; background-color: #486d7e; border-color: #446676;}.btn-delta:not(:disabled):not(.disabled):active:focus, .btn-delta:not(:disabled):not(.disabled).active:focus, .show > .btn-delta.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(116, 155, 173, 0.5);}.btn-epsilon{color: #FFFFFF; background-color: #E46F78; border-color: #E46F78;}.btn-epsilon:hover{color: #FFFFFF; background-color: #de4f5a; border-color: #dc4450;}.btn-epsilon:focus, .btn-epsilon.focus{box-shadow: 0 0 0 0.2rem rgba(232, 133, 140, 0.5);}.btn-epsilon.disabled, .btn-epsilon:disabled{color: #FFFFFF; background-color: #E46F78; border-color: #E46F78;}.btn-epsilon:not(:disabled):not(.disabled):active, .btn-epsilon:not(:disabled):not(.disabled).active, .show > .btn-epsilon.dropdown-toggle{color: #FFFFFF; background-color: #dc4450; border-color: #da3946;}.btn-epsilon:not(:disabled):not(.disabled):active:focus, .btn-epsilon:not(:disabled):not(.disabled).active:focus, .show > .btn-epsilon.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(232, 133, 140, 0.5);}.btn-outline-primary{color: #00A09D; border-color: #00A09D;}.btn-outline-primary:hover{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-outline-primary:focus, .btn-outline-primary.focus{box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.btn-outline-primary.disabled, .btn-outline-primary:disabled{color: #00A09D; background-color: transparent;}.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-outline-primary:not(:disabled):not(.disabled):active:focus, .btn-outline-primary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.btn-outline-secondary{color: #875A7B; border-color: #875A7B;}.btn-outline-secondary:hover{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-outline-secondary:focus, .btn-outline-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.btn-outline-secondary.disabled, .btn-outline-secondary:disabled{color: #875A7B; background-color: transparent;}.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .show > .btn-outline-secondary.dropdown-toggle{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.btn-outline-success{color: #28a745; border-color: #28a745;}.btn-outline-success:hover{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-outline-success:focus, .btn-outline-success.focus{box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);}.btn-outline-success.disabled, .btn-outline-success:disabled{color: #28a745; background-color: transparent;}.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .show > .btn-outline-success.dropdown-toggle{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-outline-success:not(:disabled):not(.disabled):active:focus, .btn-outline-success:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-success.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);}.btn-outline-info{color: #17a2b8; border-color: #17a2b8;}.btn-outline-info:hover{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-outline-info:focus, .btn-outline-info.focus{box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);}.btn-outline-info.disabled, .btn-outline-info:disabled{color: #17a2b8; background-color: transparent;}.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .show > .btn-outline-info.dropdown-toggle{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-outline-info:not(:disabled):not(.disabled):active:focus, .btn-outline-info:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-info.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);}.btn-outline-warning{color: #ffc107; border-color: #ffc107;}.btn-outline-warning:hover{color: #212529; background-color: #ffc107; border-color: #ffc107;}.btn-outline-warning:focus, .btn-outline-warning.focus{box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);}.btn-outline-warning.disabled, .btn-outline-warning:disabled{color: #ffc107; background-color: transparent;}.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .show > .btn-outline-warning.dropdown-toggle{color: #212529; background-color: #ffc107; border-color: #ffc107;}.btn-outline-warning:not(:disabled):not(.disabled):active:focus, .btn-outline-warning:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-warning.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);}.btn-outline-danger{color: #dc3545; border-color: #dc3545;}.btn-outline-danger:hover{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-outline-danger:focus, .btn-outline-danger.focus{box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);}.btn-outline-danger.disabled, .btn-outline-danger:disabled{color: #dc3545; background-color: transparent;}.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .show > .btn-outline-danger.dropdown-toggle{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-outline-danger:not(:disabled):not(.disabled):active:focus, .btn-outline-danger:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-danger.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);}.btn-outline-light{color: #f6f9f9; border-color: #f6f9f9;}.btn-outline-light:hover{color: #212529; background-color: #f6f9f9; border-color: #f6f9f9;}.btn-outline-light:focus, .btn-outline-light.focus{box-shadow: 0 0 0 0.2rem rgba(246, 249, 249, 0.5);}.btn-outline-light.disabled, .btn-outline-light:disabled{color: #f6f9f9; background-color: transparent;}.btn-outline-light:not(:disabled):not(.disabled):active, .btn-outline-light:not(:disabled):not(.disabled).active, .show > .btn-outline-light.dropdown-toggle{color: #212529; background-color: #f6f9f9; border-color: #f6f9f9;}.btn-outline-light:not(:disabled):not(.disabled):active:focus, .btn-outline-light:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-light.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(246, 249, 249, 0.5);}.btn-outline-dark{color: #141f1e; border-color: #141f1e;}.btn-outline-dark:hover{color: #FFFFFF; background-color: #141f1e; border-color: #141f1e;}.btn-outline-dark:focus, .btn-outline-dark.focus{box-shadow: 0 0 0 0.2rem rgba(20, 31, 30, 0.5);}.btn-outline-dark.disabled, .btn-outline-dark:disabled{color: #141f1e; background-color: transparent;}.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .show > .btn-outline-dark.dropdown-toggle{color: #FFFFFF; background-color: #141f1e; border-color: #141f1e;}.btn-outline-dark:not(:disabled):not(.disabled):active:focus, .btn-outline-dark:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-dark.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(20, 31, 30, 0.5);}.btn-outline-alpha{color: #00A09D; border-color: #00A09D;}.btn-outline-alpha:hover{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-outline-alpha:focus, .btn-outline-alpha.focus{box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.btn-outline-alpha.disabled, .btn-outline-alpha:disabled{color: #00A09D; background-color: transparent;}.btn-outline-alpha:not(:disabled):not(.disabled):active, .btn-outline-alpha:not(:disabled):not(.disabled).active, .show > .btn-outline-alpha.dropdown-toggle{color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.btn-outline-alpha:not(:disabled):not(.disabled):active:focus, .btn-outline-alpha:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-alpha.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.btn-outline-beta{color: #875A7B; border-color: #875A7B;}.btn-outline-beta:hover{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-outline-beta:focus, .btn-outline-beta.focus{box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.btn-outline-beta.disabled, .btn-outline-beta:disabled{color: #875A7B; background-color: transparent;}.btn-outline-beta:not(:disabled):not(.disabled):active, .btn-outline-beta:not(:disabled):not(.disabled).active, .show > .btn-outline-beta.dropdown-toggle{color: #FFFFFF; background-color: #875A7B; border-color: #875A7B;}.btn-outline-beta:not(:disabled):not(.disabled):active:focus, .btn-outline-beta:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-beta.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.btn-outline-gamma{color: #5C5B80; border-color: #5C5B80;}.btn-outline-gamma:hover{color: #FFFFFF; background-color: #5C5B80; border-color: #5C5B80;}.btn-outline-gamma:focus, .btn-outline-gamma.focus{box-shadow: 0 0 0 0.2rem rgba(92, 91, 128, 0.5);}.btn-outline-gamma.disabled, .btn-outline-gamma:disabled{color: #5C5B80; background-color: transparent;}.btn-outline-gamma:not(:disabled):not(.disabled):active, .btn-outline-gamma:not(:disabled):not(.disabled).active, .show > .btn-outline-gamma.dropdown-toggle{color: #FFFFFF; background-color: #5C5B80; border-color: #5C5B80;}.btn-outline-gamma:not(:disabled):not(.disabled):active:focus, .btn-outline-gamma:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-gamma.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(92, 91, 128, 0.5);}.btn-outline-delta{color: #5B899E; border-color: #5B899E;}.btn-outline-delta:hover{color: #FFFFFF; background-color: #5B899E; border-color: #5B899E;}.btn-outline-delta:focus, .btn-outline-delta.focus{box-shadow: 0 0 0 0.2rem rgba(91, 137, 158, 0.5);}.btn-outline-delta.disabled, .btn-outline-delta:disabled{color: #5B899E; background-color: transparent;}.btn-outline-delta:not(:disabled):not(.disabled):active, .btn-outline-delta:not(:disabled):not(.disabled).active, .show > .btn-outline-delta.dropdown-toggle{color: #FFFFFF; background-color: #5B899E; border-color: #5B899E;}.btn-outline-delta:not(:disabled):not(.disabled):active:focus, .btn-outline-delta:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-delta.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(91, 137, 158, 0.5);}.btn-outline-epsilon{color: #E46F78; border-color: #E46F78;}.btn-outline-epsilon:hover{color: #FFFFFF; background-color: #E46F78; border-color: #E46F78;}.btn-outline-epsilon:focus, .btn-outline-epsilon.focus{box-shadow: 0 0 0 0.2rem rgba(228, 111, 120, 0.5);}.btn-outline-epsilon.disabled, .btn-outline-epsilon:disabled{color: #E46F78; background-color: transparent;}.btn-outline-epsilon:not(:disabled):not(.disabled):active, .btn-outline-epsilon:not(:disabled):not(.disabled).active, .show > .btn-outline-epsilon.dropdown-toggle{color: #FFFFFF; background-color: #E46F78; border-color: #E46F78;}.btn-outline-epsilon:not(:disabled):not(.disabled):active:focus, .btn-outline-epsilon:not(:disabled):not(.disabled).active:focus, .show > .btn-outline-epsilon.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(228, 111, 120, 0.5);}.btn-link{font-weight: 400; color: #00A09D; text-decoration: none;}.btn-link:hover{color: #005452; text-decoration: underline;}.btn-link:focus, .btn-link.focus{text-decoration: underline; box-shadow: none;}.btn-link:disabled, .btn-link.disabled{color: #6c757d; pointer-events: none;}.btn-lg, .btn-group-lg > .btn{padding: 0.5rem 1rem; font-size: 1.09375rem; line-height: 1.5; border-radius: 0.3rem;}.btn-sm, .btn-group-sm > .btn{padding: 0.0625rem 0.3125rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;}.btn-block{display: block; width: 100%;}.btn-block + .btn-block{margin-top: 0.5rem;}input[type="submit"].btn-block, input[type="reset"].btn-block, input[type="button"].btn-block{width: 100%;}.fade{transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce){.fade{transition: none;}}.fade:not(.show){opacity: 0;}.collapse:not(.show){display: none;}.collapsing{position: relative; height: 0; overflow: hidden; transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing{transition: none;}}.dropup, .dropright, .dropdown, .dropleft{position: relative;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid; border-right: 0.3em solid transparent; border-bottom: 0; border-left: 0.3em solid transparent;}.dropdown-toggle:empty::after{margin-left: 0;}.dropdown-menu{position: absolute; top: 100%; left: 0; z-index: 1000; display: none; float: left; min-width: 10rem; padding: 0.5rem 0; margin: 0.125rem 0 0; font-size: 0.875rem; color: #212529; text-align: left; list-style: none; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.15); border-radius: 0.25rem;}.dropdown-menu-left{right: auto; left: 0;}.dropdown-menu-right{right: 0; left: auto;}@media (min-width: 576px){.dropdown-menu-sm-left{right: auto; left: 0;}.dropdown-menu-sm-right{right: 0; left: auto;}}@media (min-width: 768px){.dropdown-menu-md-left{right: auto; left: 0;}.dropdown-menu-md-right{right: 0; left: auto;}}@media (min-width: 992px){.dropdown-menu-lg-left{right: auto; left: 0;}.dropdown-menu-lg-right{right: 0; left: auto;}}@media (min-width: 1200px){.dropdown-menu-xl-left{right: auto; left: 0;}.dropdown-menu-xl-right{right: 0; left: auto;}}.dropup .dropdown-menu{top: auto; bottom: 100%; margin-top: 0; margin-bottom: 0.125rem;}.dropup .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0; border-right: 0.3em solid transparent; border-bottom: 0.3em solid; border-left: 0.3em solid transparent;}.dropup .dropdown-toggle:empty::after{margin-left: 0;}.dropright .dropdown-menu{top: 0; right: auto; left: 100%; margin-top: 0; margin-left: 0.125rem;}.dropright .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0; border-bottom: 0.3em solid transparent; border-left: 0.3em solid;}.dropright .dropdown-toggle:empty::after{margin-left: 0;}.dropright .dropdown-toggle::after{vertical-align: 0;}.dropleft .dropdown-menu{top: 0; right: 100%; left: auto; margin-top: 0; margin-right: 0.125rem;}.dropleft .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: "";}.dropleft .dropdown-toggle::after{display: none;}.dropleft .dropdown-toggle::before{display: inline-block; margin-right: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0.3em solid; border-bottom: 0.3em solid transparent;}.dropleft .dropdown-toggle:empty::after{margin-left: 0;}.dropleft .dropdown-toggle::before{vertical-align: 0;}.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"]{right: auto; bottom: auto;}.dropdown-divider{height: 0; margin: 0.5rem 0; overflow: hidden; border-top: 1px solid #e9ecef;}.dropdown-item{display: block; width: 100%; padding: 0.25rem 1.5rem; clear: both; font-weight: 400; color: #212529; text-align: inherit; white-space: nowrap; background-color: transparent; border: 0;}.dropdown-item:hover, .dropdown-item:focus{color: #16181b; text-decoration: none; background-color: #f8f9fa;}.dropdown-item.active, .dropdown-item:active{color: #FFFFFF; text-decoration: none; background-color: #00A09D;}.dropdown-item.disabled, .dropdown-item:disabled{color: #6c757d; pointer-events: none; background-color: transparent;}.dropdown-menu.show{display: block;}.dropdown-header{display: block; padding: 0.5rem 1.5rem; margin-bottom: 0; font-size: 0.75rem; color: #6c757d; white-space: nowrap;}.dropdown-item-text{display: block; padding: 0.25rem 1.5rem; color: #212529;}.btn-group, .btn-group-vertical{position: relative; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; vertical-align: middle;}.btn-group > .btn, .btn-group-vertical > .btn{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.btn-group > .btn:hover, .btn-group-vertical > .btn:hover{z-index: 1;}.btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active{z-index: 1;}.btn-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; justify-content: flex-start;}.btn-toolbar .input-group{width: auto;}.btn-group > .btn:not(:first-child), .btn-group > .btn-group:not(:first-child){margin-left: -1px;}.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn-group:not(:last-child) > .btn{border-top-right-radius: 0; border-bottom-right-radius: 0;}.btn-group > .btn:not(:first-child), .btn-group > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}.dropdown-toggle-split{padding-right: 0.5625rem; padding-left: 0.5625rem;}.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after{margin-left: 0;}.dropleft .dropdown-toggle-split::before{margin-right: 0;}.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split{padding-right: 0.234375rem; padding-left: 0.234375rem;}.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split{padding-right: 0.75rem; padding-left: 0.75rem;}.btn-group-vertical{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: flex-start; justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group{width: 100%;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child){margin-top: -1px;}.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn-group:not(:last-child) > .btn{border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-top-right-radius: 0;}.btn-group-toggle > .btn, .btn-group-toggle > .btn-group > .btn{margin-bottom: 0;}.btn-group-toggle > .btn input[type="radio"], .btn-group-toggle > .btn input[type="checkbox"], .btn-group-toggle > .btn-group > .btn input[type="radio"], .btn-group-toggle > .btn-group > .btn input[type="checkbox"]{position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none;}.input-group{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: stretch; width: 100%;}.input-group > .form-control, .input-group > .form-control-plaintext, .input-group > .custom-select, .input-group > .custom-file{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 1%; margin-bottom: 0;}.input-group > .form-control + .form-control, .input-group > .form-control + .custom-select, .input-group > .form-control + .custom-file, .input-group > .form-control-plaintext + .form-control, .input-group > .form-control-plaintext + .custom-select, .input-group > .form-control-plaintext + .custom-file, .input-group > .custom-select + .form-control, .input-group > .custom-select + .custom-select, .input-group > .custom-select + .custom-file, .input-group > .custom-file + .form-control, .input-group > .custom-file + .custom-select, .input-group > .custom-file + .custom-file{margin-left: -1px;}.input-group > .form-control:focus, .input-group > .custom-select:focus, .input-group > .custom-file .custom-file-input:focus ~ .custom-file-label{z-index: 3;}.input-group > .custom-file .custom-file-input:focus{z-index: 4;}.input-group > .form-control:not(:last-child), .input-group > .custom-select:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > .form-control:not(:first-child), .input-group > .custom-select:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.input-group > .custom-file{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after{border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > .custom-file:not(:first-child) .custom-file-label{border-top-left-radius: 0; border-bottom-left-radius: 0;}.input-group-prepend, .input-group-append{display: -webkit-box; display: -webkit-flex; display: flex;}.input-group-prepend .btn, .input-group-append .btn{position: relative; z-index: 2;}.input-group-prepend .btn:focus, .input-group-append .btn:focus{z-index: 3;}.input-group-prepend .btn + .btn, .input-group-prepend .btn + .input-group-text, .input-group-prepend .input-group-text + .input-group-text, .input-group-prepend .input-group-text + .btn, .input-group-append .btn + .btn, .input-group-append .btn + .input-group-text, .input-group-append .input-group-text + .input-group-text, .input-group-append .input-group-text + .btn{margin-left: -1px;}.input-group-prepend{margin-right: -1px;}.input-group-append{margin-left: -1px;}.input-group-text{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.375rem 0.75rem; margin-bottom: 0; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; text-align: center; white-space: nowrap; background-color: #e9ecef; border: 1px solid #ced4da; border-radius: 0.25rem;}.input-group-text input[type="radio"], .input-group-text input[type="checkbox"]{margin-top: 0;}.input-group-lg > .form-control:not(textarea), .input-group-lg > .custom-select{height: calc(1.5em + 1rem + 2px);}.input-group-lg > .form-control, .input-group-lg > .custom-select, .input-group-lg > .input-group-prepend > .input-group-text, .input-group-lg > .input-group-append > .input-group-text, .input-group-lg > .input-group-prepend > .btn, .input-group-lg > .input-group-append > .btn{padding: 0.5rem 1rem; font-size: 1.09375rem; line-height: 1.5; border-radius: 0.3rem;}.input-group-sm > .form-control:not(textarea), .input-group-sm > .custom-select{height: calc(1.5em + 0.5rem + 2px);}.input-group-sm > .form-control, .input-group-sm > .custom-select, .input-group-sm > .input-group-prepend > .input-group-text, .input-group-sm > .input-group-append > .input-group-text, .input-group-sm > .input-group-prepend > .btn, .input-group-sm > .input-group-append > .btn{padding: 0.25rem 0.5rem; font-size: 0.75rem; line-height: 1.5; border-radius: 0.2rem;}.input-group-lg > .custom-select, .input-group-sm > .custom-select{padding-right: 1.75rem;}.input-group > .input-group-prepend > .btn, .input-group > .input-group-prepend > .input-group-text, .input-group > .input-group-append:not(:last-child) > .btn, .input-group > .input-group-append:not(:last-child) > .input-group-text, .input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group > .input-group-append:last-child > .input-group-text:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > .input-group-append > .btn, .input-group > .input-group-append > .input-group-text, .input-group > .input-group-prepend:not(:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child) > .input-group-text, .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.custom-control{position: relative; display: block; min-height: 1.3125rem; padding-left: 1.5rem;}.custom-control-inline{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; margin-right: 1rem;}.custom-control-input{position: absolute; z-index: -1; opacity: 0;}.custom-control-input:checked ~ .custom-control-label::before{color: #FFFFFF; border-color: #00A09D; background-color: #00A09D;}.custom-control-input:focus ~ .custom-control-label::before{box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-control-input:focus:not(:checked) ~ .custom-control-label::before{border-color: #21fffb;}.custom-control-input:not(:disabled):active ~ .custom-control-label::before{color: #FFFFFF; background-color: #54fffc; border-color: #54fffc;}.custom-control-input:disabled ~ .custom-control-label{color: #6c757d;}.custom-control-input:disabled ~ .custom-control-label::before{background-color: #e9ecef;}.custom-control-label{position: relative; margin-bottom: 0; vertical-align: top;}.custom-control-label::before{position: absolute; top: 0.15625rem; left: -1.5rem; display: block; width: 1rem; height: 1rem; pointer-events: none; content: ""; background-color: #FFFFFF; border: #adb5bd solid 1px;}.custom-control-label::after{position: absolute; top: 0.15625rem; left: -1.5rem; display: block; width: 1rem; height: 1rem; content: ""; background: no-repeat 50% / 50% 50%;}.custom-checkbox .custom-control-label::before{border-radius: 0.25rem;}.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23FFFFFF' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");}.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before{border-color: #00A09D; background-color: #00A09D;}.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23FFFFFF' d='M0 2h4'/%3e%3c/svg%3e");}.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before{background-color: rgba(0, 160, 157, 0.5);}.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before{background-color: rgba(0, 160, 157, 0.5);}.custom-radio .custom-control-label::before{border-radius: 50%;}.custom-radio .custom-control-input:checked ~ .custom-control-label::after{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FFFFFF'/%3e%3c/svg%3e");}.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before{background-color: rgba(0, 160, 157, 0.5);}.custom-switch{padding-left: 2.25rem;}.custom-switch .custom-control-label::before{left: -2.25rem; width: 1.75rem; pointer-events: all; border-radius: 0.5rem;}.custom-switch .custom-control-label::after{top: calc(0.15625rem + 2px); left: calc(-2.25rem + 2px); width: calc(1rem - 4px); height: calc(1rem - 4px); background-color: #adb5bd; border-radius: 0.5rem; transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.custom-switch .custom-control-label::after{transition: none;}}.custom-switch .custom-control-input:checked ~ .custom-control-label::after{background-color: #FFFFFF; transform: translateX(0.75rem);}.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before{background-color: rgba(0, 160, 157, 0.5);}.custom-select{display: inline-block; width: 100%; height: calc(1.5em + 0.75rem + 2px); padding: 0.375rem 1.75rem 0.375rem 0.75rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; vertical-align: middle; background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px; background-color: #FFFFFF; border: 1px solid #ced4da; border-radius: 0.25rem; -webkit-appearance: none; -moz-appearance: none; appearance: none;}.custom-select:focus{border-color: #21fffb; outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-select:focus::-ms-value{color: #495057; background-color: #FFFFFF;}.custom-select[multiple], .custom-select[size]:not([size="1"]){height: auto; padding-right: 0.75rem; background-image: none;}.custom-select:disabled{color: #6c757d; background-color: #e9ecef;}.custom-select::-ms-expand{display: none;}.custom-select-sm{height: calc(1.5em + 0.5rem + 2px); padding-top: 0.25rem; padding-bottom: 0.25rem; padding-left: 0.5rem; font-size: 0.75rem;}.custom-select-lg{height: calc(1.5em + 1rem + 2px); padding-top: 0.5rem; padding-bottom: 0.5rem; padding-left: 1rem; font-size: 1.09375rem;}.custom-file{position: relative; display: inline-block; width: 100%; height: calc(1.5em + 0.75rem + 2px); margin-bottom: 0;}.custom-file-input{position: relative; z-index: 2; width: 100%; height: calc(1.5em + 0.75rem + 2px); margin: 0; opacity: 0;}.custom-file-input:focus ~ .custom-file-label{border-color: #21fffb; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-file-input:disabled ~ .custom-file-label{background-color: #e9ecef;}.custom-file-input:lang(en) ~ .custom-file-label::after{content: "Browse";}.custom-file-input ~ .custom-file-label[data-browse]::after{content: attr(data-browse);}.custom-file-label{position: absolute; top: 0; right: 0; left: 0; z-index: 1; height: calc(1.5em + 0.75rem + 2px); padding: 0.375rem 0.75rem; font-weight: 400; line-height: 1.5; color: #495057; background-color: #FFFFFF; border: 1px solid #ced4da; border-radius: 0.25rem;}.custom-file-label::after{position: absolute; top: 0; right: 0; bottom: 0; z-index: 3; display: block; height: calc(1.5em + 0.75rem); padding: 0.375rem 0.75rem; line-height: 1.5; color: #495057; content: "Browse"; background-color: #e9ecef; border-left: inherit; border-radius: 0 0.25rem 0.25rem 0;}.custom-range{width: 100%; height: calc(1rem + 0.4rem); padding: 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}.custom-range:focus{outline: none;}.custom-range:focus::-webkit-slider-thumb{box-shadow: 0 0 0 1px white, 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-range:focus::-moz-range-thumb{box-shadow: 0 0 0 1px white, 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-range:focus::-ms-thumb{box-shadow: 0 0 0 1px white, 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.custom-range::-moz-focus-outer{border: 0;}.custom-range::-webkit-slider-thumb{width: 1rem; height: 1rem; margin-top: -0.25rem; background-color: #00A09D; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.custom-range::-webkit-slider-thumb{transition: none;}}.custom-range::-webkit-slider-thumb:active{background-color: #54fffc;}.custom-range::-webkit-slider-runnable-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.custom-range::-moz-range-thumb{width: 1rem; height: 1rem; background-color: #00A09D; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.custom-range::-moz-range-thumb{transition: none;}}.custom-range::-moz-range-thumb:active{background-color: #54fffc;}.custom-range::-moz-range-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.custom-range::-ms-thumb{width: 1rem; height: 1rem; margin-top: 0; margin-right: 0.2rem; margin-left: 0.2rem; background-color: #00A09D; border: 0; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.custom-range::-ms-thumb{transition: none;}}.custom-range::-ms-thumb:active{background-color: #54fffc;}.custom-range::-ms-track{width: 100%; height: 0.5rem; color: transparent; cursor: pointer; background-color: transparent; border-color: transparent; border-width: 0.5rem;}.custom-range::-ms-fill-lower{background-color: #dee2e6; border-radius: 1rem;}.custom-range::-ms-fill-upper{margin-right: 15px; background-color: #dee2e6; border-radius: 1rem;}.custom-range:disabled::-webkit-slider-thumb{background-color: #adb5bd;}.custom-range:disabled::-webkit-slider-runnable-track{cursor: default;}.custom-range:disabled::-moz-range-thumb{background-color: #adb5bd;}.custom-range:disabled::-moz-range-track{cursor: default;}.custom-range:disabled::-ms-thumb{background-color: #adb5bd;}.custom-control-label::before, .custom-file-label, .custom-select{transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.custom-control-label::before, .custom-file-label, .custom-select{transition: none;}}.nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding-left: 0; margin-bottom: 0; list-style: none;}.nav-link{display: block; padding: 0.5rem 1rem;}.nav-link:hover, .nav-link:focus{text-decoration: none;}.nav-link.disabled{color: #6c757d; pointer-events: none; cursor: default;}.nav-tabs{border-bottom: 1px solid #dee2e6;}.nav-tabs .nav-item{margin-bottom: -1px;}.nav-tabs .nav-link{border: 1px solid transparent; border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus{border-color: #e9ecef #e9ecef #dee2e6;}.nav-tabs .nav-link.disabled{color: #6c757d; background-color: transparent; border-color: transparent;}.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link{color: #495057; background-color: white; border-color: #dee2e6 #dee2e6 white;}.nav-tabs .dropdown-menu{margin-top: -1px; border-top-left-radius: 0; border-top-right-radius: 0;}.nav-pills .nav-link{border-radius: 0.25rem;}.nav-pills .nav-link.active, .nav-pills .show > .nav-link{color: #FFFFFF; background-color: #00A09D;}.nav-fill .nav-item{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: center;}.nav-justified .nav-item{flex-basis: 0; flex-grow: 1; text-align: center;}.tab-content > .tab-pane{display: none;}.tab-content > .active{display: block;}.navbar{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between; padding: 0.5rem 1rem;}.navbar > .container, .navbar > .container-fluid{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between;}.navbar-brand{display: inline-block; padding-top: 0.3359375rem; padding-bottom: 0.3359375rem; margin-right: 1rem; font-size: 1.09375rem; line-height: inherit; white-space: nowrap;}.navbar-brand:hover, .navbar-brand:focus{text-decoration: none;}.navbar-nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; list-style: none;}.navbar-nav .nav-link{padding-right: 0; padding-left: 0;}.navbar-nav .dropdown-menu{position: static; float: none;}.navbar-text{display: inline-block; padding-top: 0.5rem; padding-bottom: 0.5rem;}.navbar-collapse{flex-basis: 100%; flex-grow: 1; align-items: center;}.navbar-toggler{padding: 0.25rem 0.75rem; font-size: 1.09375rem; line-height: 1; background-color: transparent; border: 1px solid transparent; border-radius: 0.25rem;}.navbar-toggler:hover, .navbar-toggler:focus{text-decoration: none;}.navbar-toggler-icon{display: inline-block; width: 1.5em; height: 1.5em; vertical-align: middle; content: ""; background: no-repeat center center; background-size: 100% 100%;}@media (max-width: 575.98px){.navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid{padding-right: 0; padding-left: 0;}}@media (min-width: 576px){.navbar-expand-sm{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-sm .navbar-nav{flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-sm .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid{-webkit-flex-wrap: nowrap; flex-wrap: nowrap;}.navbar-expand-sm .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-sm .navbar-toggler{display: none;}}@media (max-width: 767.98px){.navbar-expand-md > .container, .navbar-expand-md > .container-fluid{padding-right: 0; padding-left: 0;}}@media (min-width: 768px){.navbar-expand-md{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-md .navbar-nav{flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-md .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-md > .container, .navbar-expand-md > .container-fluid{-webkit-flex-wrap: nowrap; flex-wrap: nowrap;}.navbar-expand-md .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-md .navbar-toggler{display: none;}}@media (max-width: 991.98px){.navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid{padding-right: 0; padding-left: 0;}}@media (min-width: 992px){.navbar-expand-lg{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-lg .navbar-nav{flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-lg .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid{-webkit-flex-wrap: nowrap; flex-wrap: nowrap;}.navbar-expand-lg .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-lg .navbar-toggler{display: none;}}@media (max-width: 1199.98px){.navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid{padding-right: 0; padding-left: 0;}}@media (min-width: 1200px){.navbar-expand-xl{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xl .navbar-nav{flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xl .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid{-webkit-flex-wrap: nowrap; flex-wrap: nowrap;}.navbar-expand-xl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xl .navbar-toggler{display: none;}}.navbar-expand{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand > .container, .navbar-expand > .container-fluid{padding-right: 0; padding-left: 0;}.navbar-expand .navbar-nav{flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand > .container, .navbar-expand > .container-fluid{-webkit-flex-wrap: nowrap; flex-wrap: nowrap;}.navbar-expand .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand .navbar-toggler{display: none;}.navbar-light .navbar-brand{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-nav .nav-link{color: rgba(0, 0, 0, 0.5);}.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus{color: rgba(0, 0, 0, 0.7);}.navbar-light .navbar-nav .nav-link.disabled{color: rgba(0, 0, 0, 0.3);}.navbar-light .navbar-nav .show > .nav-link, .navbar-light .navbar-nav .active > .nav-link, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .nav-link.active{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-toggler{color: rgba(0, 0, 0, 0.5); border-color: rgba(0, 0, 0, 0.1);}.navbar-light .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-light .navbar-text{color: rgba(0, 0, 0, 0.5);}.navbar-light .navbar-text a{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus{color: rgba(0, 0, 0, 0.9);}.navbar-dark .navbar-brand{color: #FFFFFF;}.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus{color: #FFFFFF;}.navbar-dark .navbar-nav .nav-link{color: rgba(255, 255, 255, 0.5);}.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus{color: rgba(255, 255, 255, 0.75);}.navbar-dark .navbar-nav .nav-link.disabled{color: rgba(255, 255, 255, 0.25);}.navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .active > .nav-link, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar-nav .nav-link.active{color: #FFFFFF;}.navbar-dark .navbar-toggler{color: rgba(255, 255, 255, 0.5); border-color: rgba(255, 255, 255, 0.1);}.navbar-dark .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-dark .navbar-text{color: rgba(255, 255, 255, 0.5);}.navbar-dark .navbar-text a{color: #FFFFFF;}.navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus{color: #FFFFFF;}.card{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #FFFFFF; background-clip: border-box; border: 1px solid rgba(0, 0, 0, 0.125); border-radius: 0.25rem;}.card > hr{margin-right: 0; margin-left: 0;}.card > .list-group:first-child .list-group-item:first-child{border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.card > .list-group:last-child .list-group-item:last-child{border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.card-body{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1.25rem;}.card-title{margin-bottom: 0.75rem;}.card-subtitle{margin-top: -0.375rem; margin-bottom: 0;}.card-text:last-child{margin-bottom: 0;}.card-link:hover{text-decoration: none;}.card-link + .card-link{margin-left: 1.25rem;}.card-header{padding: 0.75rem 1.25rem; margin-bottom: 0; background-color: rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.125);}.card-header:first-child{border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;}.card-header + .list-group .list-group-item:first-child{border-top: 0;}.card-footer{padding: 0.75rem 1.25rem; background-color: rgba(0, 0, 0, 0.03); border-top: 1px solid rgba(0, 0, 0, 0.125);}.card-footer:last-child{border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);}.card-header-tabs{margin-right: -0.625rem; margin-bottom: -0.75rem; margin-left: -0.625rem; border-bottom: 0;}.card-header-pills{margin-right: -0.625rem; margin-left: -0.625rem;}.card-img-overlay{position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: 1.25rem;}.card-img{width: 100%; border-radius: calc(0.25rem - 1px);}.card-img-top{width: 100%; border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-img-bottom{width: 100%; border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card-deck{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}.card-deck .card{margin-bottom: 15px;}@media (min-width: 576px){.card-deck{-webkit-flex-flow: row wrap; flex-flow: row wrap; margin-right: -15px; margin-left: -15px;}.card-deck .card{display: -webkit-box; display: -webkit-flex; display: flex; flex: 1 0 0%; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; margin-right: 15px; margin-bottom: 0; margin-left: 15px;}}.card-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}.card-group > .card{margin-bottom: 15px;}@media (min-width: 576px){.card-group{-webkit-flex-flow: row wrap; flex-flow: row wrap;}.card-group > .card{flex: 1 0 0%; margin-bottom: 0;}.card-group > .card + .card{margin-left: 0; border-left: 0;}.card-group > .card:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-top, .card-group > .card:not(:last-child) .card-header{border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-bottom, .card-group > .card:not(:last-child) .card-footer{border-bottom-right-radius: 0;}.card-group > .card:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-top, .card-group > .card:not(:first-child) .card-header{border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-bottom, .card-group > .card:not(:first-child) .card-footer{border-bottom-left-radius: 0;}}.card-columns .card{margin-bottom: 0.75rem;}@media (min-width: 576px){.card-columns{column-count: 3; column-gap: 1.25rem; orphans: 1; widows: 1;}.card-columns .card{display: inline-block; width: 100%;}}.accordion > .card{overflow: hidden;}.accordion > .card:not(:first-of-type) .card-header:first-child{border-radius: 0;}.accordion > .card:not(:first-of-type):not(:last-of-type){border-bottom: 0; border-radius: 0;}.accordion > .card:first-of-type{border-bottom: 0; border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.accordion > .card:last-of-type{border-top-left-radius: 0; border-top-right-radius: 0;}.accordion > .card .card-header{margin-bottom: -1px;}.breadcrumb{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding: 0.75rem 1rem; margin-bottom: 1rem; list-style: none; background-color: #e9ecef; border-radius: 0.25rem;}.breadcrumb-item + .breadcrumb-item{padding-left: 0.5rem;}.breadcrumb-item + .breadcrumb-item::before{display: inline-block; padding-right: 0.5rem; color: #6c757d; content: "/";}.breadcrumb-item + .breadcrumb-item:hover::before{text-decoration: underline;}.breadcrumb-item + .breadcrumb-item:hover::before{text-decoration: none;}.breadcrumb-item.active{color: #6c757d;}.pagination{display: -webkit-box; display: -webkit-flex; display: flex; padding-left: 0; list-style: none; border-radius: 0.25rem;}.page-link{position: relative; display: block; padding: 0.5rem 0.75rem; margin-left: -1px; line-height: 1.25; color: #00A09D; background-color: #FFFFFF; border: 1px solid #dee2e6;}.page-link:hover{z-index: 2; color: #005452; text-decoration: none; background-color: #e9ecef; border-color: #dee2e6;}.page-link:focus{z-index: 2; outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.25);}.page-item:first-child .page-link{margin-left: 0; border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.page-item:last-child .page-link{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem;}.page-item.active .page-link{z-index: 1; color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.page-item.disabled .page-link{color: #6c757d; pointer-events: none; cursor: auto; background-color: #FFFFFF; border-color: #dee2e6;}.pagination-lg .page-link{padding: 0.75rem 1.5rem; font-size: 1.09375rem; line-height: 1.5;}.pagination-lg .page-item:first-child .page-link{border-top-left-radius: 0.3rem; border-bottom-left-radius: 0.3rem;}.pagination-lg .page-item:last-child .page-link{border-top-right-radius: 0.3rem; border-bottom-right-radius: 0.3rem;}.pagination-sm .page-link{padding: 0.25rem 0.5rem; font-size: 0.75rem; line-height: 1.5;}.pagination-sm .page-item:first-child .page-link{border-top-left-radius: 0.2rem; border-bottom-left-radius: 0.2rem;}.pagination-sm .page-item:last-child .page-link{border-top-right-radius: 0.2rem; border-bottom-right-radius: 0.2rem;}.badge{display: inline-block; padding: 0.25em 0.4em; font-size: 75%; font-weight: 700; line-height: 1; text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: 0.25rem; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.badge{transition: none;}}a.badge:hover, a.badge:focus{text-decoration: none;}.badge:empty{display: none;}.btn .badge{position: relative; top: -1px;}.badge-pill{padding-right: 0.6em; padding-left: 0.6em; border-radius: 10rem;}.badge-primary{color: #FFFFFF; background-color: #00A09D;}a.badge-primary:hover, a.badge-primary:focus{color: #FFFFFF; background-color: #006d6b;}a.badge-primary:focus, a.badge-primary.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.badge-secondary{color: #FFFFFF; background-color: #875A7B;}a.badge-secondary:hover, a.badge-secondary:focus{color: #FFFFFF; background-color: #68465f;}a.badge-secondary:focus, a.badge-secondary.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.badge-success{color: #FFFFFF; background-color: #28a745;}a.badge-success:hover, a.badge-success:focus{color: #FFFFFF; background-color: #1e7e34;}a.badge-success:focus, a.badge-success.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);}.badge-info{color: #FFFFFF; background-color: #17a2b8;}a.badge-info:hover, a.badge-info:focus{color: #FFFFFF; background-color: #117a8b;}a.badge-info:focus, a.badge-info.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);}.badge-warning{color: #212529; background-color: #ffc107;}a.badge-warning:hover, a.badge-warning:focus{color: #212529; background-color: #d39e00;}a.badge-warning:focus, a.badge-warning.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);}.badge-danger{color: #FFFFFF; background-color: #dc3545;}a.badge-danger:hover, a.badge-danger:focus{color: #FFFFFF; background-color: #bd2130;}a.badge-danger:focus, a.badge-danger.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);}.badge-light{color: #212529; background-color: #f6f9f9;}a.badge-light:hover, a.badge-light:focus{color: #212529; background-color: #d7e4e4;}a.badge-light:focus, a.badge-light.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(246, 249, 249, 0.5);}.badge-dark{color: #FFFFFF; background-color: #141f1e;}a.badge-dark:hover, a.badge-dark:focus{color: #FFFFFF; background-color: black;}a.badge-dark:focus, a.badge-dark.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(20, 31, 30, 0.5);}.badge-alpha{color: #FFFFFF; background-color: #00A09D;}a.badge-alpha:hover, a.badge-alpha:focus{color: #FFFFFF; background-color: #006d6b;}a.badge-alpha:focus, a.badge-alpha.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(0, 160, 157, 0.5);}.badge-beta{color: #FFFFFF; background-color: #875A7B;}a.badge-beta:hover, a.badge-beta:focus{color: #FFFFFF; background-color: #68465f;}a.badge-beta:focus, a.badge-beta.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(135, 90, 123, 0.5);}.badge-gamma{color: #FFFFFF; background-color: #5C5B80;}a.badge-gamma:hover, a.badge-gamma:focus{color: #FFFFFF; background-color: #474662;}a.badge-gamma:focus, a.badge-gamma.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(92, 91, 128, 0.5);}.badge-delta{color: #FFFFFF; background-color: #5B899E;}a.badge-delta:hover, a.badge-delta:focus{color: #FFFFFF; background-color: #486d7e;}a.badge-delta:focus, a.badge-delta.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(91, 137, 158, 0.5);}.badge-epsilon{color: #FFFFFF; background-color: #E46F78;}a.badge-epsilon:hover, a.badge-epsilon:focus{color: #FFFFFF; background-color: #dc4450;}a.badge-epsilon:focus, a.badge-epsilon.focus{outline: 0; box-shadow: 0 0 0 0.2rem rgba(228, 111, 120, 0.5);}.jumbotron{padding: 2rem 1rem; margin-bottom: 2rem; background-color: transparent; border-radius: 0.3rem;}@media (min-width: 576px){.jumbotron{padding: 4rem 2rem;}}.jumbotron-fluid{padding-right: 0; padding-left: 0; border-radius: 0;}.alert{position: relative; padding: 0.75rem 1.25rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.25rem;}.alert-heading{color: inherit;}.alert-link{font-weight: 700;}.alert-dismissible{padding-right: 3.8125rem;}.alert-dismissible .close{position: absolute; top: 0; right: 0; padding: 0.75rem 1.25rem; color: inherit;}.alert-primary{color: #005352; background-color: #cceceb; border-color: #b8e4e4;}.alert-primary hr{border-top-color: #a6dddd;}.alert-primary .alert-link{color: #002020;}.alert-secondary{color: #462f40; background-color: #e7dee5; border-color: #ddd1da;}.alert-secondary hr{border-top-color: #d2c2ce;}.alert-secondary .alert-link{color: #271b24;}.alert-success{color: #155724; background-color: #d4edda; border-color: #c3e6cb;}.alert-success hr{border-top-color: #b1dfbb;}.alert-success .alert-link{color: #0b2e13;}.alert-info{color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb;}.alert-info hr{border-top-color: #abdde5;}.alert-info .alert-link{color: #062c33;}.alert-warning{color: #856404; background-color: #fff3cd; border-color: #ffeeba;}.alert-warning hr{border-top-color: #ffe8a1;}.alert-warning .alert-link{color: #533f03;}.alert-danger{color: #721c24; background-color: #f8d7da; border-color: #f5c6cb;}.alert-danger hr{border-top-color: #f1b0b7;}.alert-danger .alert-link{color: #491217;}.alert-light{color: #808181; background-color: #fdfefe; border-color: #fcfdfd;}.alert-light hr{border-top-color: #edf3f3;}.alert-light .alert-link{color: #676767;}.alert-dark{color: #0b1010; background-color: #d0d2d2; border-color: #bdc0c0;}.alert-dark hr{border-top-color: #b0b4b4;}.alert-dark .alert-link{color: black;}.alert-alpha{color: #005352; background-color: #cceceb; border-color: #b8e4e4;}.alert-alpha hr{border-top-color: #a6dddd;}.alert-alpha .alert-link{color: #002020;}.alert-beta{color: #462f40; background-color: #e7dee5; border-color: #ddd1da;}.alert-beta hr{border-top-color: #d2c2ce;}.alert-beta .alert-link{color: #271b24;}.alert-gamma{color: #302f43; background-color: #dedee6; border-color: #d1d1db;}.alert-gamma hr{border-top-color: #c3c3d0;}.alert-gamma .alert-link{color: #1b1a25;}.alert-delta{color: #2f4752; background-color: #dee7ec; border-color: #d1dee4;}.alert-delta hr{border-top-color: #c1d2db;}.alert-delta .alert-link{color: #1c2b32;}.alert-epsilon{color: #773a3e; background-color: #fae2e4; border-color: #f7d7d9;}.alert-epsilon hr{border-top-color: #f3c2c5;}.alert-epsilon .alert-link{color: #55292c;}@keyframes progress-bar-stripes{from{background-position: 1rem 0;}to{background-position: 0 0;}}.progress{display: -webkit-box; display: -webkit-flex; display: flex; height: 1rem; overflow: hidden; font-size: 0.65625rem; background-color: #e9ecef; border-radius: 0.25rem;}.progress-bar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; color: #FFFFFF; text-align: center; white-space: nowrap; background-color: #00A09D; transition: width 0.6s ease;}@media (prefers-reduced-motion: reduce){.progress-bar{transition: none;}}.progress-bar-striped{background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 1rem 1rem;}.progress-bar-animated{animation: progress-bar-stripes 1s linear infinite;}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation: none;}}.media{display: -webkit-box; display: -webkit-flex; display: flex; align-items: flex-start;}.media-body{flex: 1;}.list-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0;}.list-group-item-action{width: 100%; color: #495057; text-align: inherit;}.list-group-item-action:hover, .list-group-item-action:focus{z-index: 1; color: #495057; text-decoration: none; background-color: #f8f9fa;}.list-group-item-action:active{color: #212529; background-color: #e9ecef;}.list-group-item{position: relative; display: block; padding: 0.75rem 1.25rem; margin-bottom: -1px; background-color: #FFFFFF; border: 1px solid rgba(0, 0, 0, 0.125);}.list-group-item:first-child{border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.list-group-item:last-child{margin-bottom: 0; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.list-group-item.disabled, .list-group-item:disabled{color: #6c757d; pointer-events: none; background-color: #FFFFFF;}.list-group-item.active{z-index: 2; color: #FFFFFF; background-color: #00A09D; border-color: #00A09D;}.list-group-horizontal{flex-direction: row;}.list-group-horizontal .list-group-item{margin-right: -1px; margin-bottom: 0;}.list-group-horizontal .list-group-item:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal .list-group-item:last-child{margin-right: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0;}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction: row;}.list-group-horizontal-sm .list-group-item{margin-right: -1px; margin-bottom: 0;}.list-group-horizontal-sm .list-group-item:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-sm .list-group-item:last-child{margin-right: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0;}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction: row;}.list-group-horizontal-md .list-group-item{margin-right: -1px; margin-bottom: 0;}.list-group-horizontal-md .list-group-item:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-md .list-group-item:last-child{margin-right: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0;}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction: row;}.list-group-horizontal-lg .list-group-item{margin-right: -1px; margin-bottom: 0;}.list-group-horizontal-lg .list-group-item:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-lg .list-group-item:last-child{margin-right: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0;}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction: row;}.list-group-horizontal-xl .list-group-item{margin-right: -1px; margin-bottom: 0;}.list-group-horizontal-xl .list-group-item:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-xl .list-group-item:last-child{margin-right: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0;}}.list-group-flush .list-group-item{border-right: 0; border-left: 0; border-radius: 0;}.list-group-flush .list-group-item:last-child{margin-bottom: -1px;}.list-group-flush:first-child .list-group-item:first-child{border-top: 0;}.list-group-flush:last-child .list-group-item:last-child{margin-bottom: 0; border-bottom: 0;}.list-group-item-primary{color: #005352; background-color: #b8e4e4;}.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus{color: #005352; background-color: #a6dddd;}.list-group-item-primary.list-group-item-action.active{color: #FFFFFF; background-color: #005352; border-color: #005352;}.list-group-item-secondary{color: #462f40; background-color: #ddd1da;}.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus{color: #462f40; background-color: #d2c2ce;}.list-group-item-secondary.list-group-item-action.active{color: #FFFFFF; background-color: #462f40; border-color: #462f40;}.list-group-item-success{color: #155724; background-color: #c3e6cb;}.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus{color: #155724; background-color: #b1dfbb;}.list-group-item-success.list-group-item-action.active{color: #FFFFFF; background-color: #155724; border-color: #155724;}.list-group-item-info{color: #0c5460; background-color: #bee5eb;}.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus{color: #0c5460; background-color: #abdde5;}.list-group-item-info.list-group-item-action.active{color: #FFFFFF; background-color: #0c5460; border-color: #0c5460;}.list-group-item-warning{color: #856404; background-color: #ffeeba;}.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus{color: #856404; background-color: #ffe8a1;}.list-group-item-warning.list-group-item-action.active{color: #FFFFFF; background-color: #856404; border-color: #856404;}.list-group-item-danger{color: #721c24; background-color: #f5c6cb;}.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus{color: #721c24; background-color: #f1b0b7;}.list-group-item-danger.list-group-item-action.active{color: #FFFFFF; background-color: #721c24; border-color: #721c24;}.list-group-item-light{color: #808181; background-color: #fcfdfd;}.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus{color: #808181; background-color: #edf3f3;}.list-group-item-light.list-group-item-action.active{color: #FFFFFF; background-color: #808181; border-color: #808181;}.list-group-item-dark{color: #0b1010; background-color: #bdc0c0;}.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus{color: #0b1010; background-color: #b0b4b4;}.list-group-item-dark.list-group-item-action.active{color: #FFFFFF; background-color: #0b1010; border-color: #0b1010;}.list-group-item-alpha{color: #005352; background-color: #b8e4e4;}.list-group-item-alpha.list-group-item-action:hover, .list-group-item-alpha.list-group-item-action:focus{color: #005352; background-color: #a6dddd;}.list-group-item-alpha.list-group-item-action.active{color: #FFFFFF; background-color: #005352; border-color: #005352;}.list-group-item-beta{color: #462f40; background-color: #ddd1da;}.list-group-item-beta.list-group-item-action:hover, .list-group-item-beta.list-group-item-action:focus{color: #462f40; background-color: #d2c2ce;}.list-group-item-beta.list-group-item-action.active{color: #FFFFFF; background-color: #462f40; border-color: #462f40;}.list-group-item-gamma{color: #302f43; background-color: #d1d1db;}.list-group-item-gamma.list-group-item-action:hover, .list-group-item-gamma.list-group-item-action:focus{color: #302f43; background-color: #c3c3d0;}.list-group-item-gamma.list-group-item-action.active{color: #FFFFFF; background-color: #302f43; border-color: #302f43;}.list-group-item-delta{color: #2f4752; background-color: #d1dee4;}.list-group-item-delta.list-group-item-action:hover, .list-group-item-delta.list-group-item-action:focus{color: #2f4752; background-color: #c1d2db;}.list-group-item-delta.list-group-item-action.active{color: #FFFFFF; background-color: #2f4752; border-color: #2f4752;}.list-group-item-epsilon{color: #773a3e; background-color: #f7d7d9;}.list-group-item-epsilon.list-group-item-action:hover, .list-group-item-epsilon.list-group-item-action:focus{color: #773a3e; background-color: #f3c2c5;}.list-group-item-epsilon.list-group-item-action.active{color: #FFFFFF; background-color: #773a3e; border-color: #773a3e;}.close{float: right; font-size: 1.3125rem; font-weight: 700; line-height: 1; color: #000000; text-shadow: 0 1px 0 #FFFFFF; opacity: .5;}@media (max-width: 1200px){.close{font-size: calc(1.25625rem + 0.075vw) ;}}.close:hover{color: #000000; text-decoration: none;}.close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus{opacity: .75;}button.close{padding: 0; background-color: transparent; border: 0; -webkit-appearance: none; -moz-appearance: none; appearance: none;}a.close.disabled{pointer-events: none;}.toast{max-width: 350px; overflow: hidden; font-size: 0.875rem; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); backdrop-filter: blur(10px); opacity: 0; border-radius: 0.25rem;}.toast:not(:last-child){margin-bottom: 0.75rem;}.toast.showing{opacity: 1;}.toast.show{display: block; opacity: 1;}.toast.hide{display: none;}.toast-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.25rem 0.75rem; color: #6c757d; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border-bottom: 1px solid rgba(0, 0, 0, 0.05);}.toast-body{padding: 0.75rem;}.modal-open{overflow: hidden;}.modal-open .modal{overflow-x: hidden; overflow-y: auto;}.modal{position: fixed; top: 0; left: 0; z-index: 1050; display: none; width: 100%; height: 100%; overflow: hidden; outline: 0;}.modal-dialog{position: relative; width: auto; margin: 0.5rem; pointer-events: none;}.modal.fade .modal-dialog{transition: transform 0.3s ease-out; transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition: none;}}.modal.show .modal-dialog{transform: none;}.modal-dialog-scrollable{display: -webkit-box; display: -webkit-flex; display: flex; max-height: calc(100% - 1rem);}.modal-dialog-scrollable .modal-content{max-height: calc(100vh - 1rem); overflow: hidden;}.modal-dialog-scrollable .modal-header, .modal-dialog-scrollable .modal-footer{flex-shrink: 0;}.modal-dialog-scrollable .modal-body{overflow-y: auto;}.modal-dialog-centered{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: calc(100% - 1rem);}.modal-dialog-centered::before{display: block; height: calc(100vh - 1rem); content: "";}.modal-dialog-centered.modal-dialog-scrollable{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; height: 100%;}.modal-dialog-centered.modal-dialog-scrollable .modal-content{max-height: none;}.modal-dialog-centered.modal-dialog-scrollable::before{content: none;}.modal-content{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; pointer-events: auto; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem; outline: 0;}.modal-backdrop{position: fixed; top: 0; left: 0; z-index: 1040; width: 100vw; height: 100vh; background-color: #000000;}.modal-backdrop.fade{opacity: 0;}.modal-backdrop.show{opacity: 0.5;}.modal-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: flex-start; justify-content: space-between; padding: 1rem 1rem; border-bottom: 1px solid #dee2e6; border-top-left-radius: 0.3rem; border-top-right-radius: 0.3rem;}.modal-header .close{padding: 1rem 1rem; margin: -1rem -1rem -1rem auto;}.modal-title{margin-bottom: 0; line-height: 1.5;}.modal-body{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1rem;}.modal-footer{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; -webkit-box-pack: end; justify-content: flex-end; padding: 1rem; border-top: 1px solid #dee2e6; border-bottom-right-radius: 0.3rem; border-bottom-left-radius: 0.3rem;}.modal-footer > :not(:first-child){margin-left: .25rem;}.modal-footer > :not(:last-child){margin-right: .25rem;}.modal-scrollbar-measure{position: absolute; top: -9999px; width: 50px; height: 50px; overflow: scroll;}@media (min-width: 576px){.modal-dialog{max-width: 650px; margin: 1.75rem auto;}.modal-dialog-scrollable{max-height: calc(100% - 3.5rem);}.modal-dialog-scrollable .modal-content{max-height: calc(100vh - 3.5rem);}.modal-dialog-centered{min-height: calc(100% - 3.5rem);}.modal-dialog-centered::before{height: calc(100vh - 3.5rem);}.modal-sm{max-width: 300px;}}@media (min-width: 992px){.modal-lg, .modal-xl{max-width: 980px;}}@media (min-width: 1200px){.modal-xl{max-width: 1140px;}}.tooltip{position: absolute; z-index: 1070; display: block; margin: 0; font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif; font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.75rem; word-wrap: break-word; opacity: 0;}.tooltip.show{opacity: 0.9;}.tooltip .arrow{position: absolute; display: block; width: 0.8rem; height: 0.4rem;}.tooltip .arrow::before{position: absolute; content: ""; border-color: transparent; border-style: solid;}.bs-tooltip-top, .bs-tooltip-auto[x-placement^="top"]{padding: 0.4rem 0;}.bs-tooltip-top .arrow, .bs-tooltip-auto[x-placement^="top"] .arrow{bottom: 0;}.bs-tooltip-top .arrow::before, .bs-tooltip-auto[x-placement^="top"] .arrow::before{top: 0; border-width: 0.4rem 0.4rem 0; border-top-color: #000000;}.bs-tooltip-right, .bs-tooltip-auto[x-placement^="right"]{padding: 0 0.4rem;}.bs-tooltip-right .arrow, .bs-tooltip-auto[x-placement^="right"] .arrow{left: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-right .arrow::before, .bs-tooltip-auto[x-placement^="right"] .arrow::before{right: 0; border-width: 0.4rem 0.4rem 0.4rem 0; border-right-color: #000000;}.bs-tooltip-bottom, .bs-tooltip-auto[x-placement^="bottom"]{padding: 0.4rem 0;}.bs-tooltip-bottom .arrow, .bs-tooltip-auto[x-placement^="bottom"] .arrow{top: 0;}.bs-tooltip-bottom .arrow::before, .bs-tooltip-auto[x-placement^="bottom"] .arrow::before{bottom: 0; border-width: 0 0.4rem 0.4rem; border-bottom-color: #000000;}.bs-tooltip-left, .bs-tooltip-auto[x-placement^="left"]{padding: 0 0.4rem;}.bs-tooltip-left .arrow, .bs-tooltip-auto[x-placement^="left"] .arrow{right: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-left .arrow::before, .bs-tooltip-auto[x-placement^="left"] .arrow::before{left: 0; border-width: 0.4rem 0 0.4rem 0.4rem; border-left-color: #000000;}.tooltip-inner{max-width: 200px; padding: 0.25rem 0.5rem; color: #FFFFFF; text-align: center; background-color: #000000; border-radius: 0.25rem;}.popover{position: absolute; top: 0; left: 0; z-index: 1060; display: block; max-width: 276px; font-family: "Roboto", "Odoo Unicode Support Noto", sans-serif; font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.75rem; word-wrap: break-word; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem;}.popover .arrow{position: absolute; display: block; width: 1rem; height: 0.5rem; margin: 0 0.3rem;}.popover .arrow::before, .popover .arrow::after{position: absolute; display: block; content: ""; border-color: transparent; border-style: solid;}.bs-popover-top, .bs-popover-auto[x-placement^="top"]{margin-bottom: 0.5rem;}.bs-popover-top > .arrow, .bs-popover-auto[x-placement^="top"] > .arrow{bottom: calc((0.5rem + 1px) * -1);}.bs-popover-top > .arrow::before, .bs-popover-auto[x-placement^="top"] > .arrow::before{bottom: 0; border-width: 0.5rem 0.5rem 0; border-top-color: rgba(0, 0, 0, 0.25);}.bs-popover-top > .arrow::after, .bs-popover-auto[x-placement^="top"] > .arrow::after{bottom: 1px; border-width: 0.5rem 0.5rem 0; border-top-color: #FFFFFF;}.bs-popover-right, .bs-popover-auto[x-placement^="right"]{margin-left: 0.5rem;}.bs-popover-right > .arrow, .bs-popover-auto[x-placement^="right"] > .arrow{left: calc((0.5rem + 1px) * -1); width: 0.5rem; height: 1rem; margin: 0.3rem 0;}.bs-popover-right > .arrow::before, .bs-popover-auto[x-placement^="right"] > .arrow::before{left: 0; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: rgba(0, 0, 0, 0.25);}.bs-popover-right > .arrow::after, .bs-popover-auto[x-placement^="right"] > .arrow::after{left: 1px; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: #FFFFFF;}.bs-popover-bottom, .bs-popover-auto[x-placement^="bottom"]{margin-top: 0.5rem;}.bs-popover-bottom > .arrow, .bs-popover-auto[x-placement^="bottom"] > .arrow{top: calc((0.5rem + 1px) * -1);}.bs-popover-bottom > .arrow::before, .bs-popover-auto[x-placement^="bottom"] > .arrow::before{top: 0; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: rgba(0, 0, 0, 0.25);}.bs-popover-bottom > .arrow::after, .bs-popover-auto[x-placement^="bottom"] > .arrow::after{top: 1px; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: #FFFFFF;}.bs-popover-bottom .popover-header::before, .bs-popover-auto[x-placement^="bottom"] .popover-header::before{position: absolute; top: 0; left: 50%; display: block; width: 1rem; margin-left: -0.5rem; content: ""; border-bottom: 1px solid #f7f7f7;}.bs-popover-left, .bs-popover-auto[x-placement^="left"]{margin-right: 0.5rem;}.bs-popover-left > .arrow, .bs-popover-auto[x-placement^="left"] > .arrow{right: calc((0.5rem + 1px) * -1); width: 0.5rem; height: 1rem; margin: 0.3rem 0;}.bs-popover-left > .arrow::before, .bs-popover-auto[x-placement^="left"] > .arrow::before{right: 0; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: rgba(0, 0, 0, 0.25);}.bs-popover-left > .arrow::after, .bs-popover-auto[x-placement^="left"] > .arrow::after{right: 1px; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: #FFFFFF;}.popover-header{padding: 0.5rem 0.75rem; margin-bottom: 0; font-size: 0.875rem; background-color: #f7f7f7; border-bottom: 1px solid #ebebeb; border-top-left-radius: calc(0.3rem - 1px); border-top-right-radius: calc(0.3rem - 1px);}.popover-header:empty{display: none;}.popover-body{padding: 0.5rem 0.75rem; color: #212529;}.carousel{position: relative;}.carousel.pointer-event{touch-action: pan-y;}.carousel-inner{position: relative; width: 100%; overflow: hidden;}.carousel-inner::after{display: block; clear: both; content: "";}.carousel-item{position: relative; display: none; float: left; width: 100%; margin-right: -100%; backface-visibility: hidden; transition: transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce){.carousel-item{transition: none;}}.carousel-item.active, .carousel-item-next, .carousel-item-prev{display: block;}.carousel-item-next:not(.carousel-item-left), .active.carousel-item-right{transform: translateX(100%);}.carousel-item-prev:not(.carousel-item-right), .active.carousel-item-left{transform: translateX(-100%);}.carousel-fade .carousel-item{opacity: 0; transition-property: opacity; transform: none;}.carousel-fade .carousel-item.active, .carousel-fade .carousel-item-next.carousel-item-left, .carousel-fade .carousel-item-prev.carousel-item-right{z-index: 1; opacity: 1;}.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right{z-index: 0; opacity: 0; transition: 0s 0.6s opacity;}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right{transition: none;}}.carousel-control-prev, .carousel-control-next{position: absolute; top: 0; bottom: 0; z-index: 1; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: 15%; color: #FFFFFF; text-align: center; opacity: 0.5; transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce){.carousel-control-prev, .carousel-control-next{transition: none;}}.carousel-control-prev:hover, .carousel-control-prev:focus, .carousel-control-next:hover, .carousel-control-next:focus{color: #FFFFFF; text-decoration: none; outline: 0; opacity: 0.9;}.carousel-control-prev{left: 0;}.carousel-control-next{right: 0;}.carousel-control-prev-icon, .carousel-control-next-icon{display: inline-block; width: 20px; height: 20px; background: no-repeat 50% / 100% 100%;}.carousel-control-prev-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e");}.carousel-control-next-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23FFFFFF' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e");}.carousel-indicators{position: absolute; right: 0; bottom: 0; left: 0; z-index: 15; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; padding-left: 0; margin-right: 15%; margin-left: 15%; list-style: none;}.carousel-indicators li{box-sizing: content-box; -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 30px; height: 3px; margin-right: 3px; margin-left: 3px; text-indent: -999px; cursor: pointer; background-color: #FFFFFF; background-clip: padding-box; border-top: 10px solid transparent; border-bottom: 10px solid transparent; opacity: .5; transition: opacity 0.6s ease;}@media (prefers-reduced-motion: reduce){.carousel-indicators li{transition: none;}}.carousel-indicators .active{opacity: 1;}.carousel-caption{position: absolute; right: 15%; bottom: 20px; left: 15%; z-index: 10; padding-top: 20px; padding-bottom: 20px; color: #FFFFFF; text-align: center;}@keyframes spinner-border{to{transform: rotate(360deg);}}.spinner-border{display: inline-block; width: 2rem; height: 2rem; vertical-align: text-bottom; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: spinner-border .75s linear infinite;}.spinner-border-sm{width: 1rem; height: 1rem; border-width: 0.2em;}@keyframes spinner-grow{0%{transform: scale(0);}50%{opacity: 1;}}.spinner-grow{display: inline-block; width: 2rem; height: 2rem; vertical-align: text-bottom; background-color: currentColor; border-radius: 50%; opacity: 0; animation: spinner-grow .75s linear infinite;}.spinner-grow-sm{width: 1rem; height: 1rem;}.align-baseline{vertical-align: baseline !important;}.align-top{vertical-align: top !important;}.align-middle{vertical-align: middle !important;}.align-bottom{vertical-align: bottom !important;}.align-text-bottom{vertical-align: text-bottom !important;}.align-text-top{vertical-align: text-top !important;}.bg-primary{background-color: #00A09D !important; color: #FFFFFF;}.bg-primary .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-primary:hover, a.bg-primary:focus, button.bg-primary:hover, button.bg-primary:focus{background-color: #006d6b !important; color: #FFFFFF;}.bg-secondary{background-color: #875A7B !important; color: #FFFFFF;}.bg-secondary .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-secondary:hover, a.bg-secondary:focus, button.bg-secondary:hover, button.bg-secondary:focus{background-color: #68465f !important; color: #FFFFFF;}.bg-success{background-color: #28a745 !important; color: #FFFFFF;}.bg-success .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-success:hover, a.bg-success:focus, button.bg-success:hover, button.bg-success:focus{background-color: #1e7e34 !important; color: #FFFFFF;}.bg-info{background-color: #17a2b8 !important; color: #FFFFFF;}.bg-info .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-info:hover, a.bg-info:focus, button.bg-info:hover, button.bg-info:focus{background-color: #117a8b !important; color: #FFFFFF;}.bg-warning{background-color: #ffc107 !important; color: #212529;}.bg-warning .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-warning:hover, a.bg-warning:focus, button.bg-warning:hover, button.bg-warning:focus{background-color: #d39e00 !important; color: #212529;}.bg-danger{background-color: #dc3545 !important; color: #FFFFFF;}.bg-danger .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-danger:hover, a.bg-danger:focus, button.bg-danger:hover, button.bg-danger:focus{background-color: #bd2130 !important; color: #FFFFFF;}.bg-light{background-color: #f6f9f9 !important; color: #212529;}.bg-light .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-light:hover, a.bg-light:focus, button.bg-light:hover, button.bg-light:focus{background-color: #d7e4e4 !important; color: #212529;}.bg-dark{background-color: #141f1e !important; color: #FFFFFF;}.bg-dark .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-dark:hover, a.bg-dark:focus, button.bg-dark:hover, button.bg-dark:focus{background-color: black !important; color: #FFFFFF;}.bg-alpha{background-color: #00A09D !important; color: #FFFFFF;}.bg-alpha .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-alpha:hover, a.bg-alpha:focus, button.bg-alpha:hover, button.bg-alpha:focus{background-color: #006d6b !important; color: #FFFFFF;}.bg-beta{background-color: #875A7B !important; color: #FFFFFF;}.bg-beta .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-beta:hover, a.bg-beta:focus, button.bg-beta:hover, button.bg-beta:focus{background-color: #68465f !important; color: #FFFFFF;}.bg-gamma{background-color: #5C5B80 !important; color: #FFFFFF;}.bg-gamma .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-gamma:hover, a.bg-gamma:focus, button.bg-gamma:hover, button.bg-gamma:focus{background-color: #474662 !important; color: #FFFFFF;}.bg-delta{background-color: #5B899E !important; color: #FFFFFF;}.bg-delta .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-delta:hover, a.bg-delta:focus, button.bg-delta:hover, button.bg-delta:focus{background-color: #486d7e !important; color: #FFFFFF;}.bg-epsilon{background-color: #E46F78 !important; color: #FFFFFF;}.bg-epsilon .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-epsilon:hover, a.bg-epsilon:focus, button.bg-epsilon:hover, button.bg-epsilon:focus{background-color: #dc4450 !important; color: #FFFFFF;}.bg-white{background-color: #FFFFFF !important;}.bg-transparent{background-color: transparent !important;}.border{border: 1px solid #dee2e6 !important;}.border-top{border-top: 1px solid #dee2e6 !important;}.border-right{border-right: 1px solid #dee2e6 !important;}.border-bottom{border-bottom: 1px solid #dee2e6 !important;}.border-left{border-left: 1px solid #dee2e6 !important;}.border-0{border: 0 !important;}.border-top-0{border-top: 0 !important;}.border-right-0{border-right: 0 !important;}.border-bottom-0{border-bottom: 0 !important;}.border-left-0{border-left: 0 !important;}.border-primary{border-color: #00A09D !important;}.border-secondary{border-color: #875A7B !important;}.border-success{border-color: #28a745 !important;}.border-info{border-color: #17a2b8 !important;}.border-warning{border-color: #ffc107 !important;}.border-danger{border-color: #dc3545 !important;}.border-light{border-color: #f6f9f9 !important;}.border-dark{border-color: #141f1e !important;}.border-alpha{border-color: #00A09D !important;}.border-beta{border-color: #875A7B !important;}.border-gamma{border-color: #5C5B80 !important;}.border-delta{border-color: #5B899E !important;}.border-epsilon{border-color: #E46F78 !important;}.border-white{border-color: #FFFFFF !important;}.rounded-sm{border-radius: 0.2rem !important;}.rounded{border-radius: 0.25rem !important;}.rounded-top{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-right{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-bottom{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-left{border-top-left-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-lg{border-radius: 0.3rem !important;}.rounded-circle{border-radius: 50% !important;}.rounded-pill{border-radius: 50rem !important;}.rounded-0{border-radius: 0 !important;}.clearfix::after{display: block; clear: both; content: "";}.d-none{display: none !important;}.d-inline{display: inline !important;}.d-inline-block{display: inline-block !important;}.d-block{display: block !important;}.d-table{display: table !important;}.d-table-row{display: table-row !important;}.d-table-cell{display: table-cell !important;}.d-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}@media (min-width: 576px){.d-sm-none{display: none !important;}.d-sm-inline{display: inline !important;}.d-sm-inline-block{display: inline-block !important;}.d-sm-block{display: block !important;}.d-sm-table{display: table !important;}.d-sm-table-row{display: table-row !important;}.d-sm-table-cell{display: table-cell !important;}.d-sm-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-sm-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}}@media (min-width: 768px){.d-md-none{display: none !important;}.d-md-inline{display: inline !important;}.d-md-inline-block{display: inline-block !important;}.d-md-block{display: block !important;}.d-md-table{display: table !important;}.d-md-table-row{display: table-row !important;}.d-md-table-cell{display: table-cell !important;}.d-md-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-md-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}}@media (min-width: 992px){.d-lg-none{display: none !important;}.d-lg-inline{display: inline !important;}.d-lg-inline-block{display: inline-block !important;}.d-lg-block{display: block !important;}.d-lg-table{display: table !important;}.d-lg-table-row{display: table-row !important;}.d-lg-table-cell{display: table-cell !important;}.d-lg-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-lg-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}}@media (min-width: 1200px){.d-xl-none{display: none !important;}.d-xl-inline{display: inline !important;}.d-xl-inline-block{display: inline-block !important;}.d-xl-block{display: block !important;}.d-xl-table{display: table !important;}.d-xl-table-row{display: table-row !important;}.d-xl-table-cell{display: table-cell !important;}.d-xl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}}@media print{.d-print-none{display: none !important;}.d-print-inline{display: inline !important;}.d-print-inline-block{display: inline-block !important;}.d-print-block{display: block !important;}.d-print-table{display: table !important;}.d-print-table-row{display: table-row !important;}.d-print-table-cell{display: table-cell !important;}.d-print-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-print-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}}.embed-responsive{position: relative; display: block; width: 100%; padding: 0; overflow: hidden;}.embed-responsive::before{display: block; content: "";}.embed-responsive .embed-responsive-item, .embed-responsive iframe, .embed-responsive embed, .embed-responsive object, .embed-responsive video{position: absolute; top: 0; bottom: 0; left: 0; width: 100%; height: 100%; border: 0;}.embed-responsive-21by9::before{padding-top: 42.85714286%;}.embed-responsive-16by9::before{padding-top: 56.25%;}.embed-responsive-4by3::before{padding-top: 75%;}.embed-responsive-1by1::before{padding-top: 100%;}.flex-row{flex-direction: row !important;}.flex-column{flex-direction: column !important;}.flex-row-reverse{flex-direction: row-reverse !important;}.flex-column-reverse{flex-direction: column-reverse !important;}.flex-wrap{flex-wrap: wrap !important;}.flex-nowrap{flex-wrap: nowrap !important;}.flex-wrap-reverse{flex-wrap: wrap-reverse !important;}.flex-fill{flex: 1 1 auto !important;}.flex-grow-0{flex-grow: 0 !important;}.flex-grow-1{flex-grow: 1 !important;}.flex-shrink-0{flex-shrink: 0 !important;}.flex-shrink-1{flex-shrink: 1 !important;}.justify-content-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-center{justify-content: center !important;}.justify-content-between{justify-content: space-between !important;}.justify-content-around{justify-content: space-around !important;}.align-items-start{align-items: flex-start !important;}.align-items-end{align-items: flex-end !important;}.align-items-center{align-items: center !important;}.align-items-baseline{align-items: baseline !important;}.align-items-stretch{align-items: stretch !important;}.align-content-start{align-content: flex-start !important;}.align-content-end{align-content: flex-end !important;}.align-content-center{align-content: center !important;}.align-content-between{align-content: space-between !important;}.align-content-around{align-content: space-around !important;}.align-content-stretch{align-content: stretch !important;}.align-self-auto{align-self: auto !important;}.align-self-start{align-self: flex-start !important;}.align-self-end{align-self: flex-end !important;}.align-self-center{align-self: center !important;}.align-self-baseline{align-self: baseline !important;}.align-self-stretch{align-self: stretch !important;}@media (min-width: 576px){.flex-sm-row{flex-direction: row !important;}.flex-sm-column{flex-direction: column !important;}.flex-sm-row-reverse{flex-direction: row-reverse !important;}.flex-sm-column-reverse{flex-direction: column-reverse !important;}.flex-sm-wrap{flex-wrap: wrap !important;}.flex-sm-nowrap{flex-wrap: nowrap !important;}.flex-sm-wrap-reverse{flex-wrap: wrap-reverse !important;}.flex-sm-fill{flex: 1 1 auto !important;}.flex-sm-grow-0{flex-grow: 0 !important;}.flex-sm-grow-1{flex-grow: 1 !important;}.flex-sm-shrink-0{flex-shrink: 0 !important;}.flex-sm-shrink-1{flex-shrink: 1 !important;}.justify-content-sm-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-sm-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-sm-center{justify-content: center !important;}.justify-content-sm-between{justify-content: space-between !important;}.justify-content-sm-around{justify-content: space-around !important;}.align-items-sm-start{align-items: flex-start !important;}.align-items-sm-end{align-items: flex-end !important;}.align-items-sm-center{align-items: center !important;}.align-items-sm-baseline{align-items: baseline !important;}.align-items-sm-stretch{align-items: stretch !important;}.align-content-sm-start{align-content: flex-start !important;}.align-content-sm-end{align-content: flex-end !important;}.align-content-sm-center{align-content: center !important;}.align-content-sm-between{align-content: space-between !important;}.align-content-sm-around{align-content: space-around !important;}.align-content-sm-stretch{align-content: stretch !important;}.align-self-sm-auto{align-self: auto !important;}.align-self-sm-start{align-self: flex-start !important;}.align-self-sm-end{align-self: flex-end !important;}.align-self-sm-center{align-self: center !important;}.align-self-sm-baseline{align-self: baseline !important;}.align-self-sm-stretch{align-self: stretch !important;}}@media (min-width: 768px){.flex-md-row{flex-direction: row !important;}.flex-md-column{flex-direction: column !important;}.flex-md-row-reverse{flex-direction: row-reverse !important;}.flex-md-column-reverse{flex-direction: column-reverse !important;}.flex-md-wrap{flex-wrap: wrap !important;}.flex-md-nowrap{flex-wrap: nowrap !important;}.flex-md-wrap-reverse{flex-wrap: wrap-reverse !important;}.flex-md-fill{flex: 1 1 auto !important;}.flex-md-grow-0{flex-grow: 0 !important;}.flex-md-grow-1{flex-grow: 1 !important;}.flex-md-shrink-0{flex-shrink: 0 !important;}.flex-md-shrink-1{flex-shrink: 1 !important;}.justify-content-md-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-md-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-md-center{justify-content: center !important;}.justify-content-md-between{justify-content: space-between !important;}.justify-content-md-around{justify-content: space-around !important;}.align-items-md-start{align-items: flex-start !important;}.align-items-md-end{align-items: flex-end !important;}.align-items-md-center{align-items: center !important;}.align-items-md-baseline{align-items: baseline !important;}.align-items-md-stretch{align-items: stretch !important;}.align-content-md-start{align-content: flex-start !important;}.align-content-md-end{align-content: flex-end !important;}.align-content-md-center{align-content: center !important;}.align-content-md-between{align-content: space-between !important;}.align-content-md-around{align-content: space-around !important;}.align-content-md-stretch{align-content: stretch !important;}.align-self-md-auto{align-self: auto !important;}.align-self-md-start{align-self: flex-start !important;}.align-self-md-end{align-self: flex-end !important;}.align-self-md-center{align-self: center !important;}.align-self-md-baseline{align-self: baseline !important;}.align-self-md-stretch{align-self: stretch !important;}}@media (min-width: 992px){.flex-lg-row{flex-direction: row !important;}.flex-lg-column{flex-direction: column !important;}.flex-lg-row-reverse{flex-direction: row-reverse !important;}.flex-lg-column-reverse{flex-direction: column-reverse !important;}.flex-lg-wrap{flex-wrap: wrap !important;}.flex-lg-nowrap{flex-wrap: nowrap !important;}.flex-lg-wrap-reverse{flex-wrap: wrap-reverse !important;}.flex-lg-fill{flex: 1 1 auto !important;}.flex-lg-grow-0{flex-grow: 0 !important;}.flex-lg-grow-1{flex-grow: 1 !important;}.flex-lg-shrink-0{flex-shrink: 0 !important;}.flex-lg-shrink-1{flex-shrink: 1 !important;}.justify-content-lg-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-lg-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-lg-center{justify-content: center !important;}.justify-content-lg-between{justify-content: space-between !important;}.justify-content-lg-around{justify-content: space-around !important;}.align-items-lg-start{align-items: flex-start !important;}.align-items-lg-end{align-items: flex-end !important;}.align-items-lg-center{align-items: center !important;}.align-items-lg-baseline{align-items: baseline !important;}.align-items-lg-stretch{align-items: stretch !important;}.align-content-lg-start{align-content: flex-start !important;}.align-content-lg-end{align-content: flex-end !important;}.align-content-lg-center{align-content: center !important;}.align-content-lg-between{align-content: space-between !important;}.align-content-lg-around{align-content: space-around !important;}.align-content-lg-stretch{align-content: stretch !important;}.align-self-lg-auto{align-self: auto !important;}.align-self-lg-start{align-self: flex-start !important;}.align-self-lg-end{align-self: flex-end !important;}.align-self-lg-center{align-self: center !important;}.align-self-lg-baseline{align-self: baseline !important;}.align-self-lg-stretch{align-self: stretch !important;}}@media (min-width: 1200px){.flex-xl-row{flex-direction: row !important;}.flex-xl-column{flex-direction: column !important;}.flex-xl-row-reverse{flex-direction: row-reverse !important;}.flex-xl-column-reverse{flex-direction: column-reverse !important;}.flex-xl-wrap{flex-wrap: wrap !important;}.flex-xl-nowrap{flex-wrap: nowrap !important;}.flex-xl-wrap-reverse{flex-wrap: wrap-reverse !important;}.flex-xl-fill{flex: 1 1 auto !important;}.flex-xl-grow-0{flex-grow: 0 !important;}.flex-xl-grow-1{flex-grow: 1 !important;}.flex-xl-shrink-0{flex-shrink: 0 !important;}.flex-xl-shrink-1{flex-shrink: 1 !important;}.justify-content-xl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xl-center{justify-content: center !important;}.justify-content-xl-between{justify-content: space-between !important;}.justify-content-xl-around{justify-content: space-around !important;}.align-items-xl-start{align-items: flex-start !important;}.align-items-xl-end{align-items: flex-end !important;}.align-items-xl-center{align-items: center !important;}.align-items-xl-baseline{align-items: baseline !important;}.align-items-xl-stretch{align-items: stretch !important;}.align-content-xl-start{align-content: flex-start !important;}.align-content-xl-end{align-content: flex-end !important;}.align-content-xl-center{align-content: center !important;}.align-content-xl-between{align-content: space-between !important;}.align-content-xl-around{align-content: space-around !important;}.align-content-xl-stretch{align-content: stretch !important;}.align-self-xl-auto{align-self: auto !important;}.align-self-xl-start{align-self: flex-start !important;}.align-self-xl-end{align-self: flex-end !important;}.align-self-xl-center{align-self: center !important;}.align-self-xl-baseline{align-self: baseline !important;}.align-self-xl-stretch{align-self: stretch !important;}}.float-left{float: left !important;}.float-right{float: right !important;}.float-none{float: none !important;}@media (min-width: 576px){.float-sm-left{float: left !important;}.float-sm-right{float: right !important;}.float-sm-none{float: none !important;}}@media (min-width: 768px){.float-md-left{float: left !important;}.float-md-right{float: right !important;}.float-md-none{float: none !important;}}@media (min-width: 992px){.float-lg-left{float: left !important;}.float-lg-right{float: right !important;}.float-lg-none{float: none !important;}}@media (min-width: 1200px){.float-xl-left{float: left !important;}.float-xl-right{float: right !important;}.float-xl-none{float: none !important;}}.overflow-auto{overflow: auto !important;}.overflow-hidden{overflow: hidden !important;}.position-static{position: static !important;}.position-relative{position: relative !important;}.position-absolute{position: absolute !important;}.position-fixed{position: fixed !important;}.position-sticky{position: sticky !important;}.fixed-top{position: fixed; top: 0; right: 0; left: 0; z-index: 1030;}.fixed-bottom{position: fixed; right: 0; bottom: 0; left: 0; z-index: 1030;}@supports (position: sticky){.sticky-top{position: sticky; top: 0; z-index: 1020;}}.sr-only{position: absolute; width: 1px; height: 1px; padding: 0; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;}.sr-only-focusable:active, .sr-only-focusable:focus{position: static; width: auto; height: auto; overflow: visible; clip: auto; white-space: normal;}.shadow-sm{box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;}.shadow{box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;}.shadow-lg{box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;}.shadow-none{box-shadow: none !important;}.w-25{width: 25% !important;}.w-50{width: 50% !important;}.w-75{width: 75% !important;}.w-100{width: 100% !important;}.w-auto{width: auto !important;}.w-0{width: 0 !important;}.h-25{height: 25% !important;}.h-50{height: 50% !important;}.h-75{height: 75% !important;}.h-100{height: 100% !important;}.h-auto{height: auto !important;}.h-0{height: 0 !important;}.mw-100{max-width: 100% !important;}.mh-100{max-height: 100% !important;}.min-vw-100{min-width: 100vw !important;}.min-vh-100{min-height: 100vh !important;}.vw-100{width: 100vw !important;}.vh-100{height: 100vh !important;}.stretched-link::after{position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; pointer-events: auto; content: ""; background-color: transparent;}.m-0{margin: 0 !important;}.mt-0, .my-0{margin-top: 0 !important;}.mr-0, .mx-0{margin-right: 0 !important;}.mb-0, .my-0{margin-bottom: 0 !important;}.ml-0, .mx-0{margin-left: 0 !important;}.m-1{margin: 0.25rem !important;}.mt-1, .my-1{margin-top: 0.25rem !important;}.mr-1, .mx-1{margin-right: 0.25rem !important;}.mb-1, .my-1{margin-bottom: 0.25rem !important;}.ml-1, .mx-1{margin-left: 0.25rem !important;}.m-2{margin: 0.5rem !important;}.mt-2, .my-2{margin-top: 0.5rem !important;}.mr-2, .mx-2{margin-right: 0.5rem !important;}.mb-2, .my-2{margin-bottom: 0.5rem !important;}.ml-2, .mx-2{margin-left: 0.5rem !important;}.m-3{margin: 1rem !important;}.mt-3, .my-3{margin-top: 1rem !important;}.mr-3, .mx-3{margin-right: 1rem !important;}.mb-3, .my-3{margin-bottom: 1rem !important;}.ml-3, .mx-3{margin-left: 1rem !important;}.m-4{margin: 1.5rem !important;}.mt-4, .my-4{margin-top: 1.5rem !important;}.mr-4, .mx-4{margin-right: 1.5rem !important;}.mb-4, .my-4{margin-bottom: 1.5rem !important;}.ml-4, .mx-4{margin-left: 1.5rem !important;}.m-5{margin: 3rem !important;}.mt-5, .my-5{margin-top: 3rem !important;}.mr-5, .mx-5{margin-right: 3rem !important;}.mb-5, .my-5{margin-bottom: 3rem !important;}.ml-5, .mx-5{margin-left: 3rem !important;}.p-0{padding: 0 !important;}.pt-0, .py-0{padding-top: 0 !important;}.pr-0, .px-0{padding-right: 0 !important;}.pb-0, .py-0{padding-bottom: 0 !important;}.pl-0, .px-0{padding-left: 0 !important;}.p-1{padding: 0.25rem !important;}.pt-1, .py-1{padding-top: 0.25rem !important;}.pr-1, .px-1{padding-right: 0.25rem !important;}.pb-1, .py-1{padding-bottom: 0.25rem !important;}.pl-1, .px-1{padding-left: 0.25rem !important;}.p-2{padding: 0.5rem !important;}.pt-2, .py-2{padding-top: 0.5rem !important;}.pr-2, .px-2{padding-right: 0.5rem !important;}.pb-2, .py-2{padding-bottom: 0.5rem !important;}.pl-2, .px-2{padding-left: 0.5rem !important;}.p-3{padding: 1rem !important;}.pt-3, .py-3{padding-top: 1rem !important;}.pr-3, .px-3{padding-right: 1rem !important;}.pb-3, .py-3{padding-bottom: 1rem !important;}.pl-3, .px-3{padding-left: 1rem !important;}.p-4{padding: 1.5rem !important;}.pt-4, .py-4{padding-top: 1.5rem !important;}.pr-4, .px-4{padding-right: 1.5rem !important;}.pb-4, .py-4{padding-bottom: 1.5rem !important;}.pl-4, .px-4{padding-left: 1.5rem !important;}.p-5{padding: 3rem !important;}.pt-5, .py-5{padding-top: 3rem !important;}.pr-5, .px-5{padding-right: 3rem !important;}.pb-5, .py-5{padding-bottom: 3rem !important;}.pl-5, .px-5{padding-left: 3rem !important;}.m-n1{margin: -0.25rem !important;}.mt-n1, .my-n1{margin-top: -0.25rem !important;}.mr-n1, .mx-n1{margin-right: -0.25rem !important;}.mb-n1, .my-n1{margin-bottom: -0.25rem !important;}.ml-n1, .mx-n1{margin-left: -0.25rem !important;}.m-n2{margin: -0.5rem !important;}.mt-n2, .my-n2{margin-top: -0.5rem !important;}.mr-n2, .mx-n2{margin-right: -0.5rem !important;}.mb-n2, .my-n2{margin-bottom: -0.5rem !important;}.ml-n2, .mx-n2{margin-left: -0.5rem !important;}.m-n3{margin: -1rem !important;}.mt-n3, .my-n3{margin-top: -1rem !important;}.mr-n3, .mx-n3{margin-right: -1rem !important;}.mb-n3, .my-n3{margin-bottom: -1rem !important;}.ml-n3, .mx-n3{margin-left: -1rem !important;}.m-n4{margin: -1.5rem !important;}.mt-n4, .my-n4{margin-top: -1.5rem !important;}.mr-n4, .mx-n4{margin-right: -1.5rem !important;}.mb-n4, .my-n4{margin-bottom: -1.5rem !important;}.ml-n4, .mx-n4{margin-left: -1.5rem !important;}.m-n5{margin: -3rem !important;}.mt-n5, .my-n5{margin-top: -3rem !important;}.mr-n5, .mx-n5{margin-right: -3rem !important;}.mb-n5, .my-n5{margin-bottom: -3rem !important;}.ml-n5, .mx-n5{margin-left: -3rem !important;}.m-auto{margin: auto !important;}.mt-auto, .my-auto{margin-top: auto !important;}.mr-auto, .mx-auto{margin-right: auto !important;}.mb-auto, .my-auto{margin-bottom: auto !important;}.ml-auto, .mx-auto{margin-left: auto !important;}@media (min-width: 576px){.m-sm-0{margin: 0 !important;}.mt-sm-0, .my-sm-0{margin-top: 0 !important;}.mr-sm-0, .mx-sm-0{margin-right: 0 !important;}.mb-sm-0, .my-sm-0{margin-bottom: 0 !important;}.ml-sm-0, .mx-sm-0{margin-left: 0 !important;}.m-sm-1{margin: 0.25rem !important;}.mt-sm-1, .my-sm-1{margin-top: 0.25rem !important;}.mr-sm-1, .mx-sm-1{margin-right: 0.25rem !important;}.mb-sm-1, .my-sm-1{margin-bottom: 0.25rem !important;}.ml-sm-1, .mx-sm-1{margin-left: 0.25rem !important;}.m-sm-2{margin: 0.5rem !important;}.mt-sm-2, .my-sm-2{margin-top: 0.5rem !important;}.mr-sm-2, .mx-sm-2{margin-right: 0.5rem !important;}.mb-sm-2, .my-sm-2{margin-bottom: 0.5rem !important;}.ml-sm-2, .mx-sm-2{margin-left: 0.5rem !important;}.m-sm-3{margin: 1rem !important;}.mt-sm-3, .my-sm-3{margin-top: 1rem !important;}.mr-sm-3, .mx-sm-3{margin-right: 1rem !important;}.mb-sm-3, .my-sm-3{margin-bottom: 1rem !important;}.ml-sm-3, .mx-sm-3{margin-left: 1rem !important;}.m-sm-4{margin: 1.5rem !important;}.mt-sm-4, .my-sm-4{margin-top: 1.5rem !important;}.mr-sm-4, .mx-sm-4{margin-right: 1.5rem !important;}.mb-sm-4, .my-sm-4{margin-bottom: 1.5rem !important;}.ml-sm-4, .mx-sm-4{margin-left: 1.5rem !important;}.m-sm-5{margin: 3rem !important;}.mt-sm-5, .my-sm-5{margin-top: 3rem !important;}.mr-sm-5, .mx-sm-5{margin-right: 3rem !important;}.mb-sm-5, .my-sm-5{margin-bottom: 3rem !important;}.ml-sm-5, .mx-sm-5{margin-left: 3rem !important;}.p-sm-0{padding: 0 !important;}.pt-sm-0, .py-sm-0{padding-top: 0 !important;}.pr-sm-0, .px-sm-0{padding-right: 0 !important;}.pb-sm-0, .py-sm-0{padding-bottom: 0 !important;}.pl-sm-0, .px-sm-0{padding-left: 0 !important;}.p-sm-1{padding: 0.25rem !important;}.pt-sm-1, .py-sm-1{padding-top: 0.25rem !important;}.pr-sm-1, .px-sm-1{padding-right: 0.25rem !important;}.pb-sm-1, .py-sm-1{padding-bottom: 0.25rem !important;}.pl-sm-1, .px-sm-1{padding-left: 0.25rem !important;}.p-sm-2{padding: 0.5rem !important;}.pt-sm-2, .py-sm-2{padding-top: 0.5rem !important;}.pr-sm-2, .px-sm-2{padding-right: 0.5rem !important;}.pb-sm-2, .py-sm-2{padding-bottom: 0.5rem !important;}.pl-sm-2, .px-sm-2{padding-left: 0.5rem !important;}.p-sm-3{padding: 1rem !important;}.pt-sm-3, .py-sm-3{padding-top: 1rem !important;}.pr-sm-3, .px-sm-3{padding-right: 1rem !important;}.pb-sm-3, .py-sm-3{padding-bottom: 1rem !important;}.pl-sm-3, .px-sm-3{padding-left: 1rem !important;}.p-sm-4{padding: 1.5rem !important;}.pt-sm-4, .py-sm-4{padding-top: 1.5rem !important;}.pr-sm-4, .px-sm-4{padding-right: 1.5rem !important;}.pb-sm-4, .py-sm-4{padding-bottom: 1.5rem !important;}.pl-sm-4, .px-sm-4{padding-left: 1.5rem !important;}.p-sm-5{padding: 3rem !important;}.pt-sm-5, .py-sm-5{padding-top: 3rem !important;}.pr-sm-5, .px-sm-5{padding-right: 3rem !important;}.pb-sm-5, .py-sm-5{padding-bottom: 3rem !important;}.pl-sm-5, .px-sm-5{padding-left: 3rem !important;}.m-sm-n1{margin: -0.25rem !important;}.mt-sm-n1, .my-sm-n1{margin-top: -0.25rem !important;}.mr-sm-n1, .mx-sm-n1{margin-right: -0.25rem !important;}.mb-sm-n1, .my-sm-n1{margin-bottom: -0.25rem !important;}.ml-sm-n1, .mx-sm-n1{margin-left: -0.25rem !important;}.m-sm-n2{margin: -0.5rem !important;}.mt-sm-n2, .my-sm-n2{margin-top: -0.5rem !important;}.mr-sm-n2, .mx-sm-n2{margin-right: -0.5rem !important;}.mb-sm-n2, .my-sm-n2{margin-bottom: -0.5rem !important;}.ml-sm-n2, .mx-sm-n2{margin-left: -0.5rem !important;}.m-sm-n3{margin: -1rem !important;}.mt-sm-n3, .my-sm-n3{margin-top: -1rem !important;}.mr-sm-n3, .mx-sm-n3{margin-right: -1rem !important;}.mb-sm-n3, .my-sm-n3{margin-bottom: -1rem !important;}.ml-sm-n3, .mx-sm-n3{margin-left: -1rem !important;}.m-sm-n4{margin: -1.5rem !important;}.mt-sm-n4, .my-sm-n4{margin-top: -1.5rem !important;}.mr-sm-n4, .mx-sm-n4{margin-right: -1.5rem !important;}.mb-sm-n4, .my-sm-n4{margin-bottom: -1.5rem !important;}.ml-sm-n4, .mx-sm-n4{margin-left: -1.5rem !important;}.m-sm-n5{margin: -3rem !important;}.mt-sm-n5, .my-sm-n5{margin-top: -3rem !important;}.mr-sm-n5, .mx-sm-n5{margin-right: -3rem !important;}.mb-sm-n5, .my-sm-n5{margin-bottom: -3rem !important;}.ml-sm-n5, .mx-sm-n5{margin-left: -3rem !important;}.m-sm-auto{margin: auto !important;}.mt-sm-auto, .my-sm-auto{margin-top: auto !important;}.mr-sm-auto, .mx-sm-auto{margin-right: auto !important;}.mb-sm-auto, .my-sm-auto{margin-bottom: auto !important;}.ml-sm-auto, .mx-sm-auto{margin-left: auto !important;}}@media (min-width: 768px){.m-md-0{margin: 0 !important;}.mt-md-0, .my-md-0{margin-top: 0 !important;}.mr-md-0, .mx-md-0{margin-right: 0 !important;}.mb-md-0, .my-md-0{margin-bottom: 0 !important;}.ml-md-0, .mx-md-0{margin-left: 0 !important;}.m-md-1{margin: 0.25rem !important;}.mt-md-1, .my-md-1{margin-top: 0.25rem !important;}.mr-md-1, .mx-md-1{margin-right: 0.25rem !important;}.mb-md-1, .my-md-1{margin-bottom: 0.25rem !important;}.ml-md-1, .mx-md-1{margin-left: 0.25rem !important;}.m-md-2{margin: 0.5rem !important;}.mt-md-2, .my-md-2{margin-top: 0.5rem !important;}.mr-md-2, .mx-md-2{margin-right: 0.5rem !important;}.mb-md-2, .my-md-2{margin-bottom: 0.5rem !important;}.ml-md-2, .mx-md-2{margin-left: 0.5rem !important;}.m-md-3{margin: 1rem !important;}.mt-md-3, .my-md-3{margin-top: 1rem !important;}.mr-md-3, .mx-md-3{margin-right: 1rem !important;}.mb-md-3, .my-md-3{margin-bottom: 1rem !important;}.ml-md-3, .mx-md-3{margin-left: 1rem !important;}.m-md-4{margin: 1.5rem !important;}.mt-md-4, .my-md-4{margin-top: 1.5rem !important;}.mr-md-4, .mx-md-4{margin-right: 1.5rem !important;}.mb-md-4, .my-md-4{margin-bottom: 1.5rem !important;}.ml-md-4, .mx-md-4{margin-left: 1.5rem !important;}.m-md-5{margin: 3rem !important;}.mt-md-5, .my-md-5{margin-top: 3rem !important;}.mr-md-5, .mx-md-5{margin-right: 3rem !important;}.mb-md-5, .my-md-5{margin-bottom: 3rem !important;}.ml-md-5, .mx-md-5{margin-left: 3rem !important;}.p-md-0{padding: 0 !important;}.pt-md-0, .py-md-0{padding-top: 0 !important;}.pr-md-0, .px-md-0{padding-right: 0 !important;}.pb-md-0, .py-md-0{padding-bottom: 0 !important;}.pl-md-0, .px-md-0{padding-left: 0 !important;}.p-md-1{padding: 0.25rem !important;}.pt-md-1, .py-md-1{padding-top: 0.25rem !important;}.pr-md-1, .px-md-1{padding-right: 0.25rem !important;}.pb-md-1, .py-md-1{padding-bottom: 0.25rem !important;}.pl-md-1, .px-md-1{padding-left: 0.25rem !important;}.p-md-2{padding: 0.5rem !important;}.pt-md-2, .py-md-2{padding-top: 0.5rem !important;}.pr-md-2, .px-md-2{padding-right: 0.5rem !important;}.pb-md-2, .py-md-2{padding-bottom: 0.5rem !important;}.pl-md-2, .px-md-2{padding-left: 0.5rem !important;}.p-md-3{padding: 1rem !important;}.pt-md-3, .py-md-3{padding-top: 1rem !important;}.pr-md-3, .px-md-3{padding-right: 1rem !important;}.pb-md-3, .py-md-3{padding-bottom: 1rem !important;}.pl-md-3, .px-md-3{padding-left: 1rem !important;}.p-md-4{padding: 1.5rem !important;}.pt-md-4, .py-md-4{padding-top: 1.5rem !important;}.pr-md-4, .px-md-4{padding-right: 1.5rem !important;}.pb-md-4, .py-md-4{padding-bottom: 1.5rem !important;}.pl-md-4, .px-md-4{padding-left: 1.5rem !important;}.p-md-5{padding: 3rem !important;}.pt-md-5, .py-md-5{padding-top: 3rem !important;}.pr-md-5, .px-md-5{padding-right: 3rem !important;}.pb-md-5, .py-md-5{padding-bottom: 3rem !important;}.pl-md-5, .px-md-5{padding-left: 3rem !important;}.m-md-n1{margin: -0.25rem !important;}.mt-md-n1, .my-md-n1{margin-top: -0.25rem !important;}.mr-md-n1, .mx-md-n1{margin-right: -0.25rem !important;}.mb-md-n1, .my-md-n1{margin-bottom: -0.25rem !important;}.ml-md-n1, .mx-md-n1{margin-left: -0.25rem !important;}.m-md-n2{margin: -0.5rem !important;}.mt-md-n2, .my-md-n2{margin-top: -0.5rem !important;}.mr-md-n2, .mx-md-n2{margin-right: -0.5rem !important;}.mb-md-n2, .my-md-n2{margin-bottom: -0.5rem !important;}.ml-md-n2, .mx-md-n2{margin-left: -0.5rem !important;}.m-md-n3{margin: -1rem !important;}.mt-md-n3, .my-md-n3{margin-top: -1rem !important;}.mr-md-n3, .mx-md-n3{margin-right: -1rem !important;}.mb-md-n3, .my-md-n3{margin-bottom: -1rem !important;}.ml-md-n3, .mx-md-n3{margin-left: -1rem !important;}.m-md-n4{margin: -1.5rem !important;}.mt-md-n4, .my-md-n4{margin-top: -1.5rem !important;}.mr-md-n4, .mx-md-n4{margin-right: -1.5rem !important;}.mb-md-n4, .my-md-n4{margin-bottom: -1.5rem !important;}.ml-md-n4, .mx-md-n4{margin-left: -1.5rem !important;}.m-md-n5{margin: -3rem !important;}.mt-md-n5, .my-md-n5{margin-top: -3rem !important;}.mr-md-n5, .mx-md-n5{margin-right: -3rem !important;}.mb-md-n5, .my-md-n5{margin-bottom: -3rem !important;}.ml-md-n5, .mx-md-n5{margin-left: -3rem !important;}.m-md-auto{margin: auto !important;}.mt-md-auto, .my-md-auto{margin-top: auto !important;}.mr-md-auto, .mx-md-auto{margin-right: auto !important;}.mb-md-auto, .my-md-auto{margin-bottom: auto !important;}.ml-md-auto, .mx-md-auto{margin-left: auto !important;}}@media (min-width: 992px){.m-lg-0{margin: 0 !important;}.mt-lg-0, .my-lg-0{margin-top: 0 !important;}.mr-lg-0, .mx-lg-0{margin-right: 0 !important;}.mb-lg-0, .my-lg-0{margin-bottom: 0 !important;}.ml-lg-0, .mx-lg-0{margin-left: 0 !important;}.m-lg-1{margin: 0.25rem !important;}.mt-lg-1, .my-lg-1{margin-top: 0.25rem !important;}.mr-lg-1, .mx-lg-1{margin-right: 0.25rem !important;}.mb-lg-1, .my-lg-1{margin-bottom: 0.25rem !important;}.ml-lg-1, .mx-lg-1{margin-left: 0.25rem !important;}.m-lg-2{margin: 0.5rem !important;}.mt-lg-2, .my-lg-2{margin-top: 0.5rem !important;}.mr-lg-2, .mx-lg-2{margin-right: 0.5rem !important;}.mb-lg-2, .my-lg-2{margin-bottom: 0.5rem !important;}.ml-lg-2, .mx-lg-2{margin-left: 0.5rem !important;}.m-lg-3{margin: 1rem !important;}.mt-lg-3, .my-lg-3{margin-top: 1rem !important;}.mr-lg-3, .mx-lg-3{margin-right: 1rem !important;}.mb-lg-3, .my-lg-3{margin-bottom: 1rem !important;}.ml-lg-3, .mx-lg-3{margin-left: 1rem !important;}.m-lg-4{margin: 1.5rem !important;}.mt-lg-4, .my-lg-4{margin-top: 1.5rem !important;}.mr-lg-4, .mx-lg-4{margin-right: 1.5rem !important;}.mb-lg-4, .my-lg-4{margin-bottom: 1.5rem !important;}.ml-lg-4, .mx-lg-4{margin-left: 1.5rem !important;}.m-lg-5{margin: 3rem !important;}.mt-lg-5, .my-lg-5{margin-top: 3rem !important;}.mr-lg-5, .mx-lg-5{margin-right: 3rem !important;}.mb-lg-5, .my-lg-5{margin-bottom: 3rem !important;}.ml-lg-5, .mx-lg-5{margin-left: 3rem !important;}.p-lg-0{padding: 0 !important;}.pt-lg-0, .py-lg-0{padding-top: 0 !important;}.pr-lg-0, .px-lg-0{padding-right: 0 !important;}.pb-lg-0, .py-lg-0{padding-bottom: 0 !important;}.pl-lg-0, .px-lg-0{padding-left: 0 !important;}.p-lg-1{padding: 0.25rem !important;}.pt-lg-1, .py-lg-1{padding-top: 0.25rem !important;}.pr-lg-1, .px-lg-1{padding-right: 0.25rem !important;}.pb-lg-1, .py-lg-1{padding-bottom: 0.25rem !important;}.pl-lg-1, .px-lg-1{padding-left: 0.25rem !important;}.p-lg-2{padding: 0.5rem !important;}.pt-lg-2, .py-lg-2{padding-top: 0.5rem !important;}.pr-lg-2, .px-lg-2{padding-right: 0.5rem !important;}.pb-lg-2, .py-lg-2{padding-bottom: 0.5rem !important;}.pl-lg-2, .px-lg-2{padding-left: 0.5rem !important;}.p-lg-3{padding: 1rem !important;}.pt-lg-3, .py-lg-3{padding-top: 1rem !important;}.pr-lg-3, .px-lg-3{padding-right: 1rem !important;}.pb-lg-3, .py-lg-3{padding-bottom: 1rem !important;}.pl-lg-3, .px-lg-3{padding-left: 1rem !important;}.p-lg-4{padding: 1.5rem !important;}.pt-lg-4, .py-lg-4{padding-top: 1.5rem !important;}.pr-lg-4, .px-lg-4{padding-right: 1.5rem !important;}.pb-lg-4, .py-lg-4{padding-bottom: 1.5rem !important;}.pl-lg-4, .px-lg-4{padding-left: 1.5rem !important;}.p-lg-5{padding: 3rem !important;}.pt-lg-5, .py-lg-5{padding-top: 3rem !important;}.pr-lg-5, .px-lg-5{padding-right: 3rem !important;}.pb-lg-5, .py-lg-5{padding-bottom: 3rem !important;}.pl-lg-5, .px-lg-5{padding-left: 3rem !important;}.m-lg-n1{margin: -0.25rem !important;}.mt-lg-n1, .my-lg-n1{margin-top: -0.25rem !important;}.mr-lg-n1, .mx-lg-n1{margin-right: -0.25rem !important;}.mb-lg-n1, .my-lg-n1{margin-bottom: -0.25rem !important;}.ml-lg-n1, .mx-lg-n1{margin-left: -0.25rem !important;}.m-lg-n2{margin: -0.5rem !important;}.mt-lg-n2, .my-lg-n2{margin-top: -0.5rem !important;}.mr-lg-n2, .mx-lg-n2{margin-right: -0.5rem !important;}.mb-lg-n2, .my-lg-n2{margin-bottom: -0.5rem !important;}.ml-lg-n2, .mx-lg-n2{margin-left: -0.5rem !important;}.m-lg-n3{margin: -1rem !important;}.mt-lg-n3, .my-lg-n3{margin-top: -1rem !important;}.mr-lg-n3, .mx-lg-n3{margin-right: -1rem !important;}.mb-lg-n3, .my-lg-n3{margin-bottom: -1rem !important;}.ml-lg-n3, .mx-lg-n3{margin-left: -1rem !important;}.m-lg-n4{margin: -1.5rem !important;}.mt-lg-n4, .my-lg-n4{margin-top: -1.5rem !important;}.mr-lg-n4, .mx-lg-n4{margin-right: -1.5rem !important;}.mb-lg-n4, .my-lg-n4{margin-bottom: -1.5rem !important;}.ml-lg-n4, .mx-lg-n4{margin-left: -1.5rem !important;}.m-lg-n5{margin: -3rem !important;}.mt-lg-n5, .my-lg-n5{margin-top: -3rem !important;}.mr-lg-n5, .mx-lg-n5{margin-right: -3rem !important;}.mb-lg-n5, .my-lg-n5{margin-bottom: -3rem !important;}.ml-lg-n5, .mx-lg-n5{margin-left: -3rem !important;}.m-lg-auto{margin: auto !important;}.mt-lg-auto, .my-lg-auto{margin-top: auto !important;}.mr-lg-auto, .mx-lg-auto{margin-right: auto !important;}.mb-lg-auto, .my-lg-auto{margin-bottom: auto !important;}.ml-lg-auto, .mx-lg-auto{margin-left: auto !important;}}@media (min-width: 1200px){.m-xl-0{margin: 0 !important;}.mt-xl-0, .my-xl-0{margin-top: 0 !important;}.mr-xl-0, .mx-xl-0{margin-right: 0 !important;}.mb-xl-0, .my-xl-0{margin-bottom: 0 !important;}.ml-xl-0, .mx-xl-0{margin-left: 0 !important;}.m-xl-1{margin: 0.25rem !important;}.mt-xl-1, .my-xl-1{margin-top: 0.25rem !important;}.mr-xl-1, .mx-xl-1{margin-right: 0.25rem !important;}.mb-xl-1, .my-xl-1{margin-bottom: 0.25rem !important;}.ml-xl-1, .mx-xl-1{margin-left: 0.25rem !important;}.m-xl-2{margin: 0.5rem !important;}.mt-xl-2, .my-xl-2{margin-top: 0.5rem !important;}.mr-xl-2, .mx-xl-2{margin-right: 0.5rem !important;}.mb-xl-2, .my-xl-2{margin-bottom: 0.5rem !important;}.ml-xl-2, .mx-xl-2{margin-left: 0.5rem !important;}.m-xl-3{margin: 1rem !important;}.mt-xl-3, .my-xl-3{margin-top: 1rem !important;}.mr-xl-3, .mx-xl-3{margin-right: 1rem !important;}.mb-xl-3, .my-xl-3{margin-bottom: 1rem !important;}.ml-xl-3, .mx-xl-3{margin-left: 1rem !important;}.m-xl-4{margin: 1.5rem !important;}.mt-xl-4, .my-xl-4{margin-top: 1.5rem !important;}.mr-xl-4, .mx-xl-4{margin-right: 1.5rem !important;}.mb-xl-4, .my-xl-4{margin-bottom: 1.5rem !important;}.ml-xl-4, .mx-xl-4{margin-left: 1.5rem !important;}.m-xl-5{margin: 3rem !important;}.mt-xl-5, .my-xl-5{margin-top: 3rem !important;}.mr-xl-5, .mx-xl-5{margin-right: 3rem !important;}.mb-xl-5, .my-xl-5{margin-bottom: 3rem !important;}.ml-xl-5, .mx-xl-5{margin-left: 3rem !important;}.p-xl-0{padding: 0 !important;}.pt-xl-0, .py-xl-0{padding-top: 0 !important;}.pr-xl-0, .px-xl-0{padding-right: 0 !important;}.pb-xl-0, .py-xl-0{padding-bottom: 0 !important;}.pl-xl-0, .px-xl-0{padding-left: 0 !important;}.p-xl-1{padding: 0.25rem !important;}.pt-xl-1, .py-xl-1{padding-top: 0.25rem !important;}.pr-xl-1, .px-xl-1{padding-right: 0.25rem !important;}.pb-xl-1, .py-xl-1{padding-bottom: 0.25rem !important;}.pl-xl-1, .px-xl-1{padding-left: 0.25rem !important;}.p-xl-2{padding: 0.5rem !important;}.pt-xl-2, .py-xl-2{padding-top: 0.5rem !important;}.pr-xl-2, .px-xl-2{padding-right: 0.5rem !important;}.pb-xl-2, .py-xl-2{padding-bottom: 0.5rem !important;}.pl-xl-2, .px-xl-2{padding-left: 0.5rem !important;}.p-xl-3{padding: 1rem !important;}.pt-xl-3, .py-xl-3{padding-top: 1rem !important;}.pr-xl-3, .px-xl-3{padding-right: 1rem !important;}.pb-xl-3, .py-xl-3{padding-bottom: 1rem !important;}.pl-xl-3, .px-xl-3{padding-left: 1rem !important;}.p-xl-4{padding: 1.5rem !important;}.pt-xl-4, .py-xl-4{padding-top: 1.5rem !important;}.pr-xl-4, .px-xl-4{padding-right: 1.5rem !important;}.pb-xl-4, .py-xl-4{padding-bottom: 1.5rem !important;}.pl-xl-4, .px-xl-4{padding-left: 1.5rem !important;}.p-xl-5{padding: 3rem !important;}.pt-xl-5, .py-xl-5{padding-top: 3rem !important;}.pr-xl-5, .px-xl-5{padding-right: 3rem !important;}.pb-xl-5, .py-xl-5{padding-bottom: 3rem !important;}.pl-xl-5, .px-xl-5{padding-left: 3rem !important;}.m-xl-n1{margin: -0.25rem !important;}.mt-xl-n1, .my-xl-n1{margin-top: -0.25rem !important;}.mr-xl-n1, .mx-xl-n1{margin-right: -0.25rem !important;}.mb-xl-n1, .my-xl-n1{margin-bottom: -0.25rem !important;}.ml-xl-n1, .mx-xl-n1{margin-left: -0.25rem !important;}.m-xl-n2{margin: -0.5rem !important;}.mt-xl-n2, .my-xl-n2{margin-top: -0.5rem !important;}.mr-xl-n2, .mx-xl-n2{margin-right: -0.5rem !important;}.mb-xl-n2, .my-xl-n2{margin-bottom: -0.5rem !important;}.ml-xl-n2, .mx-xl-n2{margin-left: -0.5rem !important;}.m-xl-n3{margin: -1rem !important;}.mt-xl-n3, .my-xl-n3{margin-top: -1rem !important;}.mr-xl-n3, .mx-xl-n3{margin-right: -1rem !important;}.mb-xl-n3, .my-xl-n3{margin-bottom: -1rem !important;}.ml-xl-n3, .mx-xl-n3{margin-left: -1rem !important;}.m-xl-n4{margin: -1.5rem !important;}.mt-xl-n4, .my-xl-n4{margin-top: -1.5rem !important;}.mr-xl-n4, .mx-xl-n4{margin-right: -1.5rem !important;}.mb-xl-n4, .my-xl-n4{margin-bottom: -1.5rem !important;}.ml-xl-n4, .mx-xl-n4{margin-left: -1.5rem !important;}.m-xl-n5{margin: -3rem !important;}.mt-xl-n5, .my-xl-n5{margin-top: -3rem !important;}.mr-xl-n5, .mx-xl-n5{margin-right: -3rem !important;}.mb-xl-n5, .my-xl-n5{margin-bottom: -3rem !important;}.ml-xl-n5, .mx-xl-n5{margin-left: -3rem !important;}.m-xl-auto{margin: auto !important;}.mt-xl-auto, .my-xl-auto{margin-top: auto !important;}.mr-xl-auto, .mx-xl-auto{margin-right: auto !important;}.mb-xl-auto, .my-xl-auto{margin-bottom: auto !important;}.ml-xl-auto, .mx-xl-auto{margin-left: auto !important;}}.text-monospace{font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;}.text-justify{text-align: justify !important;}.text-wrap{white-space: normal !important;}.text-nowrap{white-space: nowrap !important;}.text-truncate{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.text-left{text-align: left !important;}.text-right{text-align: right !important;}.text-center{text-align: center !important;}@media (min-width: 576px){.text-sm-left{text-align: left !important;}.text-sm-right{text-align: right !important;}.text-sm-center{text-align: center !important;}}@media (min-width: 768px){.text-md-left{text-align: left !important;}.text-md-right{text-align: right !important;}.text-md-center{text-align: center !important;}}@media (min-width: 992px){.text-lg-left{text-align: left !important;}.text-lg-right{text-align: right !important;}.text-lg-center{text-align: center !important;}}@media (min-width: 1200px){.text-xl-left{text-align: left !important;}.text-xl-right{text-align: right !important;}.text-xl-center{text-align: center !important;}}.text-lowercase{text-transform: lowercase !important;}.text-uppercase{text-transform: uppercase !important;}.text-capitalize{text-transform: capitalize !important;}.font-weight-light{font-weight: 300 !important;}.font-weight-lighter{font-weight: lighter !important;}.font-weight-normal{font-weight: 400 !important;}.font-weight-bold{font-weight: 700 !important;}.font-weight-bolder{font-weight: bolder !important;}.font-italic{font-style: italic !important;}.text-white{color: #FFFFFF !important;}.text-primary{color: #00A09D !important;}a.text-primary:hover, a.text-primary:focus{color: #005452 !important;}.text-secondary{color: #875A7B !important;}a.text-secondary:hover, a.text-secondary:focus{color: #593b51 !important;}.text-success{color: #28a745 !important;}a.text-success:hover, a.text-success:focus{color: #19692c !important;}.text-info{color: #17a2b8 !important;}a.text-info:hover, a.text-info:focus{color: #0f6674 !important;}.text-warning{color: #ffc107 !important;}a.text-warning:hover, a.text-warning:focus{color: #ba8b00 !important;}.text-danger{color: #dc3545 !important;}a.text-danger:hover, a.text-danger:focus{color: #a71d2a !important;}.text-light{color: #f6f9f9 !important;}a.text-light:hover, a.text-light:focus{color: #c8dada !important;}.text-dark{color: #141f1e !important;}a.text-dark:hover, a.text-dark:focus{color: black !important;}.text-alpha{color: #00A09D !important;}a.text-alpha:hover, a.text-alpha:focus{color: #005452 !important;}.text-beta{color: #875A7B !important;}a.text-beta:hover, a.text-beta:focus{color: #593b51 !important;}.text-gamma{color: #5C5B80 !important;}a.text-gamma:hover, a.text-gamma:focus{color: #3c3b53 !important;}.text-delta{color: #5B899E !important;}a.text-delta:hover, a.text-delta:focus{color: #3f5f6d !important;}.text-epsilon{color: #E46F78 !important;}a.text-epsilon:hover, a.text-epsilon:focus{color: #d82f3c !important;}.text-body{color: #212529 !important;}.text-muted{color: #6c757d !important;}.text-black-50{color: rgba(0, 0, 0, 0.5) !important;}.text-white-50{color: rgba(255, 255, 255, 0.5) !important;}.text-hide{font: 0/0 a; color: transparent; text-shadow: none; background-color: transparent; border: 0;}.text-decoration-none{text-decoration: none !important;}.text-break{word-break: break-word !important; overflow-wrap: break-word !important;}.text-reset{color: inherit !important;}.visible{visibility: visible !important;}.invisible{visibility: hidden !important;}@media print{*, *::before, *::after{text-shadow: none !important; box-shadow: none !important;}a:not(.btn){text-decoration: underline;}abbr[title]::after{content: " (" attr(title) ")";}pre{white-space: pre-wrap !important;}pre, blockquote{border: 1px solid #adb5bd; page-break-inside: avoid;}thead{display: table-header-group;}tr, img{page-break-inside: avoid;}p, h2, h3{orphans: 3; widows: 3;}h2, h3{page-break-after: avoid;}@page{size: a3;}body{min-width: 992px !important;}.container{min-width: 992px !important;}.navbar{display: none;}.badge{border: 1px solid #000000;}.table{border-collapse: collapse !important;}.table td, .table th{background-color: #FFFFFF !important;}.table-bordered th, .table-bordered td{border: 1px solid #dee2e6 !important;}.table-dark{color: inherit;}.table-dark th, .table-dark td, .table-dark thead th, .table-dark tbody + tbody{border-color: #dee2e6;}.table .thead-dark th{color: inherit; border-color: #dee2e6;}}

/* /web/static/src/scss/bootstrap_review.scss defined in bundle 'web.assets_frontend' */
 .alert{clear: both;}.bg-100{background-color: #f8f9fa !important; color: #212529;}.bg-100 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-100:hover, a.bg-100:focus, button.bg-100:hover, button.bg-100:focus{background-color: #dae0e5 !important; color: #212529;}.text-100{color: #f8f9fa !important;}a.text-100:hover, a.text-100:focus{color: #cbd3da !important;}.bg-200{background-color: #e9ecef !important; color: #212529;}.bg-200 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-200:hover, a.bg-200:focus, button.bg-200:hover, button.bg-200:focus{background-color: #cbd3da !important; color: #212529;}.text-200{color: #e9ecef !important;}a.text-200:hover, a.text-200:focus{color: #bdc6cf !important;}.bg-300{background-color: #dee2e6 !important; color: #212529;}.bg-300 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-300:hover, a.bg-300:focus, button.bg-300:hover, button.bg-300:focus{background-color: #c1c9d0 !important; color: #212529;}.text-300{color: #dee2e6 !important;}a.text-300:hover, a.text-300:focus{color: #b2bcc5 !important;}.bg-400{background-color: #ced4da !important; color: #212529;}.bg-400 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-400:hover, a.bg-400:focus, button.bg-400:hover, button.bg-400:focus{background-color: #b1bbc4 !important; color: #212529;}.text-400{color: #ced4da !important;}a.text-400:hover, a.text-400:focus{color: #a2aeb9 !important;}.bg-500{background-color: #adb5bd !important; color: #212529;}.bg-500 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-500:hover, a.bg-500:focus, button.bg-500:hover, button.bg-500:focus{background-color: #919ca6 !important; color: #212529;}.text-500{color: #adb5bd !important;}a.text-500:hover, a.text-500:focus{color: #838f9b !important;}.bg-600{background-color: #6c757d !important; color: #FFFFFF;}.bg-600 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-600:hover, a.bg-600:focus, button.bg-600:hover, button.bg-600:focus{background-color: #545b62 !important; color: #FFFFFF;}.text-600{color: #6c757d !important;}a.text-600:hover, a.text-600:focus{color: #494f54 !important;}.bg-700{background-color: #495057 !important; color: #FFFFFF;}.bg-700 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-700:hover, a.bg-700:focus, button.bg-700:hover, button.bg-700:focus{background-color: #32373b !important; color: #FFFFFF;}.text-700{color: #495057 !important;}a.text-700:hover, a.text-700:focus{color: #262a2d !important;}.bg-800{background-color: #343a40 !important; color: #FFFFFF;}.bg-800 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-800:hover, a.bg-800:focus, button.bg-800:hover, button.bg-800:focus{background-color: #1d2124 !important; color: #FFFFFF;}.text-800{color: #343a40 !important;}a.text-800:hover, a.text-800:focus{color: #121416 !important;}.bg-900{background-color: #212529 !important; color: #FFFFFF;}.bg-900 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-900:hover, a.bg-900:focus, button.bg-900:hover, button.bg-900:focus{background-color: #0a0c0d !important; color: #FFFFFF;}.text-900{color: #212529 !important;}a.text-900:hover, a.text-900:focus{color: black !important;}.bg-black-25{background-color: rgba(0, 0, 0, 0.25) !important; color: #212529;}.bg-black-25 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-black-25:hover, a.bg-black-25:focus, button.bg-black-25:hover, button.bg-black-25:focus{background-color: rgba(0, 0, 0, 0.25) !important; color: #212529;}.text-black-25{color: rgba(0, 0, 0, 0.25) !important;}a.text-black-25:hover, a.text-black-25:focus{color: rgba(0, 0, 0, 0.25) !important;}.bg-black-50{background-color: rgba(0, 0, 0, 0.5) !important; color: #FFFFFF;}.bg-black-50 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-black-50:hover, a.bg-black-50:focus, button.bg-black-50:hover, button.bg-black-50:focus{background-color: rgba(0, 0, 0, 0.5) !important; color: #FFFFFF;}.text-black-50{color: rgba(0, 0, 0, 0.5) !important;}a.text-black-50:hover, a.text-black-50:focus{color: rgba(0, 0, 0, 0.5) !important;}.bg-black-75{background-color: rgba(0, 0, 0, 0.75) !important; color: #FFFFFF;}.bg-black-75 .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-black-75:hover, a.bg-black-75:focus, button.bg-black-75:hover, button.bg-black-75:focus{background-color: rgba(0, 0, 0, 0.75) !important; color: #FFFFFF;}.text-black-75{color: rgba(0, 0, 0, 0.75) !important;}a.text-black-75:hover, a.text-black-75:focus{color: rgba(0, 0, 0, 0.75) !important;}.bg-white-25{background-color: rgba(255, 255, 255, 0.25) !important; color: #212529;}.bg-white-25 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-white-25:hover, a.bg-white-25:focus, button.bg-white-25:hover, button.bg-white-25:focus{background-color: rgba(230, 229, 229, 0.25) !important; color: #212529;}.text-white-25{color: rgba(255, 255, 255, 0.25) !important;}a.text-white-25:hover, a.text-white-25:focus{color: rgba(217, 217, 217, 0.25) !important;}.bg-white-50{background-color: rgba(255, 255, 255, 0.5) !important; color: #212529;}.bg-white-50 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-white-50:hover, a.bg-white-50:focus, button.bg-white-50:hover, button.bg-white-50:focus{background-color: rgba(230, 229, 229, 0.5) !important; color: #212529;}.text-white-50{color: rgba(255, 255, 255, 0.5) !important;}a.text-white-50:hover, a.text-white-50:focus{color: rgba(217, 217, 217, 0.5) !important;}.bg-white-75{background-color: rgba(255, 255, 255, 0.75) !important; color: #212529;}.bg-white-75 .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-white-75:hover, a.bg-white-75:focus, button.bg-white-75:hover, button.bg-white-75:focus{background-color: rgba(230, 229, 229, 0.75) !important; color: #212529;}.text-white-75{color: rgba(255, 255, 255, 0.75) !important;}a.text-white-75:hover, a.text-white-75:focus{color: rgba(217, 217, 217, 0.75) !important;}.bg-white{background-color: #FFFFFF !important; color: #212529;}.bg-white .text-muted{color: rgba(33, 37, 41, 0.4) !important;}a.bg-white:hover, a.bg-white:focus, button.bg-white:hover, button.bg-white:focus{background-color: #e6e5e5 !important; color: #212529;}.text-white{color: #FFFFFF !important;}a.text-white:hover, a.text-white:focus{color: #d9d9d9 !important;}.bg-black{background-color: #000000 !important; color: #FFFFFF;}.bg-black .text-muted{color: rgba(255, 255, 255, 0.4) !important;}a.bg-black:hover, a.bg-black:focus, button.bg-black:hover, button.bg-black:focus{background-color: black !important; color: #FFFFFF;}.text-black{color: #000000 !important;}a.text-black:hover, a.text-black:focus{color: black !important;}.card-body{background-color: rgba(255, 255, 255, 0.9) !important; color: #212529;}.card-body .text-muted{color: rgba(33, 37, 41, 0.4) !important;}.card-body:first-child{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-body:last-child{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card-body.row{background-color: transparent !important;}.accordion .collapsing > .card-body:first-child, .accordion .collapse.show > .card-body:first-child{margin-top: 1px;}.toast-header{background-clip: border-box;}.toast-body{background-color: rgba(255, 255, 255, 0.93) !important; color: #212529;}.toast-body .text-muted{color: rgba(33, 37, 41, 0.4) !important;}@media (min-width: 576px){.modal-dialog{height: 100%; padding: 1.75rem 0; margin: 0 auto;}.modal-content{max-height: 100%;}.modal-header, .modal-footer{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.modal-body{overflow: auto; -webkit-transform: translate3d(0, 0, 0); min-height: 0;}}.btn:not(:disabled):not(.disabled){cursor: pointer;}

/* /web/static/src/scss/lazyloader.scss defined in bundle 'web.assets_frontend' */
 a[href].o_wait_lazy_js, .o_wait_lazy_js a[href], button.o_wait_lazy_js, .o_wait_lazy_js button, input[type="submit"].o_wait_lazy_js, .o_wait_lazy_js input[type="submit"], input[type="button"].o_wait_lazy_js, .o_wait_lazy_js input[type="button"], .btn.o_wait_lazy_js, .o_wait_lazy_js .btn{pointer-events: none;}

/* /web/static/src/scss/navbar_mobile.scss defined in bundle 'web.assets_frontend' */
 @media (max-width: 767.98px){.o_main_navbar .o_app{float: none; margin: 0; border-bottom: 1px solid #5f5e97; color: transparent !important;}}@media (max-width: 767.98px){.o_main_navbar > .o_menu_brand{float: none; margin: 0; border-bottom: 1px solid #5f5e97; color: transparent !important;}}@media (max-width: 767.98px){.o_main_navbar{transition: height 200ms linear 0s; position: relative; height: 46px;}.o_main_navbar > ul > li{float: none;}.o_main_navbar > ul > li .dropdown-backdrop{display: none;}.o_main_navbar > ul > li .dropdown-menu.show{max-height: none;}.o_main_navbar > ul.o_menu_sections{width: 100%; display: none;}.o_main_navbar > ul.o_menu_sections .dropdown-menu.show{position: static; float: none; background-color: transparent; box-shadow: none; border: none; overflow: visible;}.o_main_navbar > ul.o_menu_sections .dropdown-menu.show > .dropdown-item{background-color: transparent; color: inherit;}.o_main_navbar > ul.o_menu_systray{position: absolute; top: 0px; left: 46px; bottom: auto; right: 46px; height: 46px; text-align: right;}.o_main_navbar > ul.o_menu_systray > li{display: inline-block;}.o_main_navbar > ul.o_menu_systray > li .dropdown-menu.show{position: absolute; top: 46px; left: 0; bottom: 0; right: 0; position: fixed; width: auto;}.o_main_navbar > ul.o_menu_systray .o_user_menu .oe_topbar_name{display: none;}}@media (max-width: 767.98px){body.o_mobile_menu_opened > .o_main_navbar{height: 100%; overflow: auto;}body.o_mobile_menu_opened > .o_main_navbar .o_menu_sections{display: block;}}@media (max-width: 767.98px){.o_switch_company_menu > .dropdown-menu{padding-top: 0px;}.o_switch_company_menu > .dropdown-menu .bg-info{padding: 10px;}}

/* /web/static/src/scss/notification.scss defined in bundle 'web.assets_frontend' */
 .o_notification_manager{position: absolute; top: 2.3125rem; left: auto; bottom: auto; right: 0; position: fixed; z-index: 1055; width: 350px; max-width: 100%;}.o_notification_manager .o_notification{width: 100%;}

/* /web_editor/static/src/scss/web_editor.common.scss defined in bundle 'web.assets_frontend' */
 html, body{position: relative; width: 100%; height: 100%;}.css_non_editable_mode_hidden{display: none !important;}.editor_enable .css_editable_mode_hidden{display: none !important;}.note-toolbar{margin-left: 0 !important;}.note-popover .popover > .arrow{display: none;}.note-popover .popover .dropdown-menu .dropdown-item > i, .note-editor .dropdown-menu .dropdown-item > i{visibility: hidden;}.note-popover .popover .dropdown-menu .dropdown-item.checked > i, .note-editor .dropdown-menu .dropdown-item.checked > i{visibility: visible;}#wrapwrap table.table.table-bordered, .o_editable table.table.table-bordered{table-layout: fixed;}#wrapwrap table.table.table-bordered td, .o_editable table.table.table-bordered td{min-width: 20px;}@media (max-width: 767.98px){#wrapwrap .table-responsive > table.table, .o_editable .table-responsive > table.table{table-layout: auto;}}ul.o_checklist{list-style: none;}ul.o_checklist > li{position: relative; margin-left: 20px;}ul.o_checklist > li::before{content: ''; position: absolute; left: -20px; display: block; height: 13px; width: 13px; margin-top: 4px; border: 1px solid; text-align: center; cursor: pointer;}ul.o_checklist > li.o_checked{text-decoration: line-through;}ul.o_checklist > li.o_checked::after{content: "âœ“"; position: absolute; left: -18px; top: +1px;}ol > li.o_indent, ul > li.o_indent{margin-left: 0; list-style: none;}ol > li.o_indent::before, ul > li.o_indent::before{content: none;}img.shadow{box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);}img.padding-small, .img.padding-small, span.fa.padding-small, iframe.padding-small{padding: 4px;}img.padding-medium, .img.padding-medium, span.fa.padding-medium, iframe.padding-medium{padding: 8px;}img.padding-large, .img.padding-large, span.fa.padding-large, iframe.padding-large{padding: 16px;}img.padding-xl, .img.padding-xl, span.fa.padding-xl, iframe.padding-xl{padding: 32px;}img.ml-auto, img.mx-auto{display: block;}.fa-6x{font-size: 6em;}.fa-7x{font-size: 7em;}.fa-8x{font-size: 8em;}.fa-9x{font-size: 9em;}.fa-10x{font-size: 10em;}.fa.d-block.mx-auto{text-align: center;}div.media_iframe_video{margin: 0 auto; text-align: center; position: relative; overflow: hidden; min-width: 100px;}div.media_iframe_video iframe{width: 100%; height: 100%; position: absolute; top: 0; left: auto; bottom: auto; right: auto; margin: 0 auto; margin-left: -50%;}div.media_iframe_video.padding-small iframe{padding: 4px;}div.media_iframe_video.padding-medium iframe{padding: 8px;}div.media_iframe_video.padding-large iframe{padding: 16px;}div.media_iframe_video.padding-xl iframe{padding: 32px;}div.media_iframe_video .media_iframe_video_size{padding-bottom: 66.5%; position: relative; width: 100%; height: 0;}div.media_iframe_video .css_editable_mode_display{position: absolute; top: 0; left: 0; bottom: 0; right: 0; width: 100%; height: 100%; display: none; z-index: 2;}html[data-browser^="msie"] div.media_iframe_video iframe{margin-left: 0;}address .fa.fa-mobile-phone{margin: 0 3px 0 2px;}address .fa.fa-file-text-o{margin-right: 1px;}span[data-oe-type="monetary"]{white-space: nowrap;}ul.oe_menu_editor .oe_menu_placeholder{outline: 1px dashed #4183C4;}ul.oe_menu_editor ul{list-style: none;}ul.oe_menu_editor li div{cursor: move;}.mt0{margin-top: 0px !important;}.mb0{margin-bottom: 0px !important;}.pt0{padding-top: 0px !important;}.pb0{padding-bottom: 0px !important;}.mt8{margin-top: 8px !important;}.mb8{margin-bottom: 8px !important;}.pt8{padding-top: 8px !important;}.pb8{padding-bottom: 8px !important;}.mt16{margin-top: 16px !important;}.mb16{margin-bottom: 16px !important;}.pt16{padding-top: 16px !important;}.pb16{padding-bottom: 16px !important;}.mt24{margin-top: 24px !important;}.mb24{margin-bottom: 24px !important;}.pt24{padding-top: 24px !important;}.pb24{padding-bottom: 24px !important;}.mt32{margin-top: 32px !important;}.mb32{margin-bottom: 32px !important;}.pt32{padding-top: 32px !important;}.pb32{padding-bottom: 32px !important;}.mt40{margin-top: 40px !important;}.mb40{margin-bottom: 40px !important;}.pt40{padding-top: 40px !important;}.pb40{padding-bottom: 40px !important;}.mt48{margin-top: 48px !important;}.mb48{margin-bottom: 48px !important;}.pt48{padding-top: 48px !important;}.pb48{padding-bottom: 48px !important;}.mt56{margin-top: 56px !important;}.mb56{margin-bottom: 56px !important;}.pt56{padding-top: 56px !important;}.pb56{padding-bottom: 56px !important;}.mt64{margin-top: 64px !important;}.mb64{margin-bottom: 64px !important;}.pt64{padding-top: 64px !important;}.pb64{padding-bottom: 64px !important;}.mt72{margin-top: 72px !important;}.mb72{margin-bottom: 72px !important;}.pt72{padding-top: 72px !important;}.pb72{padding-bottom: 72px !important;}.mt80{margin-top: 80px !important;}.mb80{margin-bottom: 80px !important;}.pt80{padding-top: 80px !important;}.pb80{padding-bottom: 80px !important;}.mt88{margin-top: 88px !important;}.mb88{margin-bottom: 88px !important;}.pt88{padding-top: 88px !important;}.pb88{padding-bottom: 88px !important;}.mt96{margin-top: 96px !important;}.mb96{margin-bottom: 96px !important;}.pt96{padding-top: 96px !important;}.pb96{padding-bottom: 96px !important;}.mt104{margin-top: 104px !important;}.mb104{margin-bottom: 104px !important;}.pt104{padding-top: 104px !important;}.pb104{padding-bottom: 104px !important;}.mt112{margin-top: 112px !important;}.mb112{margin-bottom: 112px !important;}.pt112{padding-top: 112px !important;}.pb112{padding-bottom: 112px !important;}.mt120{margin-top: 120px !important;}.mb120{margin-bottom: 120px !important;}.pt120{padding-top: 120px !important;}.pb120{padding-bottom: 120px !important;}.mt128{margin-top: 128px !important;}.mb128{margin-bottom: 128px !important;}.pt128{padding-top: 128px !important;}.pb128{padding-bottom: 128px !important;}.mt136{margin-top: 136px !important;}.mb136{margin-bottom: 136px !important;}.pt136{padding-top: 136px !important;}.pb136{padding-bottom: 136px !important;}.mt144{margin-top: 144px !important;}.mb144{margin-bottom: 144px !important;}.pt144{padding-top: 144px !important;}.pb144{padding-bottom: 144px !important;}.mt152{margin-top: 152px !important;}.mb152{margin-bottom: 152px !important;}.pt152{padding-top: 152px !important;}.pb152{padding-bottom: 152px !important;}.mt160{margin-top: 160px !important;}.mb160{margin-bottom: 160px !important;}.pt160{padding-top: 160px !important;}.pb160{padding-bottom: 160px !important;}.mt168{margin-top: 168px !important;}.mb168{margin-bottom: 168px !important;}.pt168{padding-top: 168px !important;}.pb168{padding-bottom: 168px !important;}.mt176{margin-top: 176px !important;}.mb176{margin-bottom: 176px !important;}.pt176{padding-top: 176px !important;}.pb176{padding-bottom: 176px !important;}.mt184{margin-top: 184px !important;}.mb184{margin-bottom: 184px !important;}.pt184{padding-top: 184px !important;}.pb184{padding-bottom: 184px !important;}.mt192{margin-top: 192px !important;}.mb192{margin-bottom: 192px !important;}.pt192{padding-top: 192px !important;}.pb192{padding-bottom: 192px !important;}.mt200{margin-top: 200px !important;}.mb200{margin-bottom: 200px !important;}.pt200{padding-top: 200px !important;}.pb200{padding-bottom: 200px !important;}.mt208{margin-top: 208px !important;}.mb208{margin-bottom: 208px !important;}.pt208{padding-top: 208px !important;}.pb208{padding-bottom: 208px !important;}.mt216{margin-top: 216px !important;}.mb216{margin-bottom: 216px !important;}.pt216{padding-top: 216px !important;}.pb216{padding-bottom: 216px !important;}.mt224{margin-top: 224px !important;}.mb224{margin-bottom: 224px !important;}.pt224{padding-top: 224px !important;}.pb224{padding-bottom: 224px !important;}.mt232{margin-top: 232px !important;}.mb232{margin-bottom: 232px !important;}.pt232{padding-top: 232px !important;}.pb232{padding-bottom: 232px !important;}.mt240{margin-top: 240px !important;}.mb240{margin-bottom: 240px !important;}.pt240{padding-top: 240px !important;}.pb240{padding-bottom: 240px !important;}.mt248{margin-top: 248px !important;}.mb248{margin-bottom: 248px !important;}.pt248{padding-top: 248px !important;}.pb248{padding-bottom: 248px !important;}.mt256{margin-top: 256px !important;}.mb256{margin-bottom: 256px !important;}.pt256{padding-top: 256px !important;}.pb256{padding-bottom: 256px !important;}.mt4{margin-top: 4px !important;}.mb4{margin-bottom: 4px !important;}.pt4{padding-top: 4px !important;}.pb4{padding-bottom: 4px !important;}.mt92{margin-top: 92px !important;}.mb92{margin-bottom: 92px !important;}.ml0{margin-left: 0px !important;}.mr0{margin-right: 0px !important;}.ml4{margin-left: 4px !important;}.mr4{margin-right: 4px !important;}.ml8{margin-left: 8px !important;}.mr8{margin-right: 8px !important;}.ml16{margin-left: 16px !important;}.mr16{margin-right: 16px !important;}.ml32{margin-left: 32px !important;}.mr32{margin-right: 32px !important;}.ml64{margin-left: 64px !important;}.mr64{margin-right: 64px !important;}a.o_underline{text-decoration: underline;}a.o_underline:hover{text-decoration: underline;}.o_ace_view_editor{background: #2F3129; color: white; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; opacity: 0.97;}.o_ace_view_editor .o_ace_view_editor_title{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 7.5px;}.o_ace_view_editor .o_ace_view_editor_title > .o_ace_type_switcher > button::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid; -moz-transform: scale(0.9999); margin-left: 4px;}.o_ace_view_editor .o_ace_view_editor_title > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; margin: 0 7.5px;}.o_ace_view_editor .o_ace_view_editor_title > *.o_include_option{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; font-size: 11px;}.o_ace_view_editor .o_ace_view_editor_title > *.o_include_option > .custom-control{margin-right: 7.5px;}.o_ace_view_editor .o_ace_view_editor_title > *.o_res_list{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; min-width: 60px;}.o_ace_view_editor #ace-view-id{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; padding: 7.5px 15px; background-color: #4a4d40;}.o_ace_view_editor #ace-view-id .o_ace_editor_resource_info{color: #ebecee;}.o_ace_view_editor #ace-view-editor{height: 70%; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.o_ace_view_editor #ace-view-editor .ace_gutter{display: block !important; cursor: ew-resize;}.o_ace_view_editor #ace-view-editor .ace_gutter .ace_gutter-cell.o_error{position: relative;}.o_ace_view_editor #ace-view-editor .ace_gutter .ace_gutter-cell.o_error::after{position: absolute; top: -100%; left: 0; bottom: -100%; right: 0; content: ""; z-index: 1000; display: block; background-color: #dc3545; opacity: 0.5; pointer-events: none;}.o_ace_view_editor #ace-view-editor .ace_resize_bar{position: absolute; top: auto; left: auto; bottom: auto; right: 0; width: 25px; height: 100%; cursor: ew-resize;}.o_ace_view_editor #ace-view-editor .ace_scroller.o_error::after{position: absolute; top: 0; left: 0; bottom: 0; right: auto; width: 3px; content: ""; z-index: 1000; display: block; background-color: #dc3545; opacity: 0.5; pointer-events: none;}.o_ace_select2_dropdown{width: auto !important; padding-top: 4px; font-family: monospace !important;}.o_ace_select2_dropdown > .select2-results{max-height: none; max-height: 70vh;}.o_ace_select2_dropdown > .select2-results .select2-result-label{padding-top: 1px; padding-bottom: 2px;}.o_ace_select2_dropdown > .select2-results .select2-result-label > .o_ace_select2_result{padding: 0; font-size: 12px; white-space: nowrap;}.o_nocontent_help{pointer-events: auto; max-width: 650px; margin: auto; padding: 15px; z-index: 1000; text-align: center; color: #777777; font-size: 115%;}.o_nocontent_help > p:first-of-type{margin-top: 0; color: #4c4c4c; font-weight: bold; font-size: 125%;}.o_nocontent_help a{cursor: pointer;}@media (max-width: 767.98px){odoo-wysiwyg-container .panel-heading.note-toolbar{overflow-x: auto;}odoo-wysiwyg-container .btn-group{position: static;}.o_technical_modal{z-index: 2001;}.o_technical_modal > .o_select_media_dialog{max-width: inherit !important; z-index: 2001;}.o_technical_modal > .o_select_media_dialog .modal-dialog, .o_technical_modal > .o_select_media_dialog .model-content{height: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .nav .nav-item.search{width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .nav .nav-item.search .btn-group{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: space-around; padding: 5px;}.o_technical_modal > .o_select_media_dialog .modal-body .font-icons-icons{text-align: center;}.o_technical_modal > .o_select_media_dialog .modal-body .form-control.o_we_search{height: inherit;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline .btn-group{width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline .btn-group .btn.btn-primary:not(.dropdown-toggle){width: 90%;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline > .input-group.ml-2{margin-left: 0 !important;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline > .input-group.ml-2 > .input-group-append{width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline > .input-group.ml-2 > .input-group-append > .btn{width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .form-inline > .input-group.ml-2 > .input-group-append > .ml-2{margin-left: 0 !important;}.o_technical_modal > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}.o_technical_modal > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell{flex: initial; max-width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell > .o_existing_attachment_remove{opacity: inherit; top: 10px;}.o_technical_modal > .o_select_media_dialog .modal-body #editor-media-image .unsplash_img_container .unsplash_error .mx-auto{width: 100%;}.o_technical_modal > .o_select_media_dialog .modal-body #editor-media-image .unsplash_img_container .unsplash_error .mx-auto .form-group input.w-100{min-width: 100px;}}

/* /web_editor/static/src/scss/web_editor.frontend.scss defined in bundle 'web.assets_frontend' */
 @media (max-width: 767.98px){img, .media_iframe_video, span.fa, i.fa{transform: none !important;}}.o_wysiwyg_loader{pointer-events: none; min-height: 100px; color: transparent;}.o_wysiwyg_loading{position: absolute; top: 50%; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -50%);}

/* /portal/static/src/scss/bootstrap.extend.scss defined in bundle 'web.assets_frontend' */
 @media (min-width: 576px){.w-sm-25{width: 25% !important;}.w-sm-50{width: 50% !important;}.w-sm-75{width: 75% !important;}.w-sm-100{width: 100% !important;}.w-sm-auto{width: auto !important;}.w-sm-0{width: 0 !important;}.h-sm-25{height: 25% !important;}.h-sm-50{height: 50% !important;}.h-sm-75{height: 75% !important;}.h-sm-100{height: 100% !important;}.h-sm-auto{height: auto !important;}.h-sm-0{height: 0 !important;}}@media (min-width: 768px){.w-md-25{width: 25% !important;}.w-md-50{width: 50% !important;}.w-md-75{width: 75% !important;}.w-md-100{width: 100% !important;}.w-md-auto{width: auto !important;}.w-md-0{width: 0 !important;}.h-md-25{height: 25% !important;}.h-md-50{height: 50% !important;}.h-md-75{height: 75% !important;}.h-md-100{height: 100% !important;}.h-md-auto{height: auto !important;}.h-md-0{height: 0 !important;}}@media (min-width: 992px){.w-lg-25{width: 25% !important;}.w-lg-50{width: 50% !important;}.w-lg-75{width: 75% !important;}.w-lg-100{width: 100% !important;}.w-lg-auto{width: auto !important;}.w-lg-0{width: 0 !important;}.h-lg-25{height: 25% !important;}.h-lg-50{height: 50% !important;}.h-lg-75{height: 75% !important;}.h-lg-100{height: 100% !important;}.h-lg-auto{height: auto !important;}.h-lg-0{height: 0 !important;}}@media (min-width: 1200px){.w-xl-25{width: 25% !important;}.w-xl-50{width: 50% !important;}.w-xl-75{width: 75% !important;}.w-xl-100{width: 100% !important;}.w-xl-auto{width: auto !important;}.w-xl-0{width: 0 !important;}.h-xl-25{height: 25% !important;}.h-xl-50{height: 50% !important;}.h-xl-75{height: 75% !important;}.h-xl-100{height: 100% !important;}.h-xl-auto{height: auto !important;}.h-xl-0{height: 0 !important;}}

/* /portal/static/src/scss/portal.scss defined in bundle 'web.assets_frontend' */
 #wrapwrap{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; width: 100%; min-height: 100%;}#wrapwrap > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}#wrapwrap > main{-webkit-box-flex: 1; -webkit-flex: 1 0 auto; flex: 1 0 auto;}header .navbar-brand{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; max-width: 75%;}header .navbar-brand.logo{padding-top: 0; padding-bottom: 0;}header .navbar-brand.logo img{object-fit: contain; display: block; width: auto; height: 2.3125rem;}@media (max-width: 767.98px){header .navbar-brand.logo img{height: auto; max-height: 2.3125rem;}}header .nav-link{white-space: nowrap;}.navbar{margin-bottom: 0;}.navbar ul.nav > li.divider{display: none; border-right: 1px solid #e9ecef;}.navbar ul.nav > li.active + .divider{visibility: hidden;}@media (max-width: 767.98px){.navbar .nav.navbar-nav.float-right{float: none !important;}}@media (min-width: 768px){.navbar-expand-md ul.nav > li.divider{display: list-item;}}ul.flex-column > li > a{padding: 2px 15px;}a.fa:hover, .btn-link.fa:hover{text-decoration: none;}.jumbotron{margin-bottom: 0;}ul{list-style-type: disc;}ul ul{list-style-type: circle;}ul ul ul{list-style-type: square;}ul ul ul ul{list-style-type: disc;}ul ul ul ul ul{list-style-type: circle;}ul ul ul ul ul ul{list-style-type: square;}ul ul ul ul ul ul ul{list-style-type: disc;}ol{list-style-type: decimal;}ol ol{list-style-type: lower-alpha;}ol ol ol{list-style-type: lower-greek;}ol ol ol ol{list-style-type: decimal;}ol ol ol ol ol{list-style-type: lower-alpha;}ol ol ol ol ol ol{list-style-type: lower-greek;}ol ol ol ol ol ol ol{list-style-type: decimal;}li > p{margin: 0;}.container .container, .container .container-fluid, .container-fluid .container-fluid{padding-right: 0; padding-left: 0;}#wrap > .container::before, #wrap > .container::after, #wrap > .container-fluid::before, #wrap > .container-fluid::after{content: ""; display: table; clear: both;}[class^="col-lg-"]{min-height: 24px;}.input-group{-webkit-flex-flow: row nowrap; flex-flow: row nowrap;}.list-group-item:not([class*="list-group-item-"]):not(.active){color: #212529;}.o_portal .breadcrumb, .o_portal_wrap .o_portal_navbar .breadcrumb{background-color: inherit;}.o_page_header, .s_title .s_title_underlined{margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #dee2e6; line-height: 2.1rem;}img.float-right, .media_iframe_video.float-right, .o_image.float-right{margin-left: 15px;}img.float-left, .media_iframe_video.float-left, .o_image.float-left{margin-right: 15px;}img.o_we_custom_image{display: inline-block;}::-moz-selection{background: rgba(150, 150, 220, 0.3);}::selection{background: rgba(150, 150, 220, 0.3);}.oe_search_box{padding-right: 23px;}.para_large{font-size: 120%;}.jumbotron .para_large p{font-size: 150%;}.readable{font-size: 120%; max-width: 700px; margin-left: auto; margin-right: auto;}.readable .container{padding-left: 0; padding-right: 0; width: auto;}.oe_dark{background-color: rgba(200, 200, 200, 0.14);}.oe_black{background-color: rgba(0, 0, 0, 0.9); color: white;}.oe_green{background-color: #169C78; color: white;}.oe_green .text-muted{color: #ddd !important;}.oe_blue_light{background-color: #41b6ab; color: white;}.oe_blue_light .text-muted{color: #ddd !important;}.oe_blue{background-color: #34495e; color: white;}.oe_orange{background-color: #f05442; color: white;}.oe_orange .text-muted{color: #ddd !important;}.oe_purple{background-color: #b163a3; color: white;}.oe_purple .text-muted{color: #ddd !important;}.oe_red{background-color: #9C1b31; color: white;}.oe_red .text-muted{color: #ddd !important;}.oe_none{background-color: #FFFFFF;}.oe_yellow{background-color: #A2A51B;}.oe_green{background-color: #149F2C;}#wrapwrap.o_portal{background-color: #ececec !important; color: #212529;}#wrapwrap.o_portal .text-muted{color: rgba(33, 37, 41, 0.4) !important;}.o_portal > tbody.o_portal_report_tbody{vertical-align: middle;}.o_portal_wrap .o_portal_my_home > .o_page_header > a:hover{text-decoration: none;}.o_portal_wrap .o_portal_navbar{background-color: white !important;}.o_portal_wrap .o_portal_navbar .breadcrumb{padding-left: 0; padding-right: 0;}.o_portal_wrap .o_portal_my_doc_table th{padding-top: 0.5rem; padding-bottom: 0.5rem;}.o_portal_wrap .o_portal_my_doc_table td{padding-top: 0.25rem; padding-bottom: 0.25rem;}.o_portal_wrap .o_portal_my_doc_table tr:last-child td{padding-bottom: 0.375rem;}.o_portal_wrap .o_portal_my_doc_table td, .o_portal_wrap .o_portal_my_doc_table th{vertical-align: middle; white-space: nowrap;}.o_portal_wrap address span[itemprop="name"]{margin-bottom: 0.3em;}.o_portal_wrap address div[itemprop="address"] > div{position: relative;}.o_portal_wrap address div[itemprop="address"] > div span[itemprop="streetAddress"]{line-height: 1.2; margin-bottom: 0.3em;}.o_portal_wrap address div[itemprop="address"] > div .fa{position: absolute; top: 0; left: 0; bottom: auto; right: auto; line-height: 1.5;}.o_portal_wrap address div[itemprop="address"] > div .fa + span{display: block; padding-left: 1.78571429em;}.o_portal_wrap .o_my_sidebar div[itemprop="address"] > div{margin-top: 0.5em;}@media (max-width: 991.98px){.o_portal_wrap #o_portal_navbar_content{margin: 0.5rem -1rem 0; padding: 0.5rem 1rem; border-top: 1px solid #dee2e6; background-color: #e9ecef;}}.o_portal_wrap table.table tr{word-wrap: break-word;}.oe_attachments .o_image_small{height: 40px; width: 50px;}form label{font-weight: 700;}form label.label-optional{font-weight: 400;}.o_portal_contact_img{width: 2.3em; height: 2.3em; object-fit: cover;}.o_portal_sidebar #sidebar_content.card{border-left: 0; border-bottom: 0;}.o_portal_sidebar #sidebar_content.card > div.card-body{border-left: 1px solid #dee2e6;}.o_portal_sidebar #sidebar_content.card > ul > li{border-left: 1px solid #dee2e6; margin-bottom: -1px;}.o_portal_sidebar #sidebar_content.card > div.card-footer{border-left: 1px solid #dee2e6; border-bottom: 1px solid #dee2e6;}.o_portal_sidebar .o_portal_html_view{overflow: hidden; background: white; position: relative;}.o_portal_sidebar .o_portal_html_view .o_portal_html_loader{position: absolute; top: 45%; left: 0; bottom: auto; right: 0;}.o_portal_sidebar .o_portal_html_view iframe{position: relative;}.o_portal_chatter{padding: 10px;}.o_portal_chatter .o_portal_chatter_avatar{width: 45px; height: 45px; margin-right: 1rem;}.o_portal_chatter .o_portal_chatter_header{margin-bottom: 15px;}.o_portal_chatter .o_portal_chatter_composer{margin-bottom: 15px;}.o_portal_chatter .o_portal_chatter_messages{margin-bottom: 15px;}.o_portal_chatter .o_portal_chatter_messages .o_portal_chatter_message_title p{font-size: 85%; color: #a8a8a8; margin: 0px;}.o_portal_chatter .o_portal_chatter_pager{text-align: center;}.o_portal_chatter .o_portal_chatter_attachment .o_portal_chatter_attachment_name, .o_portal_chatter_composer .o_portal_chatter_attachment .o_portal_chatter_attachment_name{word-wrap: break-word;}.o_portal_chatter .o_portal_chatter_attachment .o_portal_chatter_attachment_delete, .o_portal_chatter_composer .o_portal_chatter_attachment .o_portal_chatter_attachment_delete{position: absolute; top: 0; left: auto; bottom: auto; right: 0; opacity: 0;}.o_portal_chatter .o_portal_chatter_attachment:hover .o_portal_chatter_attachment_delete, .o_portal_chatter_composer .o_portal_chatter_attachment:hover .o_portal_chatter_attachment_delete{opacity: 1;}

/* /website/static/src/scss/website.scss defined in bundle 'web.assets_frontend' */
 :root{--header-font-size: 0.875rem; --font-number: 1; --headings-font-number: 1; --navbar-font-number: 1; --buttons-font-number: 1; --body: white; --text: #212529; --h1: #212529; --h2: #212529; --h3: #212529; --h4: #212529; --h5: #212529; --h6: #212529; --logo-height: 2.3125rem; --number-of-fonts: 6;}#wrapwrap{background-size: cover; background-repeat: no-repeat; background-position: center; background-attachment: fixed;}.navbar .nav-item{transition: opacity 1000ms ease 0s;}.navbar .o_menu_loading .nav-item{height: 0 !important; overflow: hidden !important; opacity: 0 !important;}.navbar-light{background-color: #F9F9F9 !important;}header .navbar-brand{font-size: 1.54166667rem;}header .navbar-brand, header .navbar-brand.logo{padding-top: 0; padding-bottom: 0;}.o_footer{background-color: #F9F9F9 !important; color: #212529;}.o_footer .text-muted{color: rgba(33, 37, 41, 0.4) !important;}.o_footer > #footer{border-top: 1px solid transparent;}font[style*='background'], font[class*='bg-']{padding: 2px 6px 4px;}.fa{font-family: "FontAwesome" !important;}.fa.rounded-circle, .fa.rounded, .fa.rounded-0, .fa.rounded-leaf, .fa.img-thumbnail, .fa.shadow{display: inline-block; vertical-align: middle; text-align: center; width: 3rem; height: 3rem; line-height: 3rem; background-color: #f8f9fa;}.fa.rounded-circle.fa-2x, .fa.rounded.fa-2x, .fa.rounded-0.fa-2x, .fa.rounded-leaf.fa-2x, .fa.img-thumbnail.fa-2x, .fa.shadow.fa-2x{width: 5rem; height: 5rem; line-height: 5rem;}.fa.rounded-circle.fa-3x, .fa.rounded.fa-3x, .fa.rounded-0.fa-3x, .fa.rounded-leaf.fa-3x, .fa.img-thumbnail.fa-3x, .fa.shadow.fa-3x{width: 6rem; height: 6rem; line-height: 6rem;}.fa.rounded-circle.fa-4x, .fa.rounded.fa-4x, .fa.rounded-0.fa-4x, .fa.rounded-leaf.fa-4x, .fa.img-thumbnail.fa-4x, .fa.shadow.fa-4x{width: 7rem; height: 7rem; line-height: 7rem;}.fa.rounded-circle.fa-5x, .fa.rounded.fa-5x, .fa.rounded-0.fa-5x, .fa.rounded-leaf.fa-5x, .fa.img-thumbnail.fa-5x, .fa.shadow.fa-5x{width: 8rem; height: 8rem; line-height: 8rem;}.fa.img-thumbnail{padding: 0;}.fa.rounded-leaf{border-top-left-radius: 3rem; border-bottom-right-radius: 3rem;}.btn.flat{border: 0; letter-spacing: 0.05em; text-transform: uppercase; padding: 0.75rem 1.5rem; font-size: 0.65625rem; line-height: 1.5; border-radius: 0;}.btn.flat.btn-lg, .btn-group-lg > .btn.flat{padding: 1rem 2rem; font-size: 0.8203125rem; line-height: 1.5; border-radius: 0;}.btn.flat.btn-sm, .btn-group-sm > .btn.flat{padding: 0.5rem 1rem; font-size: 0.5625rem; line-height: 1.5; border-radius: 0;}.btn.flat.btn-xs{padding: 0.25rem 0.5rem; font-size: 0.4375rem; line-height: 1.5; border-radius: 0;}.btn.rounded-circle{border-radius: 100px !important; padding: 0.45rem 1.35rem; font-size: 0.875rem; line-height: 1.5; border-radius: 30px;}.btn.rounded-circle.btn-lg, .btn-group-lg > .btn.rounded-circle{padding: 0.6rem 1.8rem; font-size: 1.09375rem; line-height: 1.5; border-radius: 30px;}.btn.rounded-circle.btn-sm, .btn-group-sm > .btn.rounded-circle{padding: 0.3rem 0.9rem; font-size: 0.75rem; line-height: 1.5; border-radius: 30px;}.btn.rounded-circle.btn-xs{padding: 0.15rem 0.45rem; font-size: 0.65625rem; line-height: 1.5; border-radius: 30px;}.s_btn .btn + .btn{margin-left: .75rem;}.blockquote{font-size: 1rem;}.blockquote footer{background-color: inherit;}.oe_img_bg{background-size: cover; background-repeat: no-repeat;}.oe_img_bg.o_bg_img_opt_contain{background-size: contain; background-position: center center;}.oe_img_bg.o_bg_img_opt_custom{background-size: auto;}.oe_img_bg.o_bg_img_opt_repeat{background-repeat: repeat;}.oe_img_bg.o_bg_img_opt_repeat_x{background-repeat: repeat-x;}.oe_img_bg.o_bg_img_opt_repeat_y{background-repeat: repeat-y;}.o_background_video{position: relative;}.o_background_video > *{position: relative;}.o_bg_video_container{position: absolute; top: 0; left: 0; bottom: 0; right: 0; overflow: hidden;}.o_bg_video_iframe{position: relative; pointer-events: none !important;}.o_bg_video_loading{position: absolute; top: 0; left: 0; bottom: 0; right: 0;}.o_ul_toggle{display: none;}.o_ul_folded .o_close{display: none !important;}.o_ul_folded .o_ul_toggle_self, .o_ul_folded .o_ul_toggle_next{display: inline-block; line-height: inherit; float: left; position: relative; margin-left: -1em; top: -0.15em; left: 0.2em; font-size: 1.4em; text-decoration: none;}.o_ul_folded .o_ul_toggle_self.o_open:before, .o_ul_folded .o_ul_toggle_next.o_open:before{content: "ïƒ—";}.o_ul_folded .o_ul_toggle_self:before, .o_ul_folded .o_ul_toggle_next:before{content: "ïƒš";}@media (max-width: 400px){section, .parallax, .row, .hr, .blockquote{height: auto !important;}}.table_desc{margin: 0 0 20px 0; width: 100%; word-break: break-all; border: 1px solid #dddddd;}.table_heading{background-color: #f5f5f5; border: 1px solid #dddddd; color: #666666; font-size: 14px; padding: 4px;}table.table_desc tr td{text-align: left; padding: 5px; font-size: 13px;}table.table_desc tr td:first-child{width: 25%; font-weight: bold; border-bottom: 1px solid #c9c9c9; border-right: 1px solid #c9c9c9; border-left: none;}table.table_desc tr td:last-child{border-bottom: 1px solid #c9c9c9;}.jumbotron{border-radius: 0;}.s_title .s_title_boxed > *{display: inline-block; padding: 30px; border: 1px solid;}.s_title .s_title_lines{overflow: hidden;}.s_title .s_title_lines:before, .s_title .s_title_lines:after{content: ""; display: inline-block; vertical-align: middle; width: 100%; border-top: 1px solid; border-top-color: inherit;}.s_title .s_title_lines:before{margin: 0 15px 0 -100%;}.s_title .s_title_lines:after{margin: 0 -100% 0 15px;}.s_title .s_title_small_caps{font-variant: small-caps;}.s_title .s_title_transparent{opacity: .5;}.s_title .s_title_thin{font-weight: 300;}.s_features_grid_content{overflow: hidden;}.s_features_grid_content p{margin-bottom: 0;}.s_features_grid_icon{float: left; margin-right: 15px;}.s_alert{margin: 15px 0; border: 1px solid; border-radius: 0.25rem;}.s_alert p:last-child, .s_alert ul:last-child, .s_alert ol:last-child{margin-bottom: 0;}.s_alert_sm{padding: 10px; font-size: 0.75rem;}.s_alert_md{padding: 15px; font-size: 0.875rem;}.s_alert_lg{padding: 30px; font-size: 1.09375rem;}.s_alert_icon{float: left; margin-right: 10px;}.s_alert_content{overflow: hidden;}.s_three_columns .align-items-stretch .card{height: 100%;}.s_comparisons .card-body .card-title{margin: 0;}.s_comparisons .card-body .s_comparisons_currency, .s_comparisons .card-body .s_comparisons_price, .s_comparisons .card-body .s_comparisons_decimal{display: inline-block; vertical-align: middle;}.s_comparisons .card-body .s_comparisons_currency, .s_comparisons .card-body .s_comparisons_decimal{font-size: 80%;}.s_comparisons .card-body .s_comparisons_price{font-size: 200%;}.s_faq_collapse .accordion .card .card-header{cursor: pointer; display: inline-block; width: 100%; padding: .5em 0; border-radius: 0; outline: none;}.s_faq_collapse .accordion .card .card-header:before{content: '\f056'; font-family: 'FontAwesome'; display: inline-block; margin: 0 .5em 0 .75em; color: #6c757d;}.s_faq_collapse .accordion .card .card-header.collapsed:before{content: '\f055'; font-family: 'FontAwesome';}.s_faq_collapse .accordion .card .card-header:hover, .s_faq_collapse .accordion .card .card-header:focus{text-decoration: none;}.s_faq_collapse .accordion .card .card-body{padding: 1em 2.25em;}.s_faq_collapse .card-body p:last-child, .s_faq_collapse .card-body ul:last-child{margin-bottom: 0;}.s_references .img-thumbnail{border: none;}.s_carousel .carousel-control-prev, .s_carousel .carousel-control-next, .s_quotes_carousel .carousel-control-prev, .s_quotes_carousel .carousel-control-next{cursor: pointer; width: 8%; opacity: 1;}.s_carousel .carousel-control-prev, .s_quotes_carousel .carousel-control-prev{-webkit-box-pack: start; justify-content: flex-start;}.s_carousel .carousel-control-next, .s_quotes_carousel .carousel-control-next{-webkit-box-pack: end; justify-content: flex-end;}.s_carousel .carousel-control-prev-icon, .s_carousel .carousel-control-next-icon, .s_quotes_carousel .carousel-control-prev-icon, .s_quotes_carousel .carousel-control-next-icon{width: auto; height: auto; background-image: none; color: #212529;}.s_carousel .carousel-control-prev-icon:before, .s_carousel .carousel-control-next-icon:before, .s_quotes_carousel .carousel-control-prev-icon:before, .s_quotes_carousel .carousel-control-next-icon:before{font-family: "FontAwesome"; display: inline-block; background-color: #fff;}.s_carousel .carousel-inner, .s_quotes_carousel .carousel-inner{overflow: hidden; height: 100%;}.s_carousel .carousel-inner .carousel-item, .s_quotes_carousel .carousel-inner .carousel-item{height: 100%;}.s_carousel .carousel-indicators li:hover:not(.active), .s_quotes_carousel .carousel-indicators li:hover:not(.active){background-color: rgba(255, 255, 255, 0.8);}.s_carousel.s_carousel_default .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_default .carousel-control-prev-icon:before{content: "ï“" ; margin-left: 1.5rem;}.s_carousel.s_carousel_default .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_default .carousel-control-next-icon:before{content: "ï”" ; margin-right: 1.5rem;}.s_carousel.s_carousel_default .carousel-control-prev-icon:before, .s_carousel.s_carousel_default .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_default .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_default .carousel-control-next-icon:before{background-color: transparent; font-size: 2rem; color: #fff; text-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);}.s_carousel.s_carousel_default .carousel-indicators li, .s_quotes_carousel.s_carousel_default .carousel-indicators li{height: .5rem; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); border-radius: 2px;}.s_carousel.s_carousel_bordered, .s_quotes_carousel.s_carousel_bordered{border: 2rem solid transparent;}.s_carousel.s_carousel_bordered .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_bordered .carousel-control-prev-icon:before{content: "\f0d9";}.s_carousel.s_carousel_bordered .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_bordered .carousel-control-next-icon:before{content: "\f0da";}.s_carousel.s_carousel_bordered .carousel-control-prev-icon:before, .s_carousel.s_carousel_bordered .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_bordered .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_bordered .carousel-control-next-icon:before{width: 2rem; height: 6rem; line-height: 6rem; font-size: 1.5rem;}.s_carousel.s_carousel_bordered .carousel-indicators li, .s_quotes_carousel.s_carousel_bordered .carousel-indicators li{width: 3rem; height: 1rem;}.s_carousel.s_carousel_rounded .carousel-control-prev, .s_quotes_carousel.s_carousel_rounded .carousel-control-prev{margin-left: 1.5rem;}.s_carousel.s_carousel_rounded .carousel-control-next, .s_quotes_carousel.s_carousel_rounded .carousel-control-next{margin-right: 1.5rem;}.s_carousel.s_carousel_rounded .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_rounded .carousel-control-prev-icon:before{content: "\f060";}.s_carousel.s_carousel_rounded .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_rounded .carousel-control-next-icon:before{content: "\f061";}.s_carousel.s_carousel_rounded .carousel-control-prev-icon:before, .s_carousel.s_carousel_rounded .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_rounded .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_rounded .carousel-control-next-icon:before{width: 4rem; height: 4rem; line-height: 4rem; border-radius: 50%; font-size: 1.25rem;}.s_carousel.s_carousel_rounded .carousel-indicators li, .s_quotes_carousel.s_carousel_rounded .carousel-indicators li{width: 1rem; height: 1rem; border-radius: 50%;}.s_carousel.s_carousel_boxed, .s_quotes_carousel.s_carousel_boxed{width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto;}@media (min-width: 576px){.s_carousel.s_carousel_boxed, .s_quotes_carousel.s_carousel_boxed{max-width: 540px;}}@media (min-width: 768px){.s_carousel.s_carousel_boxed, .s_quotes_carousel.s_carousel_boxed{max-width: 720px;}}@media (min-width: 992px){.s_carousel.s_carousel_boxed, .s_quotes_carousel.s_carousel_boxed{max-width: 960px;}}@media (min-width: 1200px){.s_carousel.s_carousel_boxed, .s_quotes_carousel.s_carousel_boxed{max-width: 1140px;}}.s_carousel.s_carousel_boxed .carousel-item, .s_quotes_carousel.s_carousel_boxed .carousel-item{padding: 0 1rem;}.s_carousel.s_carousel_boxed .carousel-control-prev, .s_carousel.s_carousel_boxed .carousel-control-next, .s_quotes_carousel.s_carousel_boxed .carousel-control-prev, .s_quotes_carousel.s_carousel_boxed .carousel-control-next{align-items: flex-end; margin-bottom: 1.25rem;}.s_carousel.s_carousel_boxed .carousel-control-prev, .s_quotes_carousel.s_carousel_boxed .carousel-control-prev{margin-left: 3rem;}.s_carousel.s_carousel_boxed .carousel-control-next, .s_quotes_carousel.s_carousel_boxed .carousel-control-next{margin-right: 3rem;}.s_carousel.s_carousel_boxed .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_boxed .carousel-control-prev-icon:before{content: "\f104";}.s_carousel.s_carousel_boxed .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_boxed .carousel-control-next-icon:before{content: "\f105";}.s_carousel.s_carousel_boxed .carousel-control-prev-icon:before, .s_carousel.s_carousel_boxed .carousel-control-next-icon:before, .s_quotes_carousel.s_carousel_boxed .carousel-control-prev-icon:before, .s_quotes_carousel.s_carousel_boxed .carousel-control-next-icon:before{width: 2rem; height: 2rem; line-height: 2rem; font-size: 1.25rem;}.s_carousel.s_carousel_boxed .carousel-indicators li, .s_quotes_carousel.s_carousel_boxed .carousel-indicators li{width: 1rem; height: 1rem;}.s_carousel.s_carousel_boxed .carousel-indicators li:hover:not(.active), .s_quotes_carousel.s_carousel_boxed .carousel-indicators li:hover:not(.active){background-color: rgba(255, 255, 255, 0.8);}.carousel .container .carousel-img img{max-height: 95%; padding: 10px;}.carousel .container > .carousel-caption{position: absolute; top: auto; left: 50%; bottom: auto; right: 50%; bottom: 20px;}.carousel .container > .carousel-caption > div{position: absolute; text-align: left; padding: 20px; background: rgba(0, 0, 0, 0.4); bottom: 20px;}.carousel .container > .carousel-image{position: absolute; top: 5%; left: auto; bottom: 5%; right: auto; max-height: 90%; margin: 0 auto;}.carousel .container .carousel-item.text_image .container > .carousel-caption{left: 10%;}.carousel .container .carousel-item.text_image .container > .carousel-caption > div{right: 50%; margin-right: -20%; max-width: 550px;}.carousel .container .carousel-item.text_image .container > .carousel-image{right: 10%; left: 50%;}.carousel .container .carousel-item.image_text .container > .carousel-caption{right: 10%;}.carousel .container .carousel-item.image_text .container > .carousel-caption > div{left: 50%; margin-left: -20%; max-width: 550px;}.carousel .container .carousel-item.image_text .container > .carousel-image{right: 50%; left: 10%;}.carousel .container .carousel-item.text_only .container > .carousel-caption{left: 10%; right: 10%; top: 10%; bottom: auto;}.carousel .container .carousel-item.text_only .container > .carousel-caption > div{text-align: center; background: transparent; bottom: auto; width: 100%;}.carousel .container .carousel-item.text_only .container > .carousel-image{display: none !important;}.s_quotes_carousel blockquote{padding: 30px; margin-bottom: 0;}.s_quotes_carousel blockquote .s_quotes_carousel_icon{position: absolute; top: 0; left: -3rem;}.s_quotes_carousel blockquote img{max-width: 40px; margin-right: 5px; border-radius: 50%;}.s_quotes_carousel blockquote footer{background-color: transparent;}.s_quotes_carousel blockquote footer:before{content: "";}@media (max-width: 991.98px){.s_company_team img{max-width: 50%;}}.o_gallery.o_grid .img, .o_gallery.o_masonry .img{width: 100%;}.o_gallery.o_grid.o_spc-none div.row{margin: 0;}.o_gallery.o_grid.o_spc-none div.row > div{padding: 0;}.o_gallery.o_grid.o_spc-small div.row{margin: 5px 0;}.o_gallery.o_grid.o_spc-small div.row > div{padding: 0 5px;}.o_gallery.o_grid.o_spc-medium div.row{margin: 10px 0;}.o_gallery.o_grid.o_spc-medium div.row > div{padding: 0 10px;}.o_gallery.o_grid.o_spc-big div.row{margin: 15px 0;}.o_gallery.o_grid.o_spc-big div.row > div{padding: 0 15px;}.o_gallery.o_grid.size-auto .row{height: auto;}.o_gallery.o_grid.size-small .row{height: 100px;}.o_gallery.o_grid.size-medium .row{height: 250px;}.o_gallery.o_grid.size-big .row{height: 400px;}.o_gallery.o_grid.size-small img, .o_gallery.o_grid.size-medium img, .o_gallery.o_grid.size-big img{height: 100%;}.o_gallery.o_masonry.o_spc-none div.col{padding: 0;}.o_gallery.o_masonry.o_spc-none div.col > img{margin: 0 !important;}.o_gallery.o_masonry.o_spc-small div.col{padding: 0 5px;}.o_gallery.o_masonry.o_spc-small div.col > img{margin: 5px 0 !important;}.o_gallery.o_masonry.o_spc-medium div.col{padding: 0 10px;}.o_gallery.o_masonry.o_spc-medium div.col > img{margin: 10px 0 !important;}.o_gallery.o_masonry.o_spc-big div.col{padding: 0 15px;}.o_gallery.o_masonry.o_spc-big div.col > img{margin: 15px 0 !important;}.o_gallery.o_nomode.o_spc-none .img{padding: 0;}.o_gallery.o_nomode.o_spc-small .img{padding: 5px;}.o_gallery.o_nomode.o_spc-medium .img{padding: 10px;}.o_gallery.o_nomode.o_spc-big .img{padding: 15px;}.o_gallery.o_slideshow .carousel ul.carousel-indicators li{border: 1px solid #aaa;}.o_gallery.o_slideshow > .container{height: 100%;}.o_gallery .carousel-inner .item img{max-width: none;}.o_gallery.o_slideshow .carousel, .modal-body.o_slideshow .carousel{height: 100%;}.o_gallery.o_slideshow .carousel .carousel-inner, .modal-body.o_slideshow .carousel .carousel-inner{height: 100%;}.o_gallery.o_slideshow .carousel .carousel-item.active, .o_gallery.o_slideshow .carousel .carousel-item-next, .o_gallery.o_slideshow .carousel .carousel-item-prev, .modal-body.o_slideshow .carousel .carousel-item.active, .modal-body.o_slideshow .carousel .carousel-item-next, .modal-body.o_slideshow .carousel .carousel-item-prev{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; height: 100%; padding-bottom: 64px;}.o_gallery.o_slideshow .carousel img, .modal-body.o_slideshow .carousel img{max-height: 100%; max-width: 100%; margin: auto;}.o_gallery.o_slideshow .carousel ul.carousel-indicators, .modal-body.o_slideshow .carousel ul.carousel-indicators{height: auto; padding: 0; border-width: 0; position: absolute; bottom: 0; width: 100%; margin-left: 0; left: 0%;}.o_gallery.o_slideshow .carousel ul.carousel-indicators > *, .modal-body.o_slideshow .carousel ul.carousel-indicators > *{list-style-image: none; display: inline-block; width: 40px; height: 40px; margin: 2.5px 2.5px 2.5px 2.5px; padding: 0; border: 1px solid #aaa; text-indent: initial; background-size: cover; background-color: #fff; border-radius: 0; vertical-align: bottom; flex: 0 0 40px;}.o_gallery.o_slideshow .carousel ul.carousel-indicators > *:not(.active), .modal-body.o_slideshow .carousel ul.carousel-indicators > *:not(.active){opacity: 0.8; filter: grayscale(1);}.parallax{position: relative; overflow: hidden;}.parallax > *{position: relative;}.parallax > .s_parallax_bg{position: absolute; top: 0; left: 0; bottom: 0; right: 0; display: block; background-color: inherit; background-size: cover; background-attachment: scroll; pointer-events: none;}.parallax > .s_parallax_bg::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; display: block; background-color: inherit;}@media (min-width: 1200px){.parallax.s_parallax_is_fixed > .s_parallax_bg{background-attachment: fixed;}}.s_hr{line-height: 0;}.s_hr hr{padding: 0; border: 0; border-top-color: inherit; margin: 0;}.s_hr .s_hr_dashed{border-top-style: dashed;}.s_hr .s_hr_double{border-top-style: double;}.s_hr .s_hr_dotted{border-top-style: dotted;}.s_hr .s_hr_solid{border-top-style: solid;}.s_hr .s_hr_5px{border-top-width: 5px;}.s_hr .s_hr_4px{border-top-width: 4px;}.s_hr .s_hr_3px{border-top-width: 3px;}.s_hr .s_hr_2px{border-top-width: 2px;}.s_hr .s_hr_1px{border-top-width: 1px;}.s_card{margin: 15px 0;}.s_card .card-body p:last-child, .s_card .card-body ul:last-child, .s_card .card-body ol:last-child{margin-bottom: 0;}.s_share > *{display: inline-block; vertical-align: middle;}.s_share .s_share_title{margin: 0 .4rem 0 0;}.s_share > a + a{margin-left: .4rem;}.s_share .s_share_facebook, .s_share .s_share_facebook:hover, .s_share .s_share_facebook:focus{color: #3b5998;}.s_share .s_share_twitter, .s_share .s_share_twitter:hover, .s_share .s_share_twitter:focus{color: #1da1f2;}.s_share .s_share_linkedin, .s_share .s_share_linkedin:hover, .s_share .s_share_linkedin:focus{color: #0077b5;}.s_share .s_share_google, .s_share .s_share_google:hover, .s_share .s_share_google:focus{color: #db4437;}.s_rating > .s_rating_stars .fa:before{content: "ï€†";}.s_rating > .s_rating_stars.s_rating_5 .fa:nth-of-type(-n+5):before{content: "ï€…";}.s_rating > .s_rating_stars.s_rating_4 .fa:nth-of-type(-n+4):before{content: "ï€…";}.s_rating > .s_rating_stars.s_rating_3 .fa:nth-of-type(-n+3):before{content: "ï€…";}.s_rating > .s_rating_stars.s_rating_2 .fa:nth-of-type(-n+2):before{content: "ï€…";}.s_rating > .s_rating_stars.s_rating_1 .fa:nth-of-type(-n+1):before{content: "ï€…";}.s_rating > .s_rating_squares .fa:before{content: "ï„Œ";}.s_rating > .s_rating_squares.s_rating_5 .fa:nth-of-type(-n+5):before{content: "ï„‘";}.s_rating > .s_rating_squares.s_rating_4 .fa:nth-of-type(-n+4):before{content: "ï„‘";}.s_rating > .s_rating_squares.s_rating_3 .fa:nth-of-type(-n+3):before{content: "ï„‘";}.s_rating > .s_rating_squares.s_rating_2 .fa:nth-of-type(-n+2):before{content: "ï„‘";}.s_rating > .s_rating_squares.s_rating_1 .fa:nth-of-type(-n+1):before{content: "ï„‘";}.s_rating > .s_rating_hearts .fa:before{content: "ï‚Š";}.s_rating > .s_rating_hearts.s_rating_5 .fa:nth-of-type(-n+5):before{content: "ï€„";}.s_rating > .s_rating_hearts.s_rating_4 .fa:nth-of-type(-n+4):before{content: "ï€„";}.s_rating > .s_rating_hearts.s_rating_3 .fa:nth-of-type(-n+3):before{content: "ï€„";}.s_rating > .s_rating_hearts.s_rating_2 .fa:nth-of-type(-n+2):before{content: "ï€„";}.s_rating > .s_rating_hearts.s_rating_1 .fa:nth-of-type(-n+1):before{content: "ï€„";}.s_rating > .s_rating_bar .fa{display: none;}.s_rating > .s_rating_bar .s_rating_bar{display: -webkit-box; display: -webkit-flex; display: flex; height: 1rem; background-color: #dee2e6;}.s_rating > .s_rating_bar .s_rating_bar:before{content: ""; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; transition: width 0.6s ease; background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 1rem 1rem; background-color: #00A09D; animation: progress-bar-stripes 1s linear infinite;}@media (prefers-reduced-motion: reduce){.s_rating > .s_rating_bar .s_rating_bar:before{transition: none;}}.s_rating > .s_rating_bar.s_rating_5 .s_rating_bar:before{width: 100%;}.s_rating > .s_rating_bar.s_rating_4 .s_rating_bar:before{width: 80%;}.s_rating > .s_rating_bar.s_rating_3 .s_rating_bar:before{width: 60%;}.s_rating > .s_rating_bar.s_rating_2 .s_rating_bar:before{width: 40%;}.s_rating > .s_rating_bar.s_rating_1 .s_rating_bar:before{width: 20%;}.s_rating > .s_rating_1x .fa{font-size: 1em;}.s_rating > .s_rating_2x .fa{font-size: 2em;}.s_rating > .s_rating_3x .fa{font-size: 3em;}.o_header_affix{display: block; position: absolute; top: 0; left: 0; bottom: auto; right: 0; position: fixed; z-index: 1030; background: #f6f9f9; margin-top: -999px; transition: margin-top 500ms ease 0s;}@media (max-width: 767.98px){.o_header_affix .navbar-collapse{max-height: 70vh; overflow-y: auto;}}.o_header_affix.affixed{margin-top: 0px !important;}#oe_main_menu_navbar + #wrapwrap .o_header_affix{top: 46px;}.navbar .o_extra_menu_items.show > ul > li + li{border-top: 1px solid #e9ecef;}.navbar .o_extra_menu_items.show > ul > li > a.dropdown-toggle{background-color: #e9ecef; color: inherit; pointer-events: none;}.navbar .o_extra_menu_items.show > ul > li > ul{position: static; float: none; display: block; max-height: none; margin-top: 0; padding: 0; border: none; box-shadow: none;}.o_mega_menu{width: 100%; padding: 0; margin-top: 0; border-radius: 0; background-clip: unset;}.o_mega_menu .container, .o_mega_menu .container-fluid{padding-left: 15px; padding-right: 15px;}@media (min-width: 768px){.o_mega_menu_container_size{left: 50%; transform: translateX(-50%);}}@media (min-width: 576px){.o_mega_menu_container_size{max-width: 510px;}}@media (min-width: 768px){.o_mega_menu_container_size{max-width: 690px;}}@media (min-width: 992px){.o_mega_menu_container_size{max-width: 930px;}}@media (min-width: 1200px){.o_mega_menu_container_size{max-width: 1110px;}}.o_footer_copyright{padding: 8px;}.o_footer_copyright .js_language_selector{display: inline-block;}@media (min-width: 768px){.o_footer_copyright .row{display: -webkit-box; display: -webkit-flex; display: flex;}.o_footer_copyright .row > div{margin: auto 0;}}#wrapwrap.o_header_overlay > header:not(.o_header_affix){position: absolute; top: 0; left: 0; bottom: auto; right: 0; z-index: 1000;}#wrapwrap.o_header_overlay > header:not(.o_header_affix) > .navbar{background-color: transparent !important; border-color: transparent;}#wrapwrap.o_header_overlay > header:not(.o_header_affix) > .navbar .nav-item > .nav-link{background-color: transparent; color: inherit;}#wrapwrap.o_header_overlay > header:not(.o_header_affix) > .navbar .nav-item.show > .nav-link, #wrapwrap.o_header_overlay > header:not(.o_header_affix) > .navbar .nav-item.active > .nav-link{background-color: white; color: black;}.o_figure_relative_layout{position: relative;}.o_figure_relative_layout .figure-img{margin-bottom: 0;}.o_figure_relative_layout .figure-caption{position: absolute; top: auto; left: 0; bottom: 0; right: 0; background-color: rgba(20, 31, 30, 0.6) !important; color: #FFFFFF; padding: 0.25rem 0.5rem; font-weight: 700;}.o_figure_relative_layout .figure-caption .text-muted{color: rgba(255, 255, 255, 0.4) !important;}.o_figure_relative_layout .figure-caption a{color: inherit;}.bg-primary-light{background-color: rgba(0, 160, 157, 0.1);}.bg-secondary-light{background-color: rgba(135, 90, 123, 0.1);}.bg-success-light{background-color: rgba(40, 167, 69, 0.1);}.bg-info-light{background-color: rgba(23, 162, 184, 0.1);}.bg-warning-light{background-color: rgba(255, 193, 7, 0.1);}.bg-danger-light{background-color: rgba(220, 53, 69, 0.1);}.bg-light-light{background-color: rgba(246, 249, 249, 0.1);}.bg-dark-light{background-color: rgba(20, 31, 30, 0.1);}.bg-alpha-light{background-color: rgba(0, 160, 157, 0.1);}.bg-beta-light{background-color: rgba(135, 90, 123, 0.1);}.bg-gamma-light{background-color: rgba(92, 91, 128, 0.1);}.bg-delta-light{background-color: rgba(91, 137, 158, 0.1);}.bg-epsilon-light{background-color: rgba(228, 111, 120, 0.1);}.text-facebook{color: #3B5999 !important;}a.text-facebook:hover, a.text-facebook:focus{color: #263962 !important;}.text-twitter{color: #55ACEE !important;}a.text-twitter:hover, a.text-twitter:focus{color: #1689e0 !important;}.text-linkedin{color: #0077B5 !important;}a.text-linkedin:hover, a.text-linkedin:focus{color: #004569 !important;}.text-google-plus{color: #DD4B39 !important;}a.text-google-plus:hover, a.text-google-plus:focus{color: #ac2d1e !important;}.text-youtube{color: #ff0000 !important;}a.text-youtube:hover, a.text-youtube:focus{color: #b30000 !important;}.text-github{color: #1a1e22 !important;}a.text-github:hover, a.text-github:focus{color: black !important;}.text-instagram{color: #cf2872 !important;}a.text-instagram:hover, a.text-instagram:focus{color: #8f1c4f !important;}.modal-footer > .float-left{margin-right: auto;}.o_record_cover_container{position: relative;}.o_record_cover_container .o_record_cover_component{position: absolute; top: 0; left: 0; bottom: 0; right: 0; background-size: cover; background-position: center; background-repeat: no-repeat;}

/* /website/static/src/scss/website.ui.scss defined in bundle 'web.assets_frontend' */
 body{direction: ltr;}body.o_connected_user{padding-top: 46px !important;}#oe_main_menu_navbar{position: absolute; top: 0; left: 0; bottom: auto; right: 0; position: fixed; z-index: 1040; font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 14px;}#oe_main_menu_navbar .dropdown-menu{background-color: white;}#oe_main_menu_navbar .dropdown-item{color: #212529;}#oe_main_menu_navbar .dropdown-item:hover, #oe_main_menu_navbar .dropdown-item:focus{color: #16181b;}#oe_main_menu_navbar .dropdown-item.active, #oe_main_menu_navbar .dropdown-item:active{color: white; background-color: #7C7BAD;}#oe_main_menu_navbar a:hover, #oe_main_menu_navbar a:focus{text-decoration: none;}#oe_main_menu_navbar .dropdown-menu{font-size: inherit; border-radius: 0; color: #FFFFFF;}#oe_main_menu_navbar .o_menu_sections .o_mobile_preview a{text-align: center; font-size: 20px;}#oe_main_menu_navbar .o_menu_systray > li > a{padding: 0 15px;}#oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic{padding: 0 7.5px;}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"], #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"], #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:hover, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:hover, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:hover{color: #FFFFFF; background-color: #65639e; border-color: #5f5e97;}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"].focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"].focus, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:focus, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic.focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"].disabled, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:disabled, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"].disabled, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:disabled, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic.disabled, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:disabled{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:not(:disabled):not(.disabled):active, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:not(:disabled):not(.disabled).active, .show > #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"].dropdown-toggle, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:not(:disabled):not(.disabled):active, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:not(:disabled):not(.disabled).active, .show > #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"].dropdown-toggle, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:not(:disabled):not(.disabled):active, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:not(:disabled):not(.disabled).active, .show > #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic.dropdown-toggle{color: #FFFFFF; background-color: #5f5e97; border-color: #5a598f;}#oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:not(:disabled):not(.disabled):active:focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"]:not(:disabled):not(.disabled).active:focus, .show > #oe_main_menu_navbar .o_menu_systray > li > a[data-action="edit"].dropdown-toggle:focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:not(:disabled):not(.disabled):active:focus, #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"]:not(:disabled):not(.disabled).active:focus, .show > #oe_main_menu_navbar .o_menu_systray > li > a[data-action="translate"].dropdown-toggle:focus, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:not(:disabled):not(.disabled):active:focus, #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic:not(:disabled):not(.disabled).active:focus, .show > #oe_main_menu_navbar .o_menu_systray > li > a.css_edit_dynamic.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}#oe_main_menu_navbar .o_menu_systray .o_mobile_preview a{text-align: center; font-size: 20px;}@media (max-width: 767.98px){#oe_main_menu_navbar #oe_applications{position: inherit; z-index: 1002;}}body .modal.o_technical_modal{font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; line-height: 1.5; color: #33363e; background: 0;}body .modal.o_technical_modal .text-muted{color: #999999 !important;}body .modal.o_technical_modal .dropdown-menu{background-color: white;}body .modal.o_technical_modal .dropdown-item{color: #212529;}body .modal.o_technical_modal .dropdown-item:hover, body .modal.o_technical_modal .dropdown-item:focus{color: #16181b;}body .modal.o_technical_modal .dropdown-item.active, body .modal.o_technical_modal .dropdown-item:active{color: white; background-color: #7C7BAD;}body .modal.o_technical_modal h1, body .modal.o_technical_modal h2, body .modal.o_technical_modal h3, body .modal.o_technical_modal h4, body .modal.o_technical_modal h5, body .modal.o_technical_modal h6, body .modal.o_technical_modal .h1, body .modal.o_technical_modal .h2, body .modal.o_technical_modal .h3, body .modal.o_technical_modal .h4, body .modal.o_technical_modal .h5, body .modal.o_technical_modal .h6{font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; line-height: 1.5; color: #2c2c36; font-weight: bold;}body .modal.o_technical_modal :not(.input-group):not(.form-group):not(.input-group-append):not(.input-group-prepend) > .form-control{height: 34px;}body .modal.o_technical_modal .form-control{padding: 6px 12px; font-size: 14px; line-height: 1.5; border: 1px solid #d4d5d7; color: #555; background-color: #fff; border-radius: 0;}body .modal.o_technical_modal .form-control.is-invalid{border-color: #dc3545;}body .modal.o_technical_modal .input-group .form-control{height: auto;}body .modal.o_technical_modal .input-group-text{background-color: #e9ecef;}body .modal.o_technical_modal .was-validated .form-control:invalid{border-color: #dc3545;}body .modal.o_technical_modal select.form-control{-webkit-appearance: none; -moz-appearance: none; appearance: none; background: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPScxLjEnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgeG1 sbnM6eGxpbms9J2h0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsnIHdpZHRoPScyNCcgaGVpZ2 h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0Jz48cGF0aCBpZD0nc3ZnXzEnIGQ9J203LjQwNiw3L jgyOGw0LjU5NCw0LjU5NGw0LjU5NCwtNC41OTRsMC40MDYsMS40MDZsLTUsNC43NjZsLTUsLTQu NzY2bDAuNDA2LC0xLjQwNnonIGZpbGw9JyM4ODgnLz48L3N2Zz4="); background-position: 100% 65%; background-repeat: no-repeat;}body .modal.o_technical_modal a:not(.o_btn_preview){color: #7C7BAD;}body .modal.o_technical_modal a:not(.o_btn_preview):focus, body .modal.o_technical_modal a:not(.o_btn_preview):active, body .modal.o_technical_modal a:not(.o_btn_preview):focus:active{outline: none !important;}body .modal.o_technical_modal .badge:hover a, body .modal.o_technical_modal .badge a{color: #fff;}body .modal.o_technical_modal .btn:not(.o_btn_preview){border-radius: 0; font-weight: normal; text-transform: none; padding: 0.375rem 0.75rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:hover{color: #FFFFFF; background-color: #65639e; border-color: #5f5e97;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary.focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:disabled{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #5f5e97; border-color: #5a598f;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7; color: #7C7BAD;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:hover{color: #212529; background-color: #e4e4e4; border-color: #dedddd;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:disabled{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle{color: #212529; background-color: #dedddd; border-color: #d7d7d7;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7; color: #7C7BAD;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:hover{color: #212529; background-color: #e4e4e4; border-color: #dedddd;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link.focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:disabled{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link.dropdown-toggle{color: #212529; background-color: #dedddd; border-color: #d7d7d7;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-link.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:hover{color: #FFFFFF; background-color: #369156; border-color: #328851;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success.focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:disabled{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success.dropdown-toggle{color: #FFFFFF; background-color: #328851; border-color: #2f7e4b;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-success.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:hover{color: #FFFFFF; background-color: #568695; border-color: #517e8d;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info.focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:disabled{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info.dropdown-toggle{color: #FFFFFF; background-color: #517e8d; border-color: #4d7784;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-info.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:hover{color: #212529; background-color: #ed9d2b; border-color: #ec971f;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning.focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:disabled{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning.dropdown-toggle{color: #212529; background-color: #ec971f; border-color: #ea9214;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-warning.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:hover{color: #FFFFFF; background-color: #e1374f; border-color: #df2c45;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger.focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger.disabled, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:disabled{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger.dropdown-toggle{color: #FFFFFF; background-color: #df2c45; border-color: #dd213c;}body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active:focus, body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active:focus, .show > body .modal.o_technical_modal .btn:not(.o_btn_preview).btn-danger.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}body .modal.o_technical_modal .card{padding: 19px; margin-bottom: 20px; background-color: #F7F7F7; border: 1px solid #eaeaea; border-radius: 0; box-shadow: none;}body .modal.o_technical_modal .modal-content{border-radius: 0; background-color: #F7F7F7;}body .modal.o_technical_modal .modal-content .modal-header{border-bottom-color: #e9ecef;}body .modal.o_technical_modal .modal-content .modal-body{background-color: white;}body .modal.o_technical_modal .modal-content .modal-footer{border-top-color: #e9ecef; text-align: left;}body .modal.o_technical_modal .nav-tabs{border-bottom: 1px solid #e9ecef;}body .modal.o_technical_modal .nav-tabs > li > a{line-height: 1.5; color: #4e525b;}body .modal.o_technical_modal .nav-tabs > li > a:hover{border-color: #dee2e6;}body .modal.o_technical_modal .nav-tabs > li > a.active, body .modal.o_technical_modal .nav-tabs > li > a.active:hover, body .modal.o_technical_modal .nav-tabs > li > a.active:focus{color: #3D4047; background-color: #F7F7F7; border-color: #dee2e6 #dee2e6 #FFFFFF;}body .modal.oe_mobile_preview{text-align: center;}body .modal.oe_mobile_preview .modal-dialog{display: inline-block; width: auto;}body .modal.oe_mobile_preview .modal-dialog .modal-content{background-color: black !important; border: 3px outset gray; border-radius: 20px;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header{border: none; cursor: pointer; font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header, body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header .close{color: white;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header h4{font-family: inherit; font-weight: normal; color: inherit;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header h4 .fa{margin-left: 15px;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-header .close{color: #4e525b;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-body{background-color: inherit !important; border-radius: 20px; padding: 15px; display: -webkit-box; display: -webkit-flex; display: flex; width: 335px; height: 530px; transition: all 400ms ease 0s;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-body.o_invert_orientation{width: 545px; height: 320px;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-body > iframe{display: block; width: 100%; border: none;}body .modal.oe_mobile_preview .modal-dialog .modal-content .modal-footer{display: none;}body .modal .oe_menu_editor ul{padding-left: 37px;}body .modal .oe_menu_editor li{margin-top: -1px;}body .modal .oe_menu_editor li .input-group-addon{border-radius: 0;}body .modal.oe_seo_configuration #language-box{padding-right: 25px; background-color: white;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img{position: relative; transition: border-color 200ms; display: inline-block; border: 2px solid #ced4da;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img > img{width: 70px; height: 70px; object-fit: cover; cursor: pointer;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img:hover{border-color: #7C7BAD;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img.o_active_image{border-color: #7C7BAD;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img.o_active_image:before{position: absolute; top: auto; left: auto; bottom: auto; right: 0; content: ''; border: 16px solid rgba(124, 123, 173, 0.8); border-left-color: transparent; border-bottom-color: transparent;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img.o_active_image:after{position: absolute; top: 2px; left: auto; bottom: auto; right: 3px; display: inline-block; content: "\f00c"; font-family: FontAwesome; color: white; font-size: 12px;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img .o-custom-label{position: absolute; top: auto; left: auto; bottom: 0px; right: auto; background: rgba(52, 58, 64, 0.6); font-size: 12px;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img_upload{transition: 200ms; display: inline-block; padding: 23px 27px; border: 2px dashed #bcc1c6; vertical-align: top; cursor: pointer; color: #bcc1c6;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_img_upload:hover{border-color: #7C7BAD; color: #7C7BAD;}body .modal.oe_seo_configuration .o_seo_og_image .o_meta_active_img{height: 240px; object-fit: cover;}body .modal.oe_seo_configuration div.oe_seo_preview_g{list-style: none; font-family: arial, sans-serif;}body .modal.oe_seo_configuration div.oe_seo_preview_g .r{cursor: pointer; color: #1a0dab; font-size: 18px; overflow: hidden; text-overflow: ellipsis; -webkit-text-overflow: ellipsis; white-space: nowrap;}body .modal.oe_seo_configuration div.oe_seo_preview_g .s{font-size: 13px; line-height: 18px; color: #545454;}body .modal.oe_seo_configuration div.oe_seo_preview_g .s .kv{color: #006621; font-size: 14px; line-height: 18px;}body .modal.oe_seo_configuration td.o_seo_keyword_suggestion span.o_seo_suggestion.badge{cursor: pointer;}.o_new_content_open{overflow: hidden;}#o_new_content_menu_choices{font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; line-height: 1.5; color: #33363e; position: absolute; top: 46px; left: 0; bottom: 0; right: 0; position: fixed; display: -webkit-box; display: -webkit-flex; display: flex; overflow: auto; background-color: rgba(0, 0, 0, 0.8); font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif;}#o_new_content_menu_choices .text-muted{color: #999999 !important;}#o_new_content_menu_choices::before{content: " "; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; pointer-events: none;}#o_new_content_menu_choices .container{max-width: 720px; margin: auto;}#o_new_content_menu_choices .o_new_content_element{opacity: 0; animation: fadeInDownSmall 1s forwards;}#o_new_content_menu_choices .o_new_content_element a{display: block; font-size: 34px; text-align: center;}#o_new_content_menu_choices .o_new_content_element a i{width: 110px; height: 110px; border: 3px solid #434352; border-radius: 100%; line-height: 104px; background-color: #2C2C36; color: white; transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1) 0s;}#o_new_content_menu_choices .o_new_content_element a p{color: white; margin-top: 0.7em; font-size: 0.5em;}#o_new_content_menu_choices .o_new_content_element a:hover, #o_new_content_menu_choices .o_new_content_element a:focus{text-decoration: none; outline: none;}#o_new_content_menu_choices .o_new_content_element a:hover i, #o_new_content_menu_choices .o_new_content_element a:focus i{border-color: #1cc1a9; box-shadow: 0 0 10px rgba(28, 193, 169, 0.46);}.oe_login_form, .oe_signup_form, .oe_reset_password_form{max-width: 300px; position: relative; margin: 50px auto;}.o_ace_view_editor{font-family: Roboto, "Montserrat", "Segoe UI", "Helvetica Neue", Helvetica, Arial, sans-serif; line-height: 1.5; color: #33363e; position: absolute; top: 46px; left: auto; bottom: 0; right: 0; position: fixed; z-index: 1050;}.o_ace_view_editor .text-muted{color: #999999 !important;}.o_ace_view_editor .btn:not(.o_btn_preview){border-radius: 0; font-weight: normal; text-transform: none; padding: 0.375rem 0.75rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:hover{color: #FFFFFF; background-color: #65639e; border-color: #5f5e97;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary.focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:disabled{color: #FFFFFF; background-color: #7C7BAD; border-color: #7C7BAD;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #5f5e97; border-color: #5a598f;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(144, 143, 185, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7; color: #7C7BAD;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:hover{color: #212529; background-color: #e4e4e4; border-color: #dedddd;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary.focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:disabled{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle{color: #212529; background-color: #dedddd; border-color: #d7d7d7;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7; color: #7C7BAD;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link:hover{color: #212529; background-color: #e4e4e4; border-color: #dedddd;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-link.focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-link:disabled{color: #212529; background-color: #F7F7F7; border-color: #F7F7F7;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-link.dropdown-toggle{color: #212529; background-color: #dedddd; border-color: #d7d7d7;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-link:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-link.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(215, 216, 216, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success:hover{color: #FFFFFF; background-color: #369156; border-color: #328851;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-success.focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-success:disabled{color: #FFFFFF; background-color: #40ad67; border-color: #40ad67;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-success.dropdown-toggle{color: #FFFFFF; background-color: #328851; border-color: #2f7e4b;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-success:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-success.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(93, 185, 126, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info:hover{color: #FFFFFF; background-color: #568695; border-color: #517e8d;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-info.focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-info:disabled{color: #FFFFFF; background-color: #6999a8; border-color: #6999a8;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-info.dropdown-toggle{color: #FFFFFF; background-color: #517e8d; border-color: #4d7784;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-info:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-info.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(128, 168, 181, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:hover{color: #212529; background-color: #ed9d2b; border-color: #ec971f;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning.focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:disabled{color: #212529; background-color: #f0ad4e; border-color: #f0ad4e;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning.dropdown-toggle{color: #212529; background-color: #ec971f; border-color: #ea9214;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-warning.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(209, 153, 72, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:hover{color: #FFFFFF; background-color: #e1374f; border-color: #df2c45;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger.focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger.disabled, .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:disabled{color: #FFFFFF; background-color: #e6586c; border-color: #e6586c;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active, .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger.dropdown-toggle{color: #FFFFFF; background-color: #df2c45; border-color: #dd213c;}.o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled):active:focus, .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger:not(:disabled):not(.disabled).active:focus, .show > .o_ace_view_editor .btn:not(.o_btn_preview).btn-danger.dropdown-toggle:focus{box-shadow: 0 0 0 0.2rem rgba(234, 113, 130, 0.5);}.o_ace_view_editor :not(.input-group):not(.form-group):not(.input-group-append):not(.input-group-prepend) > .form-control{height: 34px;}.o_ace_view_editor .form-control{padding: 6px 12px; font-size: 14px; line-height: 1.5; border: 1px solid #d4d5d7; color: #555; background-color: #fff; border-radius: 0;}.o_ace_view_editor .form-control.is-invalid{border-color: #dc3545;}.o_ace_view_editor .input-group .form-control{height: auto;}.o_ace_view_editor .input-group-text{background-color: #e9ecef;}.o_ace_view_editor .was-validated .form-control:invalid{border-color: #dc3545;}.o_ace_view_editor select.form-control{-webkit-appearance: none; -moz-appearance: none; appearance: none; background: url("data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPScxLjEnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgeG1 sbnM6eGxpbms9J2h0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsnIHdpZHRoPScyNCcgaGVpZ2 h0PScyNCcgdmlld0JveD0nMCAwIDI0IDI0Jz48cGF0aCBpZD0nc3ZnXzEnIGQ9J203LjQwNiw3L jgyOGw0LjU5NCw0LjU5NGw0LjU5NCwtNC41OTRsMC40MDYsMS40MDZsLTUsNC43NjZsLTUsLTQu NzY2bDAuNDA2LC0xLjQwNnonIGZpbGw9JyM4ODgnLz48L3N2Zz4="); background-position: 100% 65%; background-repeat: no-repeat;}.tour .popover-navigation{margin-left: 13px; margin-bottom: 8px;}.css_published .btn-danger, .css_published .css_publish{display: none;}.css_unpublished .btn-success, .css_unpublished .css_unpublish{display: none;}[data-publish='off'] > *:not(.css_options){opacity: 0.5;}@media print{a[href]:after{content: initial;}}.o_page_management_info .o_switch{padding-top: 9px;}#list_website_pages th{background-color: #7C7BAD; color: white;}#list_website_pages td, #list_website_pages th{padding: 0.45rem;}#list_website_pages td > a.fa{margin-left: 5px; color: #7C7BAD;}#list_website_pages td .text-muted{opacity: 0.5;}#list_website_pages .fa-check, #list_website_pages .fa-eye-slash{color: #17a2b8;}

/* /website/static/src/scss/user_custom_rules.scss defined in bundle 'web.assets_frontend' */
 

/* /website_form/static/src/scss/website_form.scss defined in bundle 'web.assets_frontend' */
 .o_website_form_flex{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap;}.o_website_form_flex_item{flex-basis: 33%;}@media (max-width: 991.98px){.o_website_form_flex_item{flex-basis: 50%;}}@media (max-width: 767.98px){.o_website_form_flex_item{flex-basis: 100%;}}.o_website_form_field_hidden{display: none;}.editor_enable .o_website_form_field_hidden{display: -webkit-box; display: -webkit-flex; display: flex; opacity: 0.5;}.editor_enable .s_website_form .form-field select{pointer-events: none;}.o_website_form_required .col-form-label:after, .o_website_form_required_custom .col-form-label:after{content: ' *';}#editable_select.form-control{height: 100%;}.form-field input[type=file].form-control{height: 100%;}

/* /website_mail/static/src/css/website_mail.scss defined in bundle 'web.assets_frontend' */
 .js_follow[data-follow='on'] .js_follow_btn, .js_follow[data-follow='off'] .js_unfollow_btn{display: none;}.js_follow_icons_container .js_follow_btn, .js_follow_icons_container .js_unfollow_btn{animation: js_follow_fade 1s ease forwards; opacity: 0;}.js_follow_icons_container .js_follow_btn small, .js_follow_icons_container .js_unfollow_btn small{opacity: 0; transition: opacity 0.3s ease;}.js_follow_icons_container .js_follow_btn:hover small, .js_follow_icons_container .js_follow_btn:focus small, .js_follow_icons_container .js_unfollow_btn:hover small, .js_follow_icons_container .js_unfollow_btn:focus small{transition-duration: 1s; opacity: 1;}.js_follow_icons_container .fa:before{content: "\f0f3"; color: #6c757d;}.js_follow_icons_container .js_follow_btn:hover .fa:before{color: #212529;}.js_follow_icons_container .js_unfollow_btn .fa:before{color: #00A09D;}.js_follow_icons_container .js_unfollow_btn:hover .fa:before{content: "\f1f6"; color: #dc3545;}@keyframes js_follow_fade{to{opacity: 1;}}

/* /website_sale/static/src/scss/website_sale.scss defined in bundle 'web.assets_frontend' */
 .oe_website_sale ul ul{margin-left: 1.5rem;}.oe_website_sale .o_payment_form .card{border-radius: 4px !important;}.oe_website_sale .address-inline address{display: inline-block;}.oe_website_sale table#cart_products tr td, .oe_website_sale table#suggested_products tr td{vertical-align: middle;}.oe_website_sale table#cart_products{margin-bottom: 0;}.oe_website_sale table#cart_products td:first-child, .oe_website_sale table#cart_products th:first-child{padding-left: 15px;}.oe_website_sale h1[itemprop="name"], .oe_website_sale .oe_product_cart form h5{word-wrap: break-word;}@media (max-width: 767.98px){.oe_website_sale .td-img{display: none;}}@media (min-width: 1200px){.oe_website_sale .toggle_summary_div{max-width: 400px;}}.oe_website_sale input.js_quantity{min-width: 48px; text-align: center;}.oe_website_sale input.quantity{padding: 0;}.o_alternative_product{margin: auto;}.oe_product_cart .oe_product_image{height: 0; text-align: center;}.oe_product_cart .oe_product_image img{max-height: 100%;}.oe_product_cart .o_wsale_product_information{position: relative; -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; transition: .3s ease;}.oe_product_cart .oe_subdescription{max-height: 0; overflow: hidden; font-size: 0.75rem; margin-bottom: 0.25rem; transform: scale(1, 0); transition: all ease 0.3s;}.oe_product_cart .o_wsale_product_btn{position: absolute; top: auto; left: 0; bottom: 100%; right: 0; padding-bottom: 0.25rem;}.oe_product_cart .o_wsale_product_btn .btn{transform: scale(0); transition: transform ease 200ms 0s;}.oe_product_cart .o_wsale_product_btn:empty{display: none !important;}.oe_product_cart:hover{box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);}.oe_product_cart:hover .o_wsale_product_information{background-color: #e9ecef !important;}.oe_product_cart:hover .oe_subdescription{max-height: 1.5em;}@media (min-width: 992px){.oe_product_cart:hover .oe_subdescription{max-height: 3em;}}@media (min-width: 1200px){.oe_product_cart:hover .oe_subdescription{max-height: 4.5em;}}.oe_product_cart:hover .oe_subdescription, .oe_product_cart:hover .o_wsale_product_btn .btn{transform: scale(1);}@media (max-width: 767.98px){.oe_product_cart .oe_subdescription, .oe_product_cart:hover .oe_subdescription{max-height: 4.5em;}.oe_product_cart .oe_subdescription, .oe_product_cart .o_wsale_product_btn .btn{transform: scale(1);}}.oe_product_cart .ribbon-wrapper{display: none; width: 85px; height: 88px; z-index: 5; overflow: hidden; position: absolute; top: 0; left: 0; bottom: auto; right: auto;}.oe_product_cart .ribbon{font: bold 15px Sans-Serif; color: white; text-align: center; transform: rotate(-45deg); position: relative; padding: 7px 0; left: -31px; top: 14px; width: 120px; cursor: default;}.oe_product.oe_image_full .oe_product_image{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.oe_product.oe_image_full .o_wsale_product_information{position: absolute; top: auto; left: 0; bottom: 0; right: 0;}.oe_product.oe_ribbon_promo .ribbon-wrapper{display: block;}#products_grid .table{table-layout: fixed;}#products_grid .table td{margin-top: 15px; padding: 0;}#products_grid .table tr:first-child td:first-child{margin-top: 0;}#products_grid .table .o_wsale_product_grid_wrapper{position: relative;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_1_1{padding-top: 100%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_1_2{padding-top: 200%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_1_3{padding-top: 300%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_1_4{padding-top: 400%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_2_1{padding-top: 50%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_2_2{padding-top: 100%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_2_3{padding-top: 150%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_2_4{padding-top: 200%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_3_1{padding-top: 33.33333333%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_3_2{padding-top: 66.66666667%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_3_3{padding-top: 100%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_3_4{padding-top: 133.33333333%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_4_1{padding-top: 25%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_4_2{padding-top: 50%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_4_3{padding-top: 75%;}#products_grid .table .o_wsale_product_grid_wrapper.o_wsale_product_grid_wrapper_4_4{padding-top: 100%;}#products_grid .table .o_wsale_product_grid_wrapper > *{position: absolute; top: 7.5px; left: 7.5px; bottom: 7.5px; right: 7.5px;}#products_grid .o_wsale_products_grid_table_wrapper{margin: -7.5px;}@media (max-width: 767.98px){#products_grid table, #products_grid tbody, #products_grid td, #products_grid tr{display: block; width: 100%;}#products_grid .table .o_wsale_product_grid_wrapper{padding-top: 100% !important;}}@media (min-width: 576px){#products_grid.o_wsale_layout_list table, #products_grid.o_wsale_layout_list tbody, #products_grid.o_wsale_layout_list td, #products_grid.o_wsale_layout_list tr{display: block; width: 100%;}#products_grid.o_wsale_layout_list .o_wsale_products_grid_table_wrapper{margin: 0;}#products_grid.o_wsale_layout_list .table .o_wsale_product_grid_wrapper{padding-top: 0 !important;}#products_grid.o_wsale_layout_list .table .o_wsale_product_grid_wrapper > *{position: absolute; top: 0; left: 0; bottom: 0; right: 0; position: relative;}#products_grid.o_wsale_layout_list .oe_product_cart{-webkit-flex-flow: row nowrap; flex-flow: row nowrap; min-height: 10rem;}#products_grid.o_wsale_layout_list .oe_product_cart .oe_product_image{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 10rem; max-width: 35%; min-width: 100px; height: auto;}#products_grid.o_wsale_layout_list .oe_product_cart .o_wsale_product_information{position: static; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: left !important;}#products_grid.o_wsale_layout_list .oe_product_cart .o_wsale_product_information_text{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#products_grid.o_wsale_layout_list .oe_product_cart .o_wsale_product_btn{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; position: static; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; align-items: center; padding: 0.5rem; background-color: #e9ecef;}#products_grid.o_wsale_layout_list .oe_product_cart .o_wsale_product_btn .btn + .btn{margin-top: 0.5rem;}#products_grid.o_wsale_layout_list .oe_product_cart .oe_subdescription{max-height: none !important;}#products_grid.o_wsale_layout_list .oe_product_cart .oe_subdescription, #products_grid.o_wsale_layout_list .oe_product_cart .o_wsale_product_btn .btn{transform: scale(1) !important;}#products_grid.o_wsale_layout_list .oe_product_cart:hover .o_wsale_product_information{background-color: #FFFFFF !important;}}.o_wsale_products_main_row{margin-top: 15px; margin-bottom: 15px;}.oe_cart table td:first-child{min-width: 76px;}.oe_cart > .oe_structure{clear: both;}div#payment_method div.list-group{margin-left: 40px;}div#payment_method .list-group-item{padding-top: 5px; padding-bottom: 5px;}ul.wizard{padding: 0; margin-top: 20px; list-style: none outside none; border-radius: 4px; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.065);}ul.wizard li{border: 1px solid #e9ecef; border-right-width: 0; position: relative; float: left; padding: 0 10px 0 20px; margin: 0; line-height: 38px; background: #fbfbfb;}ul.wizard li .chevron{position: absolute; top: 0; right: -10px; z-index: 1; display: block; border: 20px solid transparent; border-right: 0; border-left: 10px solid #e9ecef;}ul.wizard li .chevron:before{position: absolute; top: -20px; right: 1px; display: block; border: 20px solid transparent; border-right: 0; border-left: 10px solid #fbfbfb; content: "";}ul.wizard li .o_link_disable{text-decoration: none; color: inherit; cursor: text;}ul.wizard li.text-success{background: #f3f4f5;}ul.wizard li.text-success .chevron:before{border-left: 10px solid #f5f5f5;}ul.wizard li.text-primary{background: #f1f6fc;}ul.wizard li.text-primary .chevron:before{border-left: 10px solid #f1f6fc;}ul.wizard li:first-child{padding-left: 15px; border-radius: 4px 0 0 4px;}ul.wizard li:last-child{border-radius: 0 4px 4px 0; border-right-width: 1px;}ul.wizard li:last-child .chevron{display: none;}#o_shop_collapse_category li{width: 100%;}#o_shop_collapse_category li a{display: inline-block; width: 80%; padding-left: 3px;}#o_shop_collapse_category li i.fa{cursor: pointer;}.mycart-popover{max-width: 500px; min-width: 250px;}.mycart-popover .cart_line{border-bottom: 1px #EEE solid;}tr#empty{display: none;}.js_change_shipping{cursor: pointer;}a.no-decoration{cursor: pointer; text-decoration: none !important;}#o-carousel-product.css_not_available{opacity: 0.2;}#o-carousel-product .carousel-outer{height: 400px; max-height: 90vh;}#o-carousel-product .carousel-control-prev, #o-carousel-product .carousel-control-next{height: 70%; top: 15%; opacity: 0.5; cursor: pointer;}#o-carousel-product .carousel-control-prev:focus, #o-carousel-product .carousel-control-next:focus{opacity: 0.65;}#o-carousel-product .carousel-control-prev:hover, #o-carousel-product .carousel-control-next:hover{opacity: 0.8;}#o-carousel-product .carousel-control-prev > span, #o-carousel-product .carousel-control-next > span{background: rgba(0, 0, 0, 0.8);}#o-carousel-product .carousel-indicators li{width: 64px; height: 64px; text-indent: unset; border: 1px solid #6c757d; opacity: 0.5; position: relative;}#o-carousel-product .carousel-indicators li .o_product_video_thumb{position: absolute; top: 50%; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -50%); color: #ced4da;}#o-carousel-product .carousel-indicators li.active{opacity: 1; border: 1px solid #00A09D;}.ecom-zoomable:not(.ecom-autozoom) img[data-zoom]{cursor: zoom-in;}.ecom-zoomable.ecom-autozoom img[data-zoom]{cursor: crosshair;}.ecom-zoomable .o_editable img[data-zoom]{cursor: pointer;}#coupon_box form{max-width: 300px;}.o_website_sale_animate{opacity: 0.7; position: absolute !important; height: 150px; width: 150px; z-index: 1020;}.o_red_highlight{background: #dc3545 !important; box-shadow: 0 0 0 0 rgba(240, 8, 0, 0.4); transition: all 0.5s linear;}.o_shadow_animation{box-shadow: 0 0 5px 10px rgba(240, 8, 0, 0.4) !important;}.o_carousel_product_card .o_carousel_product_card_img_top{object-fit: scale-down;}@media (max-width: 767.98px){.o_carousel_product_card .o_carousel_product_card_img_top{height: 12rem;}}@media (min-width: 768px){.o_carousel_product_card .o_carousel_product_card_img_top{height: 8rem;}}@media (min-width: 992px){.o_carousel_product_card .o_carousel_product_card_img_top{height: 12rem;}}.o_carousel_product_card .o_carousel_product_img_link:hover + .o_carousel_product_remove{display: block;}@media (min-width: 576px){.o_carousel_product_card_wrap{float: left;}}.o_carousel_product_control{top: 33.33333333%; bottom: 33.33333333%; width: 2rem; border-radius: 5px; background-color: #00A09D;}.o_carousel_product_remove{position: absolute; display: none; cursor: pointer; right: 5%; top: 5%;}.o_carousel_product_remove:hover{display: block;}

/* /website_sale/static/src/scss/website_mail.scss defined in bundle 'web.assets_frontend' */
 .oe_msg img.oe_msg_avatar{width: 50px; margin-right: 10px;}.oe_msg_attachment{display: inline-block; width: 120px; margin: 4px 2px; min-height: 80px; position: relative; border-radius: 3px; text-align: center; vertical-align: top;}.oe_msg_attachment a img.oe_attachment_embedded{display: block; position: relative; margin: 0 0 0 10px; width: 100px; height: 80px; border-radius: 1px; border: solid 3px #FFF; -webkit-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.19); -moz-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.19); box-shadow: 0 3px 10px rgba(0, 0, 0, 0.19);}.oe_msg_attachment a div.oe_attachment_name{display: inline-block; max-width: 100%; padding: 1px 3px; margin-top: 2px; margin-bottom: 5px; background: #F4F5FA; overflow: hidden; color: #4c4c4c; text-shadow: none; border-radius: 3px; word-wrap: break-word;}

/* /website_sale/static/src/scss/website_sale_frontend.scss defined in bundle 'web.assets_frontend' */
 .progress-wizard{margin-top: 15px; padding: 0 15px;}@media (min-width: 768px){.progress-wizard{padding: 0;}}.progress-wizard .progress-wizard-step{position: relative;}@media (min-width: 768px){.progress-wizard .progress-wizard-step{margin-top: 0.8125rem; float: left; width: 33.33333333%;}.o_wizard_has_extra_step + .progress-wizard .progress-wizard-step{width: 25%;}}@media (max-width: 767.98px){.progress-wizard .progress-wizard-step.disabled, .progress-wizard .progress-wizard-step.complete{display: none;}}.progress-wizard .progress-wizard-step .progress-wizard-dot{width: 0.625rem; height: 0.625rem; position: relative; display: inline-block; background-color: white; border-radius: 50%; box-shadow: 0 0 0 0.125rem #e9ecef;}@media (min-width: 768px){.progress-wizard .progress-wizard-step .progress-wizard-dot{position: absolute; top: auto; left: 50%; bottom: auto; right: auto; margin: -0.375rem 0 0 -0.3125rem;}}.progress-wizard .progress-wizard-step .progress-wizard-steplabel{color: #6c757d; margin: 5px 0 5px 5px; font-size: 0.875rem; display: inline-block;}@media (min-width: 768px){.progress-wizard .progress-wizard-step .progress-wizard-steplabel{display: block; margin: 1rem 0 20px 0;}}@media (max-width: 767.98px){.progress-wizard .progress-wizard-step .progress-wizard-steplabel{margin-left: -15px; font-size: 24px;}}.progress-wizard .progress-wizard-step .progress-wizard-bar{height: 0.125rem; background-color: #e9ecef;}.progress-wizard .progress-wizard-step.active .progress-wizard-dot{animation: fadeIn 1s ease 0s 1 normal none running; background: #00A09D; box-shadow: 0 0 0 0.1875rem white, 0 0 0 0.25rem rgba(0, 160, 157, 0.5);}.progress-wizard .progress-wizard-step.active .progress-wizard-steplabel{color: #212529; font-weight: bolder;}.progress-wizard .progress-wizard-step.complete .progress-wizard-dot{background: none; box-shadow: none;}.progress-wizard .progress-wizard-step.complete .progress-wizard-dot:after{position: absolute; top: -0.3125rem; left: -0.3125rem; bottom: auto; right: auto; width: 1.25rem; height: 1.25rem; border-radius: 100%; background: white; color: #28a745; text-align: center; line-height: 1; font-size: 1.25rem; font-family: FontAwesome; content: "\f058";}.progress-wizard .progress-wizard-step.complete .progress-wizard-steplabel{color: #28a745;}.progress-wizard .progress-wizard-step.complete:hover:not(.disabled) .progress-wizard-dot:after{color: #28a745;}.progress-wizard .progress-wizard-step.complete:hover:not(.disabled) .progress-wizard-steplabel{color: #212529;}.progress-wizard .progress-wizard-step.disabled{cursor: default;}

/* /sale/static/src/scss/sale_portal.scss defined in bundle 'web.assets_frontend' */
 .orders_vertical_align{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.orders_label_text_align{vertical-align: 15%;}

/* /sale/static/src/scss/product_configurator.scss defined in bundle 'web.assets_frontend' */
 .css_attribute_color{display: inline-block; border: 1px solid #999999; text-align: center;}.css_attribute_color input{margin: 8px; height: 13px; opacity: 0;}.css_attribute_color.active{border: 3px ridge #66ee66;}.css_attribute_color.active input{margin: 6px;}.css_attribute_color.custom_value{background-image: linear-gradient(to bottom right, #FF0000, #FFF200, #1E9600);}.css_not_available_msg{display: none;}.css_not_available.js_product .css_quantity, .css_not_available.js_product .product_price{display: none;}.css_not_available.js_product .css_not_available_msg{display: block;}.css_not_available.js_product .js_add, .css_not_available.js_product .oe_price, .css_not_available.js_product .oe_default_price, .css_not_available.js_product .oe_optional{display: none;}.css_quantity{max-width: 125px;}.css_quantity input[name="add_qty"]{text-align: center;}option.css_not_available{color: #ccc;}label.css_not_available{opacity: 0.6;}label.css_attribute_color.css_not_available{opacity: 1; background-image: url("/website_sale/static/src/img/redcross.png"); background-size: cover;}.variant_attribute{padding-bottom: 0.5rem;}.variant_attribute .attribute_name{padding-bottom: 0.5rem; display: block;}.variant_attribute .radio_input{margin-right: 0.7rem; vertical-align: middle;}.variant_attribute .radio_input_value{display: inline-block; vertical-align: middle; line-height: 1;}.variant_attribute .variant_custom_value{margin-bottom: 0.7rem;}.variant_attribute .variant_custom_value.custom_value_own_line{display: inline-block;}.variant_attribute .custom_value_radio{margin: 0.3rem 0rem 0.3rem 1.6rem;}.variant_attribute select{margin-bottom: 0.5rem;}.o_product_configurator .product_detail_img{max-height: 240px;}.o_product_configurator .variant_attribute .custom_value_radio{margin: 0.3rem 0rem 0.3rem 2.1rem;}.oe_optional_products_modal .table-striped tbody tr:nth-of-type(odd){background-color: rgba(0, 0, 0, 0.025);}.oe_optional_products_modal .o_total_row{font-size: 1.2rem;}.modal.o_technical_modal .oe_optional_products_modal .btn.js_add_cart_json{padding: 0.075rem 0.75rem;}.js_product.in_cart .js_add_cart_variants{display: none;}.js_product select{-webkit--webkit-appearance: menulist; -moz-appearance: menulist; appearance: menulist; -moz--webkit-appearance: menulist; -moz-appearance: menulist; appearance: menulist; -webkit-appearance: menulist; -moz-appearance: menulist; appearance: menulist; background-image: none;}.js_product .td-product_name{word-wrap: break-word;}.js_product .td-product_name{min-width: 140px;}.js_product .td-img{width: 100px;}.js_product .td-qty{width: 200px;}.js_product .td-qty a.input-group-addon{background-color: transparent; border: 0px;}.js_product .td-qty .input-group{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;}.js_product .td-action{width: 30px;}.js_product .td-price, .js_product .td-price-total{width: 120px;}@media (max-width: 767.98px){.js_product .td-img, .js_product .td-price-total{display: none;}.js_product .td-qty{width: 60px;}.js_product .td-price{width: 80px;}}@media (max-width: 476px){.js_product .td-qty{width: 60px;}.js_product #modal_optional_products table thead, .js_product .oe_cart table thead{display: none;}.js_product #modal_optional_products table td.td-img, .js_product .oe_cart table td.td-img{display: none;}}.o_total_row{height: 50px;}.oe_striked_price{text-decoration: line-through; white-space: nowrap;}.o_list_view .o_data_row.o_selected_row > .o_data_cell:not(.o_readonly_modifier) .o_field_widget .o_edit_product_configuration{padding: 0; background-color: inherit; margin-left: 3px;}

/* /website_sale_available_stock/static/src/css/website_sale_available.css defined in bundle 'web.assets_frontend' */
table#cart_products tr.warning input.js_quantity{background-color: #faa;}

/* /website_rating/static/src/scss/website_rating.scss defined in bundle 'web.assets_frontend' */
 .o_website_rating_static{color: #FACC2E;}.o_website_rating_card_container{}.o_website_rating_card_container .o_message_counter{color: #495057;}.o_website_rating_card_container table.o_website_rating_progress_table{width: 100%; overflow: visible;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_star_num{min-width: 50px;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_select[style*="opacity: 1"]{cursor: pointer;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_progress{min-width: 120px;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_progress > .progress{margin-bottom: 5px; margin-left: 5px; margin-right: 5px;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_progress .o_rating_progressbar{background-color: #FACC2E;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_percent{text-align: right; padding-left: 5px; font-size: 0.75rem;}.o_website_rating_card_container table.o_website_rating_progress_table .o_website_rating_table_reset .o_website_rating_select_text{visibility: hidden;}.o_rating_star_card{margin-bottom: 5px;}.o_rating_star_card .stars{display: inline-block; color: #FACC2E; margin-right: 15px;}.o_rating_star_card .stars i{margin-right: -3px; text-align: center;}.o_rating_star_card .stars.enabled{cursor: pointer;}.o_rating_star_card .rate_text{display: inline-block;}.o_rating_popup_composer .o_rating_clickable{cursor: pointer;}.o_rating_popup_composer .o_portal_chatter_avatar{margin-right: 10px;}.o_rating_popup_composer_label{color: #212529;}

/* /website_form_map/static/src/css/leaflet.css defined in bundle 'web.assets_frontend' */
 .leaflet-pane, .leaflet-tile, .leaflet-marker-icon, .leaflet-marker-shadow, .leaflet-tile-container, .leaflet-pane > svg, .leaflet-pane > canvas, .leaflet-zoom-box, .leaflet-image-layer, .leaflet-layer{position: absolute; left: 0; top: 0;}.leaflet-container{overflow: hidden;}.leaflet-tile, .leaflet-marker-icon, .leaflet-marker-shadow{-webkit-user-select: none; -moz-user-select: none; user-select: none; -webkit-user-drag: none;}.leaflet-tile::selection{background: transparent;}.leaflet-safari .leaflet-tile{image-rendering: -webkit-optimize-contrast;}.leaflet-safari .leaflet-tile-container{width: 1600px; height: 1600px; -webkit-transform-origin: 0 0;}.leaflet-marker-icon, .leaflet-marker-shadow{display: block;}.leaflet-container .leaflet-overlay-pane svg, .leaflet-container .leaflet-marker-pane img, .leaflet-container .leaflet-shadow-pane img, .leaflet-container .leaflet-tile-pane img, .leaflet-container img.leaflet-image-layer, .leaflet-container .leaflet-tile{max-width: none !important; max-height: none !important;}.leaflet-container.leaflet-touch-zoom{-ms-touch-action: pan-x pan-y; touch-action: pan-x pan-y;}.leaflet-container.leaflet-touch-drag{-ms-touch-action: pinch-zoom; touch-action: none; touch-action: pinch-zoom;}.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom{-ms-touch-action: none; touch-action: none;}.leaflet-container{-webkit-tap-highlight-color: transparent;}.leaflet-container a{-webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);}.leaflet-tile{filter: inherit; visibility: hidden;}.leaflet-tile-loaded{visibility: inherit;}.leaflet-zoom-box{width: 0; height: 0; -moz-box-sizing: border-box; box-sizing: border-box; z-index: 800;}.leaflet-overlay-pane svg{-moz-user-select: none;}.leaflet-pane{z-index: 400;}.leaflet-tile-pane{z-index: 200;}.leaflet-overlay-pane{z-index: 400;}.leaflet-shadow-pane{z-index: 500;}.leaflet-marker-pane{z-index: 600;}.leaflet-tooltip-pane{z-index: 650;}.leaflet-popup-pane{z-index: 700;}.leaflet-map-pane canvas{z-index: 100;}.leaflet-map-pane svg{z-index: 200;}.leaflet-vml-shape{width: 1px; height: 1px;}.lvml{behavior: url(/website_form_map/static/src/css/#default#VML); display: inline-block; position: absolute;}.leaflet-control{position: relative; z-index: 800; pointer-events: visiblePainted; pointer-events: auto;}.leaflet-top, .leaflet-bottom{position: absolute; z-index: 1000; pointer-events: none;}.leaflet-top{top: 0;}.leaflet-right{right: 0;}.leaflet-bottom{bottom: 0;}.leaflet-left{left: 0;}.leaflet-control{float: left; clear: both;}.leaflet-right .leaflet-control{float: right;}.leaflet-top .leaflet-control{margin-top: 10px;}.leaflet-bottom .leaflet-control{margin-bottom: 10px;}.leaflet-left .leaflet-control{margin-left: 10px;}.leaflet-right .leaflet-control{margin-right: 10px;}.leaflet-fade-anim .leaflet-tile{will-change: opacity;}.leaflet-fade-anim .leaflet-popup{opacity: 0; -webkit-transition: opacity 0.2s linear; -moz-transition: opacity 0.2s linear; transition: opacity 0.2s linear;}.leaflet-fade-anim .leaflet-map-pane .leaflet-popup{opacity: 1;}.leaflet-zoom-animated{-webkit-transform-origin: 0 0; -ms-transform-origin: 0 0; transform-origin: 0 0;}.leaflet-zoom-anim .leaflet-zoom-animated{will-change: transform;}.leaflet-zoom-anim .leaflet-zoom-animated{-webkit-transition: -webkit-transform 0.25s cubic-bezier(0,0,0.25,1); -moz-transition: -moz-transform 0.25s cubic-bezier(0,0,0.25,1); transition: transform 0.25s cubic-bezier(0,0,0.25,1);}.leaflet-zoom-anim .leaflet-tile, .leaflet-pan-anim .leaflet-tile{-webkit-transition: none; -moz-transition: none; transition: none;}.leaflet-zoom-anim .leaflet-zoom-hide{visibility: hidden;}.leaflet-interactive{cursor: pointer;}.leaflet-grab{cursor: -webkit-grab; cursor: -moz-grab; cursor: grab;}.leaflet-crosshair, .leaflet-crosshair .leaflet-interactive{cursor: crosshair;}.leaflet-popup-pane, .leaflet-control{cursor: auto;}.leaflet-dragging .leaflet-grab, .leaflet-dragging .leaflet-grab .leaflet-interactive, .leaflet-dragging .leaflet-marker-draggable{cursor: move; cursor: -webkit-grabbing; cursor: -moz-grabbing; cursor: grabbing;}.leaflet-marker-icon, .leaflet-marker-shadow, .leaflet-image-layer, .leaflet-pane > svg path, .leaflet-tile-container{pointer-events: none;}.leaflet-marker-icon.leaflet-interactive, .leaflet-image-layer.leaflet-interactive, .leaflet-pane > svg path.leaflet-interactive, svg.leaflet-image-layer.leaflet-interactive path{pointer-events: visiblePainted; pointer-events: auto;}.leaflet-container{background: #ddd; outline: 0;}.leaflet-container a{color: #0078A8;}.leaflet-container a.leaflet-active{outline: 2px solid orange;}.leaflet-zoom-box{border: 2px dotted #38f; background: rgba(255,255,255,0.5);}.leaflet-container{font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;}.leaflet-bar{box-shadow: 0 1px 5px rgba(0,0,0,0.65); border-radius: 4px;}.leaflet-bar a, .leaflet-bar a:hover{background-color: #fff; border-bottom: 1px solid #ccc; width: 26px; height: 26px; line-height: 26px; display: block; text-align: center; text-decoration: none; color: black;}.leaflet-bar a, .leaflet-control-layers-toggle{background-position: 50% 50%; background-repeat: no-repeat; display: block;}.leaflet-bar a:hover{background-color: #f4f4f4;}.leaflet-bar a:first-child{border-top-left-radius: 4px; border-top-right-radius: 4px;}.leaflet-bar a:last-child{border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; border-bottom: none;}.leaflet-bar a.leaflet-disabled{cursor: default; background-color: #f4f4f4; color: #bbb;}.leaflet-touch .leaflet-bar a{width: 30px; height: 30px; line-height: 30px;}.leaflet-touch .leaflet-bar a:first-child{border-top-left-radius: 2px; border-top-right-radius: 2px;}.leaflet-touch .leaflet-bar a:last-child{border-bottom-left-radius: 2px; border-bottom-right-radius: 2px;}.leaflet-control-zoom-in, .leaflet-control-zoom-out{font: bold 18px 'Lucida Console', Monaco, monospace; text-indent: 1px;}.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out{font-size: 22px;}.leaflet-control-layers{box-shadow: 0 1px 5px rgba(0,0,0,0.4); background: #fff; border-radius: 5px;}.leaflet-control-layers-toggle{background-image: url(/website_form_map/static/src/css/images/layers.png); width: 36px; height: 36px;}.leaflet-retina .leaflet-control-layers-toggle{background-image: url(/website_form_map/static/src/css/images/layers-2x.png); background-size: 26px 26px;}.leaflet-touch .leaflet-control-layers-toggle{width: 44px; height: 44px;}.leaflet-control-layers .leaflet-control-layers-list, .leaflet-control-layers-expanded .leaflet-control-layers-toggle{display: none;}.leaflet-control-layers-expanded .leaflet-control-layers-list{display: block; position: relative;}.leaflet-control-layers-expanded{padding: 6px 10px 6px 6px; color: #333; background: #fff;}.leaflet-control-layers-scrollbar{overflow-y: scroll; overflow-x: hidden; padding-right: 5px;}.leaflet-control-layers-selector{margin-top: 2px; position: relative; top: 1px;}.leaflet-control-layers label{display: block;}.leaflet-control-layers-separator{height: 0; border-top: 1px solid #ddd; margin: 5px -10px 5px -6px;}.leaflet-default-icon-path{background-image: url(/website_form_map/static/src/css/images/marker-icon.png);}.leaflet-container .leaflet-control-attribution{background: #fff; background: rgba(255, 255, 255, 0.7); margin: 0;}.leaflet-control-attribution, .leaflet-control-scale-line{padding: 0 5px; color: #333;}.leaflet-control-attribution a{text-decoration: none;}.leaflet-control-attribution a:hover{text-decoration: underline;}.leaflet-container .leaflet-control-attribution, .leaflet-container .leaflet-control-scale{font-size: 11px;}.leaflet-left .leaflet-control-scale{margin-left: 5px;}.leaflet-bottom .leaflet-control-scale{margin-bottom: 5px;}.leaflet-control-scale-line{border: 2px solid #777; border-top: none; line-height: 1.1; padding: 2px 5px 1px; font-size: 11px; white-space: nowrap; overflow: hidden; -moz-box-sizing: border-box; box-sizing: border-box; background: #fff; background: rgba(255, 255, 255, 0.5);}.leaflet-control-scale-line:not(:first-child){border-top: 2px solid #777; border-bottom: none; margin-top: -2px;}.leaflet-control-scale-line:not(:first-child):not(:last-child){border-bottom: 2px solid #777;}.leaflet-touch .leaflet-control-attribution, .leaflet-touch .leaflet-control-layers, .leaflet-touch .leaflet-bar{box-shadow: none;}.leaflet-touch .leaflet-control-layers, .leaflet-touch .leaflet-bar{border: 2px solid rgba(0,0,0,0.2); background-clip: padding-box;}.leaflet-popup{position: absolute; text-align: center; margin-bottom: 20px;}.leaflet-popup-content-wrapper{padding: 1px; text-align: left; border-radius: 12px;}.leaflet-popup-content{margin: 13px 19px; line-height: 1.4;}.leaflet-popup-content p{margin: 18px 0;}.leaflet-popup-tip-container{width: 40px; height: 20px; position: absolute; left: 50%; margin-left: -20px; overflow: hidden; pointer-events: none;}.leaflet-popup-tip{width: 17px; height: 17px; padding: 1px; margin: -10px auto 0; -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); -ms-transform: rotate(45deg); transform: rotate(45deg);}.leaflet-popup-content-wrapper, .leaflet-popup-tip{background: white; color: #333; box-shadow: 0 3px 14px rgba(0,0,0,0.4);}.leaflet-container a.leaflet-popup-close-button{position: absolute; top: 0; right: 0; padding: 4px 4px 0 0; border: none; text-align: center; width: 18px; height: 14px; font: 16px/14px Tahoma, Verdana, sans-serif; color: #c3c3c3; text-decoration: none; font-weight: bold; background: transparent;}.leaflet-container a.leaflet-popup-close-button:hover{color: #999;}.leaflet-popup-scrolled{overflow: auto; border-bottom: 1px solid #ddd; border-top: 1px solid #ddd;}.leaflet-oldie .leaflet-popup-content-wrapper{zoom: 1;}.leaflet-oldie .leaflet-popup-tip{width: 24px; margin: 0 auto; -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)"; filter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678);}.leaflet-oldie .leaflet-popup-tip-container{margin-top: -1px;}.leaflet-oldie .leaflet-control-zoom, .leaflet-oldie .leaflet-control-layers, .leaflet-oldie .leaflet-popup-content-wrapper, .leaflet-oldie .leaflet-popup-tip{border: 1px solid #999;}.leaflet-div-icon{background: #fff; border: 1px solid #666;}.leaflet-tooltip{position: absolute; padding: 6px; background-color: #fff; border: 1px solid #fff; border-radius: 3px; color: #222; white-space: nowrap; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; pointer-events: none; box-shadow: 0 1px 3px rgba(0,0,0,0.4);}.leaflet-tooltip.leaflet-clickable{cursor: pointer; pointer-events: auto;}.leaflet-tooltip-top:before, .leaflet-tooltip-bottom:before, .leaflet-tooltip-left:before, .leaflet-tooltip-right:before{position: absolute; pointer-events: none; border: 6px solid transparent; background: transparent; content: "";}.leaflet-tooltip-bottom{margin-top: 6px;}.leaflet-tooltip-top{margin-top: -6px;}.leaflet-tooltip-bottom:before, .leaflet-tooltip-top:before{left: 50%; margin-left: -6px;}.leaflet-tooltip-top:before{bottom: 0; margin-bottom: -12px; border-top-color: #fff;}.leaflet-tooltip-bottom:before{top: 0; margin-top: -12px; margin-left: -6px; border-bottom-color: #fff;}.leaflet-tooltip-left{margin-left: -6px;}.leaflet-tooltip-right{margin-left: 6px;}.leaflet-tooltip-left:before, .leaflet-tooltip-right:before{top: 50%; margin-top: -6px;}.leaflet-tooltip-left:before{right: 0; margin-right: -12px; border-left-color: #fff;}.leaflet-tooltip-right:before{left: 0; margin-left: -12px; border-right-color: #fff;}

/* /payment/static/src/scss/portal_payment.scss defined in bundle 'web.assets_frontend' */
 input#cc_number{background-repeat: no-repeat; background-position: center right calc(2.7em);}div.card_placeholder{background-image: url("/website_payment/static/src/img/placeholder.png"); background-repeat: no-repeat; width: 32px; height: 20px; position: absolute; top: 8px; right: 20px; -webkit-transition: 0.4s cubic-bezier(0.455, 0.03, 0.515, 0.955); transition: 0.4s cubic-bezier(0.455, 0.03, 0.515, 0.955); pointer-events: none;}div.o_card_brand_detail{position: relative;}div.o_card_brand_detail div.card_placeholder{right: 5px;}div.amex{background-image: url("/website_payment/static/src/img/amex.png"); background-repeat: no-repeat;}div.diners{background-image: url("/website_payment/static/src/img/diners.png"); background-repeat: no-repeat;}div.discover{background-image: url("/website_payment/static/src/img/discover.png"); background-repeat: no-repeat;}div.jcb{background-image: url("/website_payment/static/src/img/jcb.png"); background-repeat: no-repeat;}div.mastercard{background-image: url("/website_payment/static/src/img/mastercard.png"); background-repeat: no-repeat;}div.visa{background-image: url("/website_payment/static/src/img/visa.png"); background-repeat: no-repeat;}ul.payment_method_list img.rounded{max-width: 100px; max-height: 40px;}

/* /payment/static/src/scss/payment_form.scss defined in bundle 'web.assets_frontend' */
 .o_payment_form label > input[type="radio"], .o_payment_form input[type="checkbox"]{vertical-align: middle; margin-right: 5px;}.o_payment_form .payment_option_name{font-size: 14px; font-weight: normal !important; font-family: Helvetica Neue, sans-serif; line-height: 1.3em; color: #4d4d4d;}.o_payment_form label{font-weight: normal; margin-top: 5px;}.o_payment_form .card-body:first-child{border-top: 0px;}.o_payment_form .card{border-radius: 10px;}.o_payment_form .card-footer:last-child{border-bottom-right-radius: 10px !important; border-bottom-left-radius: 10px !important;}.o_payment_form .card-body{border-top: 1px solid #ddd; padding: 1.14em !important;}.o_payment_form .card-body.o_payment_acquirer_select:hover{cursor: pointer;}.o_payment_form .payment_icon_list{position: relative; margin-top: 0px !important; margin-bottom: -5px !important;}.o_payment_form .payment_icon_list li{padding-left: 5px !important; padding-right: 0px !important;}.o_payment_form .payment_icon_list .more_option{position: absolute; top: auto; left: auto; bottom: auto; right: 10px; font-size: 10px;}

/* /sale/static/src/scss/sale_portal.scss defined in bundle 'web.assets_frontend' */
 .orders_vertical_align{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.orders_label_text_align{vertical-align: 15%;}