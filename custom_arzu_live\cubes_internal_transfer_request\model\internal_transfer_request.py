from odoo import api, fields, models, _


class StockPickingTypeAlternative(models.Model):
    _name = 'stock.type.alternative'

    name = fields.Char()
    picking_type_id = fields.Integer()


class InternalTransferRequest(models.Model):
    _name = 'internal.transfer.request'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Internal Transfer Request'

    name = fields.Char(string='Name', default='/', copy=False)
    date = fields.Date(required=True, default=fields.Date.context_today)
    partner_id = fields.Many2one('res.partner', string='Contact')
    source_picking_type_id = fields.Many2one('stock.picking.type', string='Source Operation Type', copy=False,
                                             domain=lambda self: [('code', '=', 'internal'), (
                                             'id', 'in', self.env.user.default_picking_type_ids.ids)], )

    destination_picking_type_id = fields.Many2one('stock.picking.type', string='Destination Operation Type',
                                                  copy=False)
    destination_type_id = fields.Many2one('stock.type.alternative', string='Destination Operation Type',
                                          copy=False)
    line_ids = fields.One2many('internal.transfer.line', 'internal_transfer_id')
    state = fields.Selection([('draft', 'Draft'), ('confirmed', 'Confirmed')], string='Status', default='draft',
                             copy=False)
    first_picking_id = fields.Many2one('stock.picking', string='First Move', copy=False)
    second_picking_id = fields.Many2one('stock.picking', string='Second Move', copy=False)
    third_picking_id = fields.Many2one('stock.picking', string='Third Move', copy=False)
    company_id = fields.Many2one('res.company', default=lambda self: self.env.company, copy=False)


    @api.onchange('source_picking_type_id')
    def _check_destination_picking_type_id(self):
        for rec in self:
            all_picking_types = self.env['stock.picking.type'].sudo().search([('code', '=', 'internal')])
            all_types = self.env['stock.type.alternative'].search(
                [('picking_type_id', 'not in', all_picking_types.ids)])
            all_types.sudo().unlink()
            for picking_type in all_picking_types:
                alternatve_type = self.env['stock.type.alternative'].search(
                    [('picking_type_id', '=', picking_type.id)])
                if not alternatve_type:
                    self.env['stock.type.alternative'].sudo().create(
                        {'name': picking_type.warehouse_id.name + ':' + picking_type.name,
                         'picking_type_id': picking_type.id})

    def action_confirm(self):
        for rec in self:
            rec.state = 'confirmed'
            # create first picking which type internal
            rec.first_picking_id = self.env['stock.picking'].sudo().create({
                'location_id': rec.source_picking_type_id.default_location_src_id.id,
                'location_dest_id': rec.source_picking_type_id.default_location_dest_id.id,
                'partner_id': rec.partner_id.id if rec.partner_id else False,
                'company_id':self.env.company.id,
                'picking_type_id': rec.source_picking_type_id.id,
                'first_move': True,
                'internal_request_id': rec.id
            })

            for line in rec.line_ids:
                self.env['stock.move'].create({
                    'name': line.product_id.name,
                    'product_id': line.product_id.id,
                    'product_uom_qty': line.qty,
                    'quantity': line.qty,
                    'picking_id': rec.first_picking_id.id,
                    'product_uom': line.product_uom.id,
                    'location_id': rec.first_picking_id.location_id.id,
                    'location_dest_id': rec.first_picking_id.location_dest_id.id,
                })
            # rec.first_picking_id.sudo().action_confirm()
            # rec.first_picking_id.sudo().button_validate()


    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('internal.transfer.sequence')
        return super(InternalTransferRequest, self).create(vals)


class InternalTransferLine(models.Model):
    _name = 'internal.transfer.line'

    internal_transfer_id = fields.Many2one('internal.transfer.request')
    product_id = fields.Many2one('product.product', string='Product')
    qty = fields.Float(string='Demand')
    product_uom = fields.Many2one('uom.uom', string='UOM')

    @api.onchange('product_id')
    def _get_uom_of_product(self):
        for rec in self:
            if rec.product_id and rec.product_id.uom_id:
                rec.product_uom = rec.product_id.uom_id.id
