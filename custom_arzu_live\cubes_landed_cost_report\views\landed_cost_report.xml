<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="action_report_landed_cost" model="ir.actions.report">
        <field name="name">Landed Cost Report</field>
        <field name="model">stock.landed.cost</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">cubes_landed_cost_report.report_landed_cost</field>
        <field name="report_file">cubes_landed_cost_report.report_landed_cost</field>
        <field name="print_report_name">('Landed Cost Of - %s' % (object.name))</field>
        <field name="binding_model_id" ref="stock_landed_costs.model_stock_landed_cost"/>
        <field name="binding_type">report</field>
    </record>
    <template id="report_landed_cost">
        <t t-call="cubes_landed_cost_report.report_landed_cost_raw"/>
    </template>
    <template id="report_landed_cost_raw">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="cubes_landed_cost_report.report_landed_cost_document"/>
            </t>
        </t>
    </template>
    <template id="report_landed_cost_document">
        <t t-call="web.external_layout">
            <div class="page">
                <div class="oe_structure"/>
                <h2 class="mt-4">Landed Cost Report</h2>

                <div class="row mt-4 mb-2" id="informations">
                    <div class="col-auto col-3 mw-100 mb-2" name="informations_date">
                        <strong>Date:</strong>
                        <br/>
                        <span class="m-0" t-field="doc.date" t-options='{"widget": "date"}'/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2" name="informations journal">
                        <strong>Journal:</strong>
                        <br/>
                        <span class="m-0" t-field="doc.account_journal_id.name"/>
                    </div>
                    <div class="col-auto col-3 mw-100 mb-2"
                         name="informations transfers">
                        <strong>Transfers:</strong>
                        <br/>
                        <span class="m-0" t-field="doc.picking_ids"/>
                    </div>
                </div>

                <div class="clearfix">
                    <p>
                        <strong>
                            Additional Costs
                        </strong>
                    </p>
                </div>
                <div class="oe_structure"/>
                <table class="table table-sm o_main_table table-borderless mt-4">
                    <thead style="display: table-row-group">
                        <tr>
                            <th name="th_product" class="text-start">Product</th>
                            <th name="th_description" class="text-start">Description</th>
                            <th name="th_account" class="text-end">Account</th>
                            <th name="th_split_method" class="text-end">Split Method</th>
                            <th name="th_cost" class="text-end">Cost</th>
                        </tr>
                    </thead>
                    <tbody class="landed_cost_tbody">

                        <t t-foreach="doc.cost_lines" t-as="line">


                            <tr t-att-class="'bg-200 fw-bold'">
                                <td name="td_product">
                                    <span t-field="line.product_id.name"/>
                                </td>
                                <td name="td_description">
                                    <span t-field="line.name"/>
                                </td>
                                <td name="td_account">
                                    <span t-field="line.account_id.name"/>
                                </td>
                                <td name="td_split_method">
                                    <span t-field="line.split_method"/>
                                </td>
                                <td name="td_price_unit">
                                    <span t-field="line.price_unit"/>
                                </td>
                            </tr>

                        </t>
                    </tbody>
                </table>


                <div class="clearfix">
                    <p>
                        <strong>
                           Valuation Adjustments
                        </strong>
                    </p>
                </div>
                <br/>
                <br/>
                <div class="oe_structure"/>
                <table class="table table-sm o_main_table table-borderless mt-4">
                    <thead style="display: table-row-group">
                        <tr>
                            <th name="th_cost_line" class="text-start">Cost Line</th>
                            <th name="th_product" class="text-start">Product</th>
                            <th name="th_weight" class="text-start">Weight</th>
                            <th name="th_volume" class="text-start">Volume</th>
                            <th name="th_quantity" class="text-start">Quantity</th>
                            <th name="th_original_value" class="text-start">Original Value</th>
                            <th name="th_new_value" class="text-start">New Value</th>
                            <th name="th_additional_landed_cost" class="text-start">Additional Landed Cost</th>
                        </tr>
                    </thead>
                    <tbody class="landed_cost_tbody">

                        <t t-foreach="doc.valuation_adjustment_lines" t-as="adjustment_line">


                            <tr>
                                <td name="td_cost_line">
                                    <span t-field="adjustment_line.cost_line_id.name"/>
                                </td>
                                <td name="td_product">
                                    <span t-field="adjustment_line.product_id.name"/>
                                </td>
                                <td name="td_weight">
                                    <span t-field="adjustment_line.weight"/>
                                </td>
                                <td name="td_volume">
                                    <span t-field="adjustment_line.volume"/>
                                </td>
                                <td name="td_quantity">
                                    <span t-field="adjustment_line.quantity"/>
                                </td>
                                <td name="td_original_value">
                                    <span t-field="adjustment_line.former_cost"/>
                                </td>
                                <td name="td_new_value">
                                    <span t-field="adjustment_line.final_cost"/>
                                </td>
                                <td name="td_additional_landed_cost">
                                    <span t-field="adjustment_line.additional_landed_cost"/>
                                </td>
                            </tr>

                        </t>
                    </tbody>
                </table>

            </div>
        </t>
    </template>


</odoo>

