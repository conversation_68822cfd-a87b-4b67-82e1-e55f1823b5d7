<odoo>

    <!-- Menu Root -->
    <menuitem id="menu_skyex_master" name="SkyEx Master" parent="skyexpress_menu" sequence="10"/>

    <!-- ====== COUNTRY ====== -->
    <record id="view_skyex_country_tree" model="ir.ui.view">
        <field name="name">skyex.country.tree</field>
        <field name="model">skyex.country</field>
        <field name="arch" type="xml">
            <tree>
                <field name="api_id"/>
                <field name="name"/>
                <field name="code"/>
            </tree>
        </field>
    </record>

    <record id="view_skyex_country_form" model="ir.ui.view">
        <field name="name">skyex.country.form</field>
        <field name="model">skyex.country</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="api_id"/>
                    <field name="name"/>
                    <field name="code"/>
                </group>
            </form>
        </field>
    </record>

    <record id="action_skyex_country" model="ir.actions.act_window">
        <field name="name">Countries</field>
        <field name="res_model">skyex.country</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_skyex_country"
              name="Countries"
              parent="skyexpress_menu"
              action="action_skyex_country"
              sequence="1"/>


    <!-- ====== ZONES ====== -->
    <record id="view_skyex_zones_tree" model="ir.ui.view">
        <field name="name">skyex.zones.tree</field>
        <field name="model">skyex.zones</field>
        <field name="arch" type="xml">
            <tree>
                <field name="api_id"/>
                <field name="name"/>
                <field name="code"/>
            </tree>
        </field>
    </record>

    <record id="view_skyex_zones_form" model="ir.ui.view">
        <field name="name">skyex.zones.form</field>
        <field name="model">skyex.zones</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="api_id"/>
                    <field name="name"/>
                    <field name="code"/>
                </group>
            </form>
        </field>
    </record>

    <record id="action_skyex_zones" model="ir.actions.act_window">
        <field name="name">Zones</field>
        <field name="res_model">skyex.zones</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_skyex_zones"
              name="Zones"
              parent="skyexpress_menu"
              action="action_skyex_zones"
              sequence="2"/>


    <!-- ====== SUBZONES ====== -->
    <record id="view_skyex_subzones_tree" model="ir.ui.view">
        <field name="name">skyex.subzones.tree</field>
        <field name="model">skyex.subzones</field>
        <field name="arch" type="xml">
            <tree>
                <field name="api_id"/>
                <field name="name"/>
                <field name="code"/>
                <field name="zone_id"/>
            </tree>
        </field>
    </record>

    <record id="view_skyex_subzones_form" model="ir.ui.view">
        <field name="name">skyex.subzones.form</field>
        <field name="model">skyex.subzones</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="api_id"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="zone_id"/>
                </group>
            </form>
        </field>
    </record>

    <record id="action_skyex_subzones" model="ir.actions.act_window">
        <field name="name">Subzones</field>
        <field name="res_model">skyex.subzones</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_skyex_subzones"
              name="Subzones"
              parent="skyexpress_menu"
              action="action_skyex_subzones"
              sequence="3"/>


    <!-- ====== SHIPMENT TYPES ====== -->
    <record id="view_skyex_shipment_types_tree" model="ir.ui.view">
        <field name="name">skyex.shipment.types.tree</field>
        <field name="model">skyex.shipment.types</field>
        <field name="arch" type="xml">
            <tree>
                <field name="api_id"/>
                <field name="name"/>
                <field name="code"/>
            </tree>
        </field>
    </record>

    <record id="view_skyex_shipment_types_form" model="ir.ui.view">
        <field name="name">skyex.shipment.types.form</field>
        <field name="model">skyex.shipment.types</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <field name="api_id"/>
                    <field name="name"/>
                    <field name="code"/>
                </group>
            </form>
        </field>
    </record>

    <record id="action_skyex_shipment_types" model="ir.actions.act_window">
        <field name="name">Shipment Types</field>
        <field name="res_model">skyex.shipment.types</field>
        <field name="view_mode">tree,form</field>
    </record>

    <!--<menuitem id="menu_skyex_shipment_types"
              name="Shipment Types"
              parent="skyexpress_menu"
              action="action_skyex_shipment_types"
              sequence="4"/>-->

</odoo>
