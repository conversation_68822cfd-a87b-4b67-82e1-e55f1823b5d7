# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_quantity_history_location
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-12-09 21:33+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__include_child_locations
msgid "Include Child Locations"
msgstr "Incluir Localizaciones Infantiles"

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__location_id
msgid "Location"
msgstr "Localización"

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quant
msgid "Quants"
msgstr ""

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historial de Cantidades de Existencias"

#, python-format
#~ msgid "Inventory at Date"
#~ msgstr "Inventario a Fecha"

#, python-format
#~ msgid "Inventory at Date & Location"
#~ msgstr "Inventario en Fecha y lugar"
