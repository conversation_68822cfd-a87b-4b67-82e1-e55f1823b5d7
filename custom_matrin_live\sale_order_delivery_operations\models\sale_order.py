from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    delivery_company_partner_id = fields.Many2one('res.partner', string='Delivery Company', store=True)
    phone = fields.Char(string='Phone Number', related='partner_id.phone', store=True)
    street = fields.Char(string='Client Address', related='partner_id.street', store=True)
    
    def _prepare_invoice(self):
        res = super(SaleOrder, self)._prepare_invoice()
        if self.delivery_company_partner_id:
            res['partner_id'] = self.delivery_company_partner_id.id
        return res
    
    
    
    
