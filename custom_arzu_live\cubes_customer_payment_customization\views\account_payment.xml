<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_payment_form_view_inherit_customer" model="ir.ui.view">
        <field name="name">account.payment.form.view.inherit.customer</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_customer_due"
                        type="object"
                        class="oe_stat_button"
                        icon="fa-money"
                        invisible="partner_type != 'customer' or is_internal_transfer">
                    <field name="customer_due_amount"
                           widget="statinfo"
                           string="Customer Due"/>
                </button>
            </xpath>
            <xpath position="after" expr="//field[@name='amount']">
                <field name="payment_difference" invisible="1"/>
            </xpath>
            <xpath expr="//group[@name='group2']" position="after">
                <group name="group3"
                       invisible="payment_difference == 0.0 or partner_type != 'customer' or is_internal_transfer or state == 'posted'">
                    <label for="payment_difference"/>
                    <div>
                        <field name="payment_difference"/>
                        <field name="payment_difference_handling" widget="radio" nolabel="1"/>
                        <div invisible="payment_difference_handling == 'open'">
                            <label for="writeoff_account_id" string="Post Difference In" class="oe_edit_only"/>
                            <field name="writeoff_account_id" string="Post Difference In" options="{'no_create': True}"
                                   required="payment_difference_handling == 'reconcile'"/>
                            <label for="writeoff_label" class="oe_edit_only" string="Label"/>
                            <field name="writeoff_label" required="payment_difference_handling == 'reconcile'"/>
                        </div>
                    </div>
                </group>
            </xpath>
        </field>
    </record>
</odoo>
