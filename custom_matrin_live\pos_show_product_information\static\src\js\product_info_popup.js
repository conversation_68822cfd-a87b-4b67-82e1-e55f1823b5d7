/** @odoo-module */
import {patch} from "@web/core/utils/patch";
import { ProductInfoPopup } from "@point_of_sale/app/screens/product_screen/product_info_popup/product_info_popup";


patch(ProductInfoPopup.prototype, {
    setup() {
        super.setup(...arguments);
        const posConfig = this.pos.config;

        this.show_financial = posConfig.show_financial;
        this.show_order = posConfig.show_order;

    },
});

