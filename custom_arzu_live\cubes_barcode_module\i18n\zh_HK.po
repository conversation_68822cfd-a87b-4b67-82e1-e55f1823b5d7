# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cubes_barcode_module
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-12-10 07:02+0000\n"
"PO-Revision-Date: 2020-12-10 07:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cubes_barcode_module
#: model:ir.actions.report,print_report_name:cubes_barcode_module.sh_barcode_report_action
msgid "'Product Dynamic Barcode Label'"
msgstr "'产品动态条码标签'"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__after
msgid "After"
msgstr "后"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__barcode
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_barcode_report_doc
msgid "Barcode"
msgstr "条码"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_field_id
msgid "Barcode Field"
msgstr "条码领域"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_height
msgid "Barcode Height"
msgstr "条形码高度"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__quantity
msgid "Barcode Quantity"
msgstr "条形码数量"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_report_cubes_barcode_module_sh_barcode_report_doc
msgid "Barcode Report"
msgstr "条形码报告"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_width
msgid "Barcode Width"
msgstr "条形码宽度"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_print_barcode_or_qr
msgid "Barcode/QR Type"
msgstr "条形码/ QR型"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__before
msgid "Before"
msgstr "前"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Cancel"
msgstr "取消"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__center
msgid "Center"
msgstr "中心"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code128
msgid "Code 128"
msgstr "代码128."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code11
msgid "Code11"
msgstr "CODE11."

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__company_id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__company_id
msgid "Company"
msgstr "公司"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_uid
msgid "Created by"
msgstr "由...制作"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_date
msgid "Created on"
msgstr "创建于"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__currency_id
msgid "Currency"
msgstr "货币"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_currency_position
msgid "Currency Symbol Position"
msgstr "货币符号位置"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label"
msgstr "动态条形码标签"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label Line"
msgstr "动态条形码标签线"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__lable_id
msgid "Dynamic Label"
msgstr "动态标签"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template
msgid "Dynamic Label Template"
msgstr "动态标签模板"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_print_line_ids
msgid "Dynamic Lable Line"
msgstr "动态合约线"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print
msgid "Dynamic Lable Print"
msgstr "动态合理的印刷品"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print_line
msgid "Dynamic Lable Print Line"
msgstr "动态合理的打印线"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.account_move_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_product_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_template_action
#: model:ir.actions.act_window,name:cubes_barcode_module.purchase_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sale_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_action_product_label_wizard
#: model:ir.actions.act_window,name:cubes_barcode_module.stock_picking_action
msgid "Dynamic Product Label"
msgstr "动态产品标签"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_product_lable_template_action
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_lable_template_tree_view
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Product Label Template"
msgstr "动态产品标签模板"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean13
msgid "EAN-13"
msgstr "伊恩13."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean8
msgid "EAN-8"
msgstr "ean-8."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended39
msgid "Extended39"
msgstr "Extended39."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended93
msgid "Extended93"
msgstr "Extendet93."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__fim
msgid "FIM"
msgstr "FIM."

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_field_id
msgid "Field"
msgstr "场地"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__ttype
msgid "Field Type"
msgstr "字段类型"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_bold
msgid "Font Bold"
msgstr "字体大胆"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_color
msgid "Font Color"
msgstr "字体颜色"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_italic
msgid "Font Italic"
msgstr "字体斜体"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_size
msgid "Font Size"
msgstr "字体大小"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_underline
msgid "Font Underline"
msgstr "字体强调"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__i2of5
msgid "I2of5"
msgstr "I2OF5"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__id
msgid "ID"
msgstr "ID"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_label_display
msgid "Is Label Display ?"
msgstr "是标签显示吗？"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_is_price_field
msgid "Is Price Field ?"
msgstr "价格领域吗？"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__is_valid
msgid "Is Valid ?"
msgstr "已验证 ？"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_template_id
msgid "Label Print Template"
msgstr "标签打印模板"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__landscape
msgid "Landscape"
msgstr "风景"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line____last_update
msgid "Last Modified on"
msgstr "最后修改了"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_uid
msgid "Last Updated by"
msgstr "最后更新"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_date
msgid "Last Updated on"
msgstr "上次更新了"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__left
msgid "Left"
msgstr "剩下"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__msi
msgid "MSI"
msgstr "MSI"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_margin
msgid "Margin"
msgstr "利润"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__name
msgid "Name"
msgstr "姓名"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "PDF Page Configuration"
msgstr "PDF页面配置"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_height
msgid "PDF Page Height"
msgstr "PDF页面高度"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_width
msgid "PDF Page Width"
msgstr "PDF页面宽度"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__postnet
msgid "POSTNET"
msgstr "Postnet."

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_orientation
msgid "Page Orientation"
msgstr "页面方向"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__paperformat_id
msgid "Paper Format"
msgstr "纸张格式"

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid ""
"Please select fields which you have defined in dynamic barcode lines, If you"
" have not added pls add field first in dynamic barcode label lines."
msgstr ""
“请选择您在动态条形码行中定义的字段，如果您”
“尚未在动态条形码标签行中添加请先添加字段。”

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__portrait
msgid "Portrait"
msgstr "肖像"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_position
msgid "Position"
msgstr "位置"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Print"
msgstr "打印"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__product_id
msgid "Product"
msgstr "产品"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product & Barcode Quantity"
msgstr "产品和条形码数量"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_main
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_sub
msgid "Product Barcode Label"
msgstr "产品条形码标签"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template
#: model:res.groups,name:cubes_barcode_module.group_product_barcode_label_template
msgid "Product Barcode Label Template"
msgstr "产品条形码标签模板"

#. module: cubes_barcode_module
#: model:ir.actions.report,name:cubes_barcode_module.sh_barcode_report_action
msgid "Product Dynamic Barcode Label"
msgstr "产品动态条形码标签"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product Dynamic Label Print"
msgstr "产品动态标签打印"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__qr
msgid "QR"
msgstr "QR."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__right
msgid "Right"
msgstr "对"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Sequence"
msgstr "序列"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard39
msgid "Standard39"
msgstr "标准39."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard93
msgid "Standard93"
msgstr "标准93."

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__template_id
msgid "Template"
msgstr "模板"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_template_line_ids
msgid "Template Line"
msgstr "模板线"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template_line
msgid "Template Lines"
msgstr "模板线"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_type
msgid "Type of Barcode"
msgstr "条形码类型"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__upca
msgid "UPCA"
msgstr "UPCA."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__usps_4state
msgid "USPS_4State"
msgstr "USPS_4STATE."

#. module: cubes_barcode_module
#: model:ir.model.fields,help:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Used to handle lines."
msgstr "用来处理线条。"

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid "You can not Add Duplicate Fields"
msgstr "您无法添加重复的字段"
