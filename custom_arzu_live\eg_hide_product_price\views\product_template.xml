<odoo>
    <record model="ir.ui.view" id="product_template_hide_product_cost_price_form_view">
        <field name="name">product.template.hide.cost.price.form.view</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='categ_id']" position="before">
                <field name="hide_product_cost_price" invisible="1"/>
                <field name="hide_product_sale_price" invisible="1"/>
            </xpath>
            <xpath expr="//label[@for='list_price']" position="attributes">
                <attribute name="invisible">hide_product_sale_price == True or type != 'product'
                </attribute>
            </xpath>
            <xpath expr="//label[@for='standard_price']" position="attributes">
                <attribute name="invisible">hide_product_cost_price == True or type != 'product'
                </attribute>
            </xpath>
            <xpath expr="//field[@name='standard_price']" position="attributes">
                <attribute name="invisible">hide_product_cost_price == True or type != 'product'
                </attribute>
            </xpath>
            <xpath expr="//field[@name='list_price']" position="attributes">
                <attribute name="invisible">hide_product_sale_price ==  True or type !=
                    'product'
                </attribute>
            </xpath>
        </field>
    </record>


</odoo>