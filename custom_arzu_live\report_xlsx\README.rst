================
Base report xlsx
================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:eac09290cc88cd06dd772db4fc71b344b37f272b627cff3f30aded0eceedd3b1
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Mature-brightgreen.png
    :target: https://odoo-community.org/page/development-status
    :alt: Mature
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Freporting--engine-lightgray.png?logo=github
    :target: https://github.com/OCA/reporting-engine/tree/17.0/report_xlsx
    :alt: OCA/reporting-engine
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/reporting-engine-17-0/reporting-engine-17-0-report_xlsx
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/reporting-engine&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module provides a basic report class to generate xlsx report.

**Table of contents**

.. contents::
   :local:

Installation
============

Make sure you have ``xlsxwriter`` Python module installed:

::

   $ pip3 install xlsxwriter

For testing it is also necessary ``xlrd`` Python module installed:

::

   $ pip3 install xlrd

Usage
=====

An example of XLSX report for partners on a module called
\`module_name\`:

A python class :

::

   from odoo import models

   class PartnerXlsx(models.AbstractModel):
       _name = 'report.module_name.report_name'
       _inherit = 'report.report_xlsx.abstract'

       def generate_xlsx_report(self, workbook, data, partners):
           for obj in partners:
               report_name = obj.name
               # One sheet by partner
               sheet = workbook.add_worksheet(report_name[:31])
               bold = workbook.add_format({'bold': True})
               sheet.write(0, 0, obj.name, bold)

To manipulate the ``workbook`` and ``sheet`` objects, refer to the
`documentation <http://xlsxwriter.readthedocs.org/>`__ of
``xlsxwriter``.

A report XML record :

::

   <report
       id="partner_xlsx"
       model="res.partner"
       string="Print to XLSX"
       report_type="xlsx"
       name="module_name.report_name"
       file="res_partner"
       attachment_use="False"
   />

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/reporting-engine/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/reporting-engine/issues/new?body=module:%20report_xlsx%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* ACSONE SA/NV
* Creu Blanca

Contributors
------------

-  Adrien Peiffer <<EMAIL>>
-  Sébastien Alix <<EMAIL>>
-  Stéphane Bidoul <<EMAIL>>
-  Enric Tobella <<EMAIL>>
-  Graeme Gellatly <<EMAIL>>
-  Cristian Salamea <<EMAIL>>
-  Rod Schouteden <<EMAIL>>
-  Eugene Molotov <<EMAIL>>
-  Christopher Ormaza <<EMAIL>>
-  Houzéfa Abbasbhay <<EMAIL>>
-  Le Dinh Tien <<EMAIL>>

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/reporting-engine <https://github.com/OCA/reporting-engine/tree/17.0/report_xlsx>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
