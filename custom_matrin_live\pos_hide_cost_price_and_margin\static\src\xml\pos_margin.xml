<?xml version="1.0" encoding="UTF-8" ?>
<!--
Copyright (C) 2022 - Today: camptocamp (https://www.camptocamp.com)
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<templates id="template" xml:space="preserve">

    <t t-inherit="point_of_sale.ProductInfoPopup" t-inherit-mode="extension">
        <xpath
            expr="//table/tr/td/t[@t-esc='costCurrency']/../.."
            position="attributes"
        >
            <attribute name="t-if">env.pos.config.iface_display_margin</attribute>
        </xpath>
        <xpath
            expr="//table/tr/td/t[@t-esc='marginCurrency']/../.."
            position="attributes"
        >
            <attribute name="t-if">env.pos.config.iface_display_margin</attribute>
        </xpath>
        <xpath
            expr="//table/tr/td[@t-esc='orderCostCurrency']/.."
            position="attributes"
        >
            <attribute name="t-if">env.pos.config.iface_display_margin</attribute>
        </xpath>
        <xpath
            expr="//table/tr/td/t[@t-esc='orderMarginCurrency']/../.."
            position="attributes"
        >
            <attribute name="t-if">env.pos.config.iface_display_margin</attribute>
        </xpath>
        <xpath expr="//div[hasclass('section-supplier')]" position="attributes">
            <attribute name="t-if">env.pos.config.iface_display_margin</attribute>
        </xpath>
    </t>

</templates>
