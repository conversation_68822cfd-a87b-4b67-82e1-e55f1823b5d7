<?xml version="1.0" encoding="UTF-8"?>
<odoo>
<!--    Set up the pos configuration    -->
    <record id="restrict_user" model="ir.rule">
        <field name="name">Config User</field>
        <field name="model_id" ref="point_of_sale.model_pos_config"/>
        <field name="domain_force">[('id','in',user.allowed_pos.ids)]</field>
        <field name="groups" eval="[(4,ref('point_of_sale.group_pos_user'))]"/>
    </record>
    <record id="restrict_manager" model="ir.rule">
        <field name="name">Config Manager</field>
        <field name="model_id" ref="point_of_sale.model_pos_config"/>
        <field name="domain_force">[]</field>
        <field name="groups" eval="[(4,ref('point_of_sale.group_pos_manager'))]"/>
    </record>
    <record id="order_user" model="ir.rule">
        <field name="name">Orders User</field>
        <field name="model_id" ref="point_of_sale.model_pos_order"/>
        <field name="domain_force">[('config_id','in',user.allowed_pos.ids)]</field>
        <field name="groups" eval="[(4,ref('point_of_sale.group_pos_user'))]"/>
    </record>
    <record id="order_manager" model="ir.rule">
        <field name="name">Orders Manager</field>
        <field name="model_id" ref="point_of_sale.model_pos_order"/>
        <field name="domain_force">[]</field>
        <field name="groups" eval="[(4,ref('point_of_sale.group_pos_manager'))]"/>
    </record>
</odoo>
