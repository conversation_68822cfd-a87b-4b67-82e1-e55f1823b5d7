# -*- coding: utf-8 -*-

from odoo import api, fields, models

class ProductLabelLayout(models.TransientModel):
    _inherit = 'product.label.layout'

    print_format = fields.Selection(selection_add=[
        ('elgahad_label', 'Elgahad Label'),
        ('elgahad_label_price', 'Elgahad Label with Price')
    ], ondelete={'elgahad_label': 'set default', 'elgahad_label_price': 'set default'}, insert_before=['dymo'])

    def _prepare_report_data(self):
        if self.print_format in ['elgahad_label', 'elgahad_label_price']:
            xml_id = 'elgahad_inventory.action_report_product_label' if self.print_format == 'elgahad_label' else 'elgahad_inventory.action_report_product_label_price'
            
            if self.product_ids:
                products = self.product_ids
            elif self.product_tmpl_ids:
                products = self.product_tmpl_ids.mapped('product_variant_ids')
            else:
                products = self.env['product.product'].browse()

            # Prepare the quantity by product dictionary
            quantity_by_product = {str(p.id): self.custom_quantity for p in products}
            
            # Prepare price by product dictionary based on selected pricelist
            price_by_product = {}
            if self.print_format == 'elgahad_label_price':
                for product in products:
                    if self.pricelist_id:
                        # Get price from selected pricelist
                        price = self.pricelist_id._get_product_price(product, 1.0)
                    else:
                        # Fallback to product's list price
                        price = product.lst_price
                    price_by_product[str(product.id)] = price
            
            data = {
                'quantity_by_product': quantity_by_product,
                'price_by_product': price_by_product,
                'pricelist_id': self.pricelist_id.id if self.pricelist_id else False,
                'layout_wizard': self.id,
            }
            
            return xml_id, data
        return super()._prepare_report_data() 