<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="skyexpress_config_form" model="ir.ui.view">
            <field name="name">Sky Express Setting</field>
            <field name="model">sky.express.configuration</field>
            <field name="arch" type="xml">
                <form string="_form">
                    <header>
                        <button type="object" name="authenticate_user" string="Get Authentication"/>
                        <button type="object" name="get_countries" string="Get Countries"/>
<!--                        <button type="object" name="get_shipments" string="Get Shipments"/>-->
<!--                        <button type="object" name="get_countries" string="Get Countries"/>-->
                        <button type="object" name="get_zones" string="Get Zones"/>
<!--                        <button type="object" name="get_services" string="Get Services"/>-->
                        <button icon="fa-align-justify"
                                name="%(subzone_action)d"
                                string="Get Sub Zone" type="action"/>
<!--                        <button type="object" name="tree_shipments" string="tree Shipments"/>-->
<!--                        <button type="object" name="get_shipment" string="Get Shipments"/>-->
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="url"/>
                                <field name="username"/>
                                <field name="password"/>
                                <field name="token"/>
                                <field name="access_token"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="skyexpress_setting_action" model="ir.actions.act_window">
            <field name="name">Sky Express</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sky.express.configuration</field>
            <field name="view_mode">tree,form</field>
        </record>

        <!-- This Menu Item must have a parent and an action -->
        <menuitem id="skyexpress_menu" name="Sky Express Setting" action="skyexpress_setting_action" sequence="1"/>




    </data>
</odoo>