# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_quantity_history_location
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2020-09-29 11:00+0000\n"
"Last-Translator: erik-bzcl <<EMAIL>>\n"
"Language-Team: none\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < "
"20)) ? 1 : 2;\n"
"X-Generator: Weblate 3.10\n"

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__include_child_locations
msgid "Include Child Locations"
msgstr ""

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__location_id
msgid "Location"
msgstr "Locaţia"

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quant
msgid "Quants"
msgstr ""

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Stoc cantitativ istoric"

#, python-format
#~ msgid "Inventory at Date & Location"
#~ msgstr "Stoc în data & locaţia"

#~ msgid "Include child locations"
#~ msgstr "Inclusiv sublocaţii"
