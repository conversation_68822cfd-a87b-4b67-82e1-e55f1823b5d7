<?xml version="1.0" encoding="utf-8"?>
<odoo>
<!--    Created new record rule for created user group-->
    <record id="account_journal_rule_users" model="ir.rule">
        <field name="name">Account Journal Restrict on Users</field>
        <field name="model_id" ref="account.model_account_journal"/>
<!--        <field name="domain_force">[('id','not in', user.journal_ids.ids)]-->
<!--        </field>-->
        <field name="groups"
               eval="[(4, ref('account_restrict_journal.account_restrict_journal_group_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="active" eval="True"/>
    </record>
    <record id="account_payment_rule_users" model="ir.rule">
        <field name="name">Account Payment Restrict on Users</field>
        <field name="model_id" ref="account.model_account_payment"/>
<!--        <field name="domain_force">[('journal_id','not in', user.journal_ids.ids)]-->
<!--        </field>-->
        <field name="groups"
               eval="[(4, ref('account_restrict_journal.account_restrict_journal_group_admin'))]"/>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="True"/>
        <field name="active" eval="True"/>
    </record>
</odoo>
