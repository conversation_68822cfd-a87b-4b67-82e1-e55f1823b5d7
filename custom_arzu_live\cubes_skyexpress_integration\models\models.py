from odoo import fields, models
import requests
import json
from odoo.exceptions import ValidationError


class SkyExpressConfiguration(models.Model):
    _name = 'sky.express.configuration'

    name = fields.Char(string='Name')
    username = fields.Char(string='User Name')
    password = fields.Char(string='Password')
    token = fields.Char(string='Token')
    url = fields.Char(string='Url')
    access_token = fields.Char(string='Access Token')

    # =============================
    # AUTHENTICATION
    # =============================
    def authenticate_user(self):
        mutation = """
        mutation Login($input: LoginInput!) {
          login(input: $input) {
            token
            user {
              id
              username
            }
          }
        }
        """

        variables = {
            "input": {
                "username": self.username,
                "password": self.password,
                "rememberMe": True,
                "fcmToken": self.token
            }
        }

        headers = {"Content-Type": "application/json"}

        response = requests.post(self.url, json={"query": mutation, "variables": variables}, headers=headers)
        if response.status_code == 200 and response.json().get("data", {}).get("login", {}).get("token"):
            self.access_token = response.json()['data']['login']['token']
        else:
            raise ValidationError("Authentication failed: %s" % response.json())

    # =============================
    # GET COUNTRIES
    # =============================
    def get_countries(self):
        query = """
        query {
          listCountriesDropdown {
            id
            code
            name
          }
        }
        """

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }
        response = requests.post(self.url, json={"query": query}, headers=headers)
        data = response.json()
        for line in data['data']['listCountriesDropdown']:
            self.env['skyex.country'].create({
                'api_id': line.get('id'),
                'name': line.get('name'),
                'code': line.get('code'),
            })
        # print(json.dumps(data, indent=4))
        return data.get('data', {}).get('listCountriesDropdown', [])

    # =============================
    # GET ZONES BY COUNTRY
    # =============================
    def get_zones(self):
        query = """
        query ($input: ListZonesFilterInput) {
          listZonesDropdown(input: $input) {
            id
            code
            name
          }
        }
        """

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }
        variables = {"countryId": 137}
        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()
        for line in data['data']['listZonesDropdown']:
            self.env['skyex.zones'].create({
                'api_id': line.get('id'),
                'name': line.get('name'),
                'code': line.get('code'),
            })
        return data.get('data', {}).get('listZonesDropdown', [])

    def get_services(self):
        query = """query ($input: ListShippingServicesFilterInput) {
                      listShippingServicesDropdown(input: $input) {
                        id
                        code
                        name
                      }
                    }

                    """
        variables = {
            "input": {
                "active": True
            }
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }
        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()
        for line in data['data']['listZonesDropdown']:
            self.env['skyex.shipment.types'].create({
                'api_id': line.get('id'),
                'name': line.get('name'),
                'code': line.get('code'),
            })
        return data.get("data", {}).get("listShipmentTypesDropdown", [])

    # =============================
    # CREATE SHIPMENT
    # =============================
    def get_shipments(self):

        query = """
        query ($id: Int, $code: String) {
          shipment(id: $id, code: $code) {
            id
            code
            date
            createdAt
            updatedAt
            deliveryDate
            deliveredOrReturnedDate
            description
            weight
            piecesCount
            collected
            paidToCustomer
            paidToDeliveryAgent
            editable
            trackingUrl
            deletable
            hasProducts
            inWarehouse
            unpacked
            refNumber
            notes
            adminNotes
            returnPiecesCount
            service {
                  id
                  code
                  name
                  days
                  egsCode
                  active
                }
            type {
              id
              code
              name
              active
              weight
              mobileActive
            }
            status {
              id
              code
              name
              active
              weight
              mobileActive
            }
            senderZone {
                      id
                      code
                      name
                      active
                    }
                senderSubzone {
                      id
                      code
                      name
                      active
                    }
                
                 recipientZone {
                      id
                      code
                      name
                      active
                    }
                recipientSubzone {
                  id
                  code
                  name
                  active
                }
                
                recipientAddress
                recipientPostalCode
                recipientLatitude
                recipientLongitude
                senderName
                senderPhone
                senderMobile
          }
        }
        """

        variables = {
            "id": 314782,
            "code": "SKYX014604"
        }

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }

        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        print(response.json())

    def get_subzones(self):
        query = """
        query ($input: ListZonesFilterInput) {
            listZones(input: $input) {
                data {
                    id
                    code
                    name
                }
            }
        }
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }

        variables = {"input": {"countryId": 1}}  # Adjust "countryId" as per your actual input filter

        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()
        print(json.dumps(data, indent=4))

        zones = data.get("data", {}).get("listZones", {}).get("data", [])
        for zone in zones:
            if zone["id"] == 1:
                return zone  # or zone.get("subzones", [])
        return []

    def list_shipments(self):
        """
        Fetch one page of shipments and pretty-print the whole response.
        :param dict  filters:  ListShipmentsFilterInput or None
        :param int   first:    Page size (1-100)
        :param int   page:     Page number (starts at 1)
        :return dict           Same structure returned by GraphQL
        """

        query = """
        query ($input: ListShipmentsFilterInput, $first: Int!, $page: Int) {
          listShipments(input: $input, first: $first, page: $page) {
            paginatorInfo {
              count currentPage firstItem hasMorePages lastItem
              lastPage perPage total
            }
            data {
              id
              code
              date
              createdAt
              updatedAt
              deliveryDate
              deliveredOrReturnedDate
              description
              weight
              piecesCount
              collected
              paidToCustomer
              paidToDeliveryAgent
              editable
              trackingUrl
              deletable
              hasProducts
              inWarehouse
              unpacked
              refNumber
              notes
              adminNotes
              returnPiecesCount
              attempts
              cancellable
              cancelled
              price
              amount
              deliveryFees
              extraWeightFees
              collectionFees
              totalAmount
              deliveredAmount
              returningDueFees
              returnedValue
              allDueFees
              collectedFees
              customerDue
              collectedAmount
              pendingCollectionAmount
              returnFees
              postFees
              tax
              deliveryCommission
              recipientName
              recipientPhone
              recipientMobile
              recipientAddress
              recipientPostalCode
              recipientLatitude
              recipientLongitude
              senderName
              senderPhone
              senderMobile
              senderAddress
              senderPostalCode
              senderLatitude
              senderLongitude
              deliveryOtp
              hasDeliveryOtp
            }
          }
        }
        """
        variables = {"input": 1 or None, "first": 1, "page": 10}
        # result = self._post(query, variables)["listShipments"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }

        # variables = {"input": {"countryId": 1}}  # Adjust "countryId" as per your actual input filter

        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()

        # ---- print the entire response in readable JSON ----
        # print(json.dumps(result, indent=4, ensure_ascii=False))
        #
        # return result

    def get_shipment(self):

        query = """
            query ($id: Int, $code: String) {
                  shipment(id: $id, code: $code) {
                    id
                    code
                    type {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    openable {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    date
                    createdAt
                    updatedAt
                    deliveryDate
                    deliveredOrReturnedDate
                    description
                    weight
                    piecesCount
                    collected
                    paidToCustomer
                    paidToDeliveryAgent
                    editable
                    trackingUrl
                    deletable
                    hasProducts
                    inWarehouse
                    unpacked
                    refNumber
                    notes
                    adminNotes
                    returnPiecesCount
                    paymentType {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    priceType {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    deliveryType {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    returnType {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    transactionType {
                      id
                      code
                      name
                      active
                    }
                    branch {
                      id
                      code
                      name
                      active
                      main
                      address
                      phone
                      fax
                    }
                    originBranch {
                      id
                      code
                      name
                      active
                      main
                      address
                      phone
                      fax
                    }
                    status {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    returnStatus {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    service {
                      id
                      code
                      name
                      days
                      egsCode
                      active
                    }
                    history {
                      id
                      code
                      date
                      createdAt
                      updatedAt
                      deliveryDate
                      deliveredOrReturnedDate
                      description
                      weight
                      piecesCount
                      collected
                      paidToCustomer
                      paidToDeliveryAgent
                      editable
                      trackingUrl
                      deletable
                      hasProducts
                      inWarehouse
                      unpacked
                      refNumber
                      notes
                      adminNotes
                      returnPiecesCount
                      attempts
                      cancellable
                      cancelled
                      price
                      amount
                      deliveryFees
                      extraWeightFees
                      collectionFees
                      totalAmount
                      deliveredAmount
                      returningDueFees
                      returnedValue
                      allDueFees
                      collectedFees
                      customerDue
                      collectedAmount
                      pendingCollectionAmount
                      returnFees
                      postFees
                      tax
                      deliveryCommission
                      recipientName
                      recipientPhone
                      recipientMobile
                      recipientAddress
                      recipientPostalCode
                      recipientLatitude
                      recipientLongitude
                      senderName
                      senderPhone
                      senderMobile
                      senderAddress
                      senderPostalCode
                      senderLatitude
                      senderLongitude
                      deliveryOtp
                      hasDeliveryOtp
                    }
                    createdBy {
                      id
                      username
                      active
                      isSuper
                    }
                    updatedBy {
                      id
                      username
                      active
                      isSuper
                    }
                    warehouseSection {
                      id
                      name
                      active
                    }
                    customerType {
                      id
                      code
                      name
                      active
                      weight
                      mobileActive
                    }
                    attempts
                    cancellable
                    cancelled
                    price
                    amount
                    deliveryFees
                    extraWeightFees
                    collectionFees
                    totalAmount
                    deliveredAmount
                    returningDueFees
                    returnedValue
                    allDueFees
                    collectedFees
                    customerDue
                    collectedAmount
                    pendingCollectionAmount
                    returnFees
                    postFees
                    tax
                    deliveryCommission
                    recipientName
                    recipientPhone
                    recipientMobile
                    recipientZone {
                      id
                      code
                      name
                      active
                    }
                    recipientSubzone {
                      id
                      code
                      name
                      active
                    }
                    recipientAddress
                    recipientPostalCode
                    recipientLatitude
                    recipientLongitude
                    senderName
                    senderPhone
                    senderMobile
                    senderZone {
                      id
                      code
                      name
                      active
                    }
                    senderSubzone {
                      id
                      code
                      name
                      active
                    }
                    senderAddress
                    senderPostalCode
                    senderLatitude
                    senderLongitude
                    deliveryOtp
                    hasDeliveryOtp
                    images {
                      path
                      subjectCode
                    }
                  }
                }
        """

        variables = {
            "code": "SKYX014604"
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": self.access_token
        }

        response = requests.post(self.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()
