<?xml version="1.0" encoding="UTF-8" ?>
<!--
Copyright (C) 2016-Today: GRAP (http://www.grap.coop)
Copyright (C) 2016-Today La Louve (http://www.lalouve.net)
<AUTHOR> LE GAL (https://twitter.com/legalsylvain)
License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl.html).
-->
<odoo>
    <record id="generate_barcode" model="res.groups">
        <field name="name">Generate Barcodes</field>
        <field
            name="users"
            eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"
        />
    </record>
</odoo>
