import requests

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class StockPicking(models.Model):
    _inherit = 'stock.picking'


    country_id = fields.Many2one(
        comodel_name='skyex.country',
        string='Country',
        default= lambda rec: [('id', '=', rec.env['skyex.country'].search([('code', '=', 'LY')]).id)]
    )
    zone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Sender Zone'
    )
    sender_subzone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Sender Sub Zone'
    )
    sub_zone_id = fields.Many2one(
            comodel_name='skyex.subzones',
            string='Sender Sub Zone',
        )

    receiption_zone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Destination Zone'
    )
    receiption_subzone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Destination Sub Zone'
    )
    receiption_sub_zone_id = fields.Many2one(
        comodel_name='skyex.subzones',
        string='Destination Sub Zone',
    )

    shipment_number = fields.Char(string="Shipment Number")
    shipment_id = fields.Char(string="Shipment ID", readonly=True, store=True)
    shipment_status = fields.Selection([
        ('driver', 'Driver'),
        ('delivered', 'Delivered')
    ], string="Shipment Status")
    shipment_type = fields.Selection([
        ('FDP', 'FDP'),
        ('PDP', 'PDP'),
        ('PTP', 'PTP'),
        ('RTS', 'RTS'),
        ('CLC', 'CLC'),
    ], default='FDP')

    subzon_domain = fields.Char()
    receiption_subzon_domain = fields.Char()

    shipment_url_link = fields.Char(string="Tracking URL")
    delivery_agent = fields.Char(string="Delivery Agent")
    delivery_fees = fields.Float(string="Delivery Fees")
    shipment_driver = fields.Many2one('res.partner', string="Driver")
    pieces = fields.Integer(string="Total Pieces")
    warehouse_phone = fields.Char(string="Warehouse Phone", compute="_compute_values", store=True, readonly=False)
    warehouse_address = fields.Char(string="Warehouse Address", compute="_compute_values", store=True, readonly=False)
    destination_street = fields.Char(string="Destination Street")
    destination_city = fields.Char(string="Destination City")
    destination_mobile = fields.Char(string="Destination Mobile")
    consignee_name = fields.Char(string="Consignee Name")
    package_amount = fields.Float(string="Package Amount", compute="_compute_values", store=True, readonly=False)

    service_type = fields.Selection([
        ('1', 'شحن عادي :1'),
        ('3', 'شحن نسائي :3'),
        ('4', 'شحن سريع :4'),
    ], default='1', string="Shipment Status")

    origin = fields.Char(
        'Source Document', index='trigram',
        help="Reference of the document",)


    @api.model
    def create(self, vals_list):
        res = super().create(vals_list)
        if res:
            if res.picking_type_id.code == 'outgoing':
                sale = self.env['sale.order'].search([('name', '=', res.origin)])
                if sale:
                    res.receiption_zone_id = sale.receiption_zone_id.id
                    res.receiption_sub_zone_id = sale.receiption_sub_zone_id.id
                    res.package_amount = sale.amount_total
                    country = self.country_id.search([('code', '=', 'LY')], limit=1)
                    if country:
                        res.country_id = country.id
        return res

    @api.depends('origin')
    def _compute_values(self):
        for line in self:
            if line.location_id:
                line.warehouse_phone = line.location_id.warehouse_id.warehouse_phone or ''
                line.warehouse_address = line.location_id.warehouse_id.warehouse_address or ''
                line.zone_id = line.location_id.warehouse_id.zone_id.id
                line.sub_zone_id = line.location_id.warehouse_id.sub_zone_id.id

    @api.onchange('zone_id')
    def onchange_zone_id(self):
        if self.zone_id:
            self.subzon_domain = [('id', 'in', self.sub_zone_id.search([('zone_id', '=', self.zone_id.id)]).ids)]
        else:
            self.subzon_domain = [('id', 'in', [])]

    @api.onchange('receiption_zone_id')
    def onchange_receiption_zone_id(self):
        if self.receiption_zone_id:
            self.receiption_subzon_domain = [('id', 'in', self.sub_zone_id.search([('zone_id', '=', self.receiption_zone_id.id)]).ids)]
        else:
            self.receiption_subzon_domain = [('id', 'in', [])]

    def create_shipment(self):
        config = self.env['sky.express.configuration'].search([], limit=1)
        # self.get_shipments(shipping_id=314782, code='SKYX014604')
        weight = sum((rec.product_id.weight * rec.quantity) for rec in self.move_ids_without_package)
        if self.zone_id and self.receiption_zone_id and self.country_id:

            if config:
                query = """
                        mutation CreateShipment($input: ShipmentInput!) {
                            saveShipment(input: $input) {
                                id
                                code
                                refNumber
                            }
                        }
                        """
                price = self.env['sale.order'].search([('name', '=', self.origin)]).amount_total if self.origin else  1
                variables = {
                    "input": {
                        "serviceId": int(self.service_type),
                        "senderName": "Arzu",
                        "senderZoneId": int(self.zone_id.api_id),
                        "senderSubzoneId": int(self.sub_zone_id.api_id),
                        "senderPhone": self.warehouse_phone if self.warehouse_phone else "+966501234567",
                        "senderMobile": self.warehouse_phone if self.warehouse_phone else "+966501234567",
                        "senderAddress": self.warehouse_address if self.warehouse_address else self.sub_zone_id.name,
                        "senderPostalCode": "12345",
                        "recipientName": self.partner_id.name,
                        "recipientZoneId": int(self.receiption_zone_id.api_id),
                        "recipientSubzoneId": int(self.receiption_sub_zone_id.api_id),
                        # "recipientSubzoneId": 129,
                        "recipientPhone": f'+{self.partner_id.phone}' if self.partner_id.phone else "+966501234567",
                        "recipientMobile": f'+{self.partner_id.mobile}' if self.partner_id.mobile else "+966501234567",
                        "recipientAddress": f'{self.partner_id.street}, {self.partner_id.street2}, {self.partner_id.city}',
                        "piecesCount": sum(rec.quantity for rec in self.move_ids_without_package),
                        "weight": 20,
                        "price": price,
                        "paymentTypeCode": "COLC",
                        "priceTypeCode": "EXCLD",
                        "openableCode": "Y",
                        "typeCode": str(self.shipment_type)
                    }
                }

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f'Bearer {config.access_token}'
                }

                response = requests.post(config.url, json={"query": query, "variables": variables}, headers=headers)
                data = response.json()
                if 'data' in data:
                    if 'saveShipment' in data['data']:
                        if data['data']['saveShipment']:
                            self.get_shipments(shipping_id=data['data']['saveShipment']['id'], code=data['data']['saveShipment']['code'])
                        else:
                            if 'errors' in data:
                                for key, values in data['errors'][0]['extensions']['validation'].items():
                                    msg = values[0]
                                    raise ValidationError(f'{msg}')
                if 'errors' in data:
                    msg = data['errors'][0]
                    raise ValidationError(msg)

    def update_all_skyex_deliveries(self):
        for line in self.search([('shipment_number', '!=', False)]):
            self.get_shipments(shipping_id=line.shipment_id,
                               code=line.shipment_number)

    def get_shipments(self, shipping_id=None, code=None):
        config = self.env['sky.express.configuration'].search([], limit=1)
        query = """
                    query ($id: Int, $code: String) {
                          shipment(id: $id, code: $code) {
                            id
                            code
                            type {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            openable {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            date
                            createdAt
                            updatedAt
                            deliveryDate
                            deliveredOrReturnedDate
                            description
                            weight
                            piecesCount
                            collected
                            paidToCustomer
                            paidToDeliveryAgent
                            editable
                            trackingUrl
                            deletable
                            hasProducts
                            inWarehouse
                            unpacked
                            refNumber
                            notes
                            adminNotes
                            returnPiecesCount
                            paymentType {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            priceType {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            deliveryType {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            returnType {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            transactionType {
                              id
                              code
                              name
                              active
                            }
                            branch {
                              id
                              code
                              name
                              active
                              main
                              address
                              phone
                              fax
                            }
                            originBranch {
                              id
                              code
                              name
                              active
                              main
                              address
                              phone
                              fax
                            }
                            status {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            returnStatus {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            service {
                              id
                              code
                              name
                              days
                              egsCode
                              active
                            }
                     
                            createdBy {
                              id
                              username
                              active
                              isSuper
                            }
                            updatedBy {
                              id
                              username
                              active
                              isSuper
                            }
                            warehouseSection {
                              id
                              name
                              active
                            }
                            customerType {
                              id
                              code
                              name
                              active
                              weight
                              mobileActive
                            }
                            attempts
                            cancellable
                            cancelled
                            price
                            amount
                            deliveryFees
                            extraWeightFees
                            collectionFees
                            totalAmount
                            deliveredAmount
                            returningDueFees
                            returnedValue
                            allDueFees
                            collectedFees
                            customerDue
                            collectedAmount
                            pendingCollectionAmount
                            returnFees
                            postFees
                            tax
                            deliveryCommission
                            recipientName
                            recipientPhone
                            recipientMobile
                            recipientZone {
                              id
                              code
                              name
                              active
                            }
                            recipientSubzone {
                              id
                              code
                              name
                              active
                            }
                            recipientAddress
                            recipientPostalCode
                            recipientLatitude
                            recipientLongitude
                            senderName
                            senderPhone
                            senderMobile
                            senderZone {
                              id
                              code
                              name
                              active
                            }
                            senderSubzone {
                              id
                              code
                              name
                              active
                            }
                            senderAddress
                            senderPostalCode
                            senderLatitude
                            senderLongitude
                            deliveryOtp
                            hasDeliveryOtp
                            images {
                              path
                              subjectCode
                            }
                          }
                        }
                """

        variables = {
            "id": int(shipping_id),
            "code": code
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {config.access_token}"
        }

        response = requests.post(config.url, json={"query": query, "variables": variables}, headers=headers)
        data = response.json()
        if 'errors' in data:
            pass
        else:

            data = data['data']['shipment']
            if data.get('status')['name'] == 'Delivered':
                status = 'delivered'
            else:
                status = 'driver'
            self.shipment_id = data.get('id')
            self.shipment_number = data.get('code')
            self.shipment_status = status
            self.delivery_fees = data.get('deliveryFees')
            self.pieces = data.get('piecesCount')
            self.shipment_url_link = data.get('trackingUrl')






