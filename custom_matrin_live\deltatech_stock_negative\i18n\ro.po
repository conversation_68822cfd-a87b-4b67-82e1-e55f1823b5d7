# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * deltatech_stock_negative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-24 03:17+0000\n"
"PO-Revision-Date: 2019-03-24 03:17+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: deltatech_stock_negative
#: model_terms:ir.ui.view,arch_db:deltatech_stock_negative.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Valorile stabilite aici sunt "
"specifice companiei.\" aria-label=\"Valorile stabilite aici sunt specifice "
"companiei.\" groups=\"base.group_multi_company\" role=\"img\"/>"

#. module: deltatech_stock_negative
#: model:ir.model.fields,field_description:deltatech_stock_negative.field_stock_location__allow_negative_stock
msgid "Allow Negative Stock"
msgstr "Permite stoc negativ"

#. module: deltatech_stock_negative
#: model:ir.model.fields,help:deltatech_stock_negative.field_res_company__no_negative_stock
#: model:ir.model.fields,help:deltatech_stock_negative.field_res_config_settings__no_negative_stock
msgid "Allows you to prohibit negative stock quantities."
msgstr "Da posibilitatea de a interzice lucrul cu stocuri negative"

#. module: deltatech_stock_negative
#: model:ir.model,name:deltatech_stock_negative.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: deltatech_stock_negative
#: model:ir.model,name:deltatech_stock_negative.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: deltatech_stock_negative
#: model:ir.model,name:deltatech_stock_negative.model_stock_location
msgid "Inventory Locations"
msgstr "Locații stoc"

#. module: deltatech_stock_negative
#: model:ir.model.fields,field_description:deltatech_stock_negative.field_res_company__no_negative_stock
#: model:ir.model.fields,field_description:deltatech_stock_negative.field_res_config_settings__no_negative_stock
msgid "No negative stock"
msgstr "Fară stoc negativ"

#. module: deltatech_stock_negative
#: model_terms:ir.ui.view,arch_db:deltatech_stock_negative.res_config_settings_view_form
msgid "No negative stocks are allowed"
msgstr "Fară stoc negativ"

#. module: deltatech_stock_negative
#: model:ir.model,name:deltatech_stock_negative.model_stock_quant
msgid "Quants"
msgstr "Poziții de stoc"

#. module: deltatech_stock_negative
#: code:addons/deltatech_stock_negative/models/stock.py:22
#, python-format
msgid ""
"You have chosen to avoid negative stock. %s pieces of %s are remaining in "
"location %s, but you want to transfer %s pieces. Please adjust your "
"quantities or correct your stock with an inventory adjustment."
msgstr ""
"Ați ales să evitați stocurile negative.   %s din %s există în locația %s dar"
" dvs vreți să transferați %s. Ajustați cantitățile sau corectați stocul cu o"
" ajustare de inventar."

#. module: deltatech_stock_negative
#: code:addons/deltatech_stock_negative/models/stock.py:0
#, python-format
msgid ""
"You have chosen to avoid negative stock. %s pieces of %s are remaining in "
"location %s, lot %s, but you want to transfer %s pieces. Please adjust your "
"quantities or correct your stock with an inventory adjustment."
msgstr ""
"Ați ales să evitați stocurile negative.   %s din %s există în locația %s, "
"lot %s dar dvs vreți să transferați %s. Ajustați cantitățile sau corectați "
"stocul cu o ajustare de inventar."
