<odoo>
    <data>
        <record id="skyex_status_update_crone" model="ir.cron">
            <field name="name">Update Skyex Status</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False" />
            <field name="model_id" ref="model_stock_picking" />
            <field name="code">model.update_all_skyex_deliveries()</field>
        </record>
<record id="view_picking_form_custom" model="ir.ui.view">
    <field name="name">stock.picking.form.skyex</field>
    <field name="model">stock.picking</field>
    <field name="inherit_id" ref="stock.view_picking_form"/>
    <field name="arch" type="xml">


        <xpath expr="//field[@name='origin']" position="after">
            <field name="country_id"/>
            <field name="zone_id"/>
            <field name="subzon_domain" invisible="1"/>
            <field name="sub_zone_id"/>
            <field name="receiption_zone_id"/>
            <field name="receiption_subzon_domain" invisible="1"/>
            <field name="receiption_sub_zone_id" domain="receiption_subzon_domain"/>
            <field name="shipment_type"/>
            <field name="service_type"/>
            <button type="object" string="Skyex" name="create_shipment"/>
        </xpath>
        <xpath expr="//notebook" position="inside">
            <page string="SkyEx Shipment">
                <group string="SkyEx Delivery Info" col="4">
                    <field name="shipment_number"/>
                    <field name="shipment_id" />
                    <field name="shipment_status"/>
                    <field name="shipment_driver"/>
                    <field name="delivery_agent"/>
                    <field name="delivery_fees"/>
                    <field name="shipment_url_link"/>
                </group>
                <group string="Shipment Details" col="4">
                    <field name="pieces"/>
                    <field name="package_amount"/>
                </group>
                <group string="Warehouse Info" col="4">
                    <field name="warehouse_phone"/>
                    <field name="warehouse_address"/>
                </group>
                <group string="Destination Info" col="4">
                    <field name="consignee_name"/>
                    <field name="destination_street"/>
                    <field name="destination_city"/>
                    <field name="destination_mobile"/>
                </group>
            </page>
        </xpath>
    </field>
</record>

    </data>
</odoo>
