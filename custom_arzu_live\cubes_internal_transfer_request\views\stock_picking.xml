<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="internal_transfer_request_tree_view" model="ir.ui.view">
        <field name="name">internal.transfer.request.tree.view</field>
        <field name="model">internal.transfer.request</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date"/>
                <field name="partner_id"/>
                <field name="source_picking_type_id"/>
                <field name="destination_type_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <record id="internal_transfer_request_form_view" model="ir.ui.view">
        <field name="name">internal.transfer.request.form.view</field>
        <field name="model">internal.transfer.request</field>
        <field name="arch" type="xml">
            <form string="Internal Transfer Request">
                <header>
                    <button name="action_confirm"
                            invisible="state !='draft'"
                            string="Confirm"
                            type="object"
                            class="oe_highlight"
                            groups="base.group_user"
                            data-hotkey="x"/>
                    <field name="state"
                           widget="statusbar"
                           statusbar_visible="draft,confirmed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1 class="d-flex">

                            <field name="name"
                                   readonly="1"
                                   force_save="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <div class="o_td_label">
                                <label for="partner_id"
                                       string="Contact" style="font-weight:bold;"/>
                            </div>
                            <field name="partner_id"
                                   readonly="state != 'draft'"
                                   nolabel="1"/>
                            <field name="date" required="1"
                                   readonly="state != 'draft'"/>
                            <field name="source_picking_type_id"
                                   readonly="state != 'draft'"
                                   required="1"
                                   options="{'no_create': True,'no_open':True}"
                                   force_save="1"/>
                            <!--                            <field name="source_picking_type_str"-->
                            <!--                                   force_save="1"/>-->
                            <field name="destination_picking_type_id"
                                   invisible="1"
                            />
                            <field name="destination_type_id"
                                   required="1"
                                   options="{'no_create': True,'no_open':True}"
                                   readonly="state != 'draft'"
                            />

                        </group>
                        <group>
                            <field name="company_id" required="1"
                                   readonly="state != 'draft'"/>
                            <field name="first_picking_id"
                                   invisible="0"
                                   readonly="1" force_save="1"/>

                            <field name="second_picking_id"
                                   invisible="1"
                                   readonly="1" force_save="1"/>

                            <field name="third_picking_id"
                                   invisible="0"
                                   readonly="1" force_save="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Operations"
                              name="detailed_operations">
                            <field name="line_ids"
                                   readonly="state == 'confirmed'">
                                <tree editable="bottom">
                                    <field name="product_id"
                                           required="1"/>
                                    <field name="qty"/>
                                    <field name="product_uom" required="1"/>
                                </tree>
                            </field>
                        </page>

                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="internal_transfer_request_action" model="ir.actions.act_window">
        <field name="name">Internal Transfer Requests</field>
        <field name="res_model">internal.transfer.request</field>
        <field name="view_mode">tree,form</field>

    </record>
    <menuitem id="internal_transfer_menu"
              name="Internal Transfers"
              parent="stock.menu_stock_root"
              action="internal_transfer_request_action"
              groups="cubes_internal_transfer_request.internal_transfer_group"
              sequence="3"/>
</odoo>

