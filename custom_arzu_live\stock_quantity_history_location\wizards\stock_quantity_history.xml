<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <record id="view_stock_quantity_history_location" model="ir.ui.view">
        <field
            name="name"
        >stock.quantity.history.form - stock_quantity_history_location</field>
        <field name="model">stock.quantity.history</field>
        <field name="inherit_id" ref="stock.view_stock_quantity_history" />
        <field name="arch" type="xml">
            <field name="inventory_datetime" position="before">
                <field name="location_id" groups="stock.group_stock_multi_locations" />
                <field
                    name="include_child_locations"
                    invisible="not location_id"
                    groups="stock.group_stock_multi_locations"
                />
            </field>
        </field>
    </record>
</odoo>
