from odoo import api, fields, models
from odoo.exceptions import ValidationError


class InventoryInherit(models.Model):
    _inherit = 'stock.picking'
    state = fields.Selection([
        ('draft', 'Draft'),
        ('waiting', 'Waiting Another Operation'),
        ('confirmed', 'Waiting'),
        ('assigned', 'Ready'),
        ('driver', 'Driver'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ], string='Status', compute='_compute_state',
        copy=False, index=True, readonly=True, store=True, tracking=True,
        help=" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
             " * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
             " * Waiting: The transfer is waiting for the availability of some products.\n(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
             " * Ready: The transfer is ready to be processed.\n(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
             " * Done: The transfer has been processed.\n"
             " * Cancelled: The transfer has been cancelled.")

    driver_id = fields.Many2one(
        comodel_name='delivery.driver',
        related='sale_id.driver_id',
        store=True,
        readonly=False,
        tracking=True
    )

    def button_validate(self):
        res = super().button_validate()
        for line in self:
            if not line.driver_id:
                raise ValidationError(f"Driver field must be filled before validation !")
        return res


    def action_driver(self):
        self.state = 'driver'

class SaleOrderInherit(models.Model):
    _inherit = 'sale.order'

    driver_id = fields.Many2one(
        comodel_name='delivery.driver',
        store=True,
        readonly=False,
        tracking=True
    )

    def action_confirm(self):
        res = super().action_confirm()
        return res