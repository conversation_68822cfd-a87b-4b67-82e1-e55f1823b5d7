<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="sh_dynamic_lable_print_wizard_view" model="ir.ui.view">
        <field name="name">sh.dynamic.lable.print</field>
        <field name="model">sh.dynamic.lable.print</field>
        <field name="arch" type="xml">
            <form string="Product Dynamic Label Print">
                <sheet>
                    <notebook>
                        <page string="Product &amp; Barcode Quantity">
                            <field name="sh_lable_print_line_ids">
                                <form>
                                    <group>
                                        <group>
                                            <field name="product_id"/>
                                            <field name="partner_id"/>
                                            <field name="quantity"/>
                                        </group>
                                    </group>
                                </form>
                                <tree editable="bottom">
                                    <field name="product_id"/>
                                    <field name="partner_id"/>
                                    <field name="quantity"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Print Barcodes" name="print_cubes_barcode" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="sh_action_product_label_wizard" model="ir.actions.act_window">
        <field name="name">Dynamic Product Label</field>
        <field name="res_model">sh.dynamic.lable.print</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="sh_dynamic_lable_print_wizard_view"/>
        <field name="target">new</field>
    </record>
    <record model="ir.ui.view" id="product_template_print_barcode_button">
        <field name="name">product_template_print_barcode_button</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="cubes_barcode_module.sh_action_product_label_wizard" type="action" class="btn-primary"
                        string="Print Barcodes"/>
            </xpath>
        </field>
    </record>
    <record model="ir.ui.view" id="product_product_print_barcode_button">
        <field name="name">product_product_print_barcode_button</field>
        <field name="model">product.product</field>
        <field name="inherit_id" ref="product.product_normal_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="cubes_barcode_module.sh_action_product_label_wizard" type="action" class="btn-primary"
                        string="Print Barcodes"/>
            </xpath>
        </field>
    </record>
</odoo>
