<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Added fields for check journal type -->
    <record id="view_move_form" model="ir.ui.view">
        <field name="name">account.move.form.inherit.account.restrict.journal</field>
        <field name="model">account.move</field>
        <field name="mode">extension</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@id='header_right_group']" position="inside">
                <field name="is_check_journal" invisible="1"/>
                <field name="journal_id"
                       options="{'no_create': True}"
                       readonly="state != 'draft'"/>
            </xpath>
        </field>
    </record>
</odoo>
