<odoo>
    <record id="stock_view_picking_internal_search_inherited" model="ir.ui.view">
        <field name="name">stock picking</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_internal_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='picking_type']" position="after">
                <filter string="Client Address" name="street" domain="[]" context="{'group_by':'street'}"/>
            </xpath>
        </field>
    </record>
</odoo>