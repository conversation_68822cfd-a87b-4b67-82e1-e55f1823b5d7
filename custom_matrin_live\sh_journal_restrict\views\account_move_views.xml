<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_move_form_inherited" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='journal_div']//field[@name='journal_id'][1]" position="before">
                    <field name="user_journal_ids" invisible="1"/>
                </xpath>
                <xpath expr="//div[@name='journal_div']//field[@name='journal_id'][1]" position="attributes">
                    <attribute name="domain">[('id','in',user_journal_ids),('id', 'in', suitable_journal_ids)]</attribute>
                </xpath>
            </field>
    </record>

    <record id="view_account_payment_form_inherited" model="ir.ui.view">
            <field name="name">account.payment.form</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='journal_id']" position="before">
                    <field name="user_journal_ids" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='journal_id']" position="attributes">
                    <attribute name="domain">[('id','in',user_journal_ids),('id', 'in', available_journal_ids)]</attribute>
                </xpath>
<!--                <xpath expr="//field[@name='journal_id']" position="after">
                    <field name="branch_ids" widget="many2many_tags"/>
                </xpath>-->
            </field>
    </record>
</odoo>