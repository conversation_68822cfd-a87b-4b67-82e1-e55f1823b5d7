# -*- coding: utf-8 -*-

from odoo import models, fields, api

class HrEmployee(models.Model):
    _inherit ="hr.employee"
    
    access_pos_closing_details = fields.<PERSON><PERSON>an("Access Pos Closing Details")

class HrEmployeePublic(models.Model):
    _inherit ="hr.employee.public"
    
    access_pos_closing_details = fields.<PERSON><PERSON>an("Access Pos Closing Details")

class PosSessions(models.Model):
    _inherit="pos.session"
    
    def _loader_params_hr_employee(self):
        res = super()._loader_params_hr_employee()
        new_fields_need_load = [
            "access_pos_closing_details",
        ]
        res["search_params"]["fields"].extend(new_fields_need_load)
        return res