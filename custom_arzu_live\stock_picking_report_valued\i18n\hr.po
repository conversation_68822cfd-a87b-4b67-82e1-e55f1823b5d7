# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_picking_report_valued
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-12-01 02:22+0000\n"
"PO-Revision-Date: 2017-12-01 02:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Croatian (https://www.transifex.com/oca/teams/23907/hr/)\n"
"Language: hr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Discount</strong>"
msgstr "<strong>Popust</strong>"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Subtotal</strong>"
msgstr "<strong>Subtotal</strong>"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Taxes</strong>"
msgstr "<strong>Porezi</strong>"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Unit Price</strong>"
msgstr "<strong>Jed.cijena</strong>"

#. module: stock_picking_report_valued
#: model_terms:ir.ui.view,arch_db:stock_picking_report_valued.valued_report_picking
msgid "<strong>Untaxed Amount</strong>"
msgstr "<strong>Bez poreza</strong>"

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_res_partner
msgid "Contact"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_subtotal
msgid "Price subtotal"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_line
msgid "Related order line"
msgstr "Povezana stavka ponude"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__currency_id
#, fuzzy
msgid "Sale Currency"
msgstr "Valuta"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_unit
msgid "Sale Price Unit"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_tax_id
msgid "Sale Tax"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_discount
msgid "Sale discount (%)"
msgstr "Popust prodaje (%)"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_tax_description
msgid "Tax Description"
msgstr "Opis poreza"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_tax
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_tax
msgid "Taxes"
msgstr "Porezi"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_move_line__sale_price_total
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_total
msgid "Total"
msgstr "Ukupno"

#. module: stock_picking_report_valued
#: model:ir.model,name:stock_picking_report_valued.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__amount_untaxed
msgid "Untaxed Amount"
msgstr "Iznos bez poreza"

#. module: stock_picking_report_valued
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_res_partner__valued_picking
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_res_users__valued_picking
#: model:ir.model.fields,field_description:stock_picking_report_valued.field_stock_picking__valued
msgid "Valued Picking"
msgstr ""

#. module: stock_picking_report_valued
#: model:ir.model.fields,help:stock_picking_report_valued.field_res_partner__valued_picking
#: model:ir.model.fields,help:stock_picking_report_valued.field_res_users__valued_picking
#: model:ir.model.fields,help:stock_picking_report_valued.field_stock_picking__valued
msgid "You can select which partners have valued pickings"
msgstr ""

#, fuzzy
#~ msgid "<strong>Qty Reserved</strong>"
#~ msgstr "<strong>Jed.cijena</strong>"

#~ msgid "Sale price unit"
#~ msgstr "Prodajna cijena"

#~ msgid "Packing Operation"
#~ msgstr "Operacija pakiranja"

#~ msgid "Partner"
#~ msgstr "Partner"
