============================
Generate Barcodes (Abstract)
============================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:e9e42f143864f0fbcb74dabdfe7f7bb1cac5e24dbf49b4e7f2945d53d9ed0999
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-AGPL--3-blue.png
    :target: http://www.gnu.org/licenses/agpl-3.0-standalone.html
    :alt: License: AGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fstock--logistics--barcode-lightgray.png?logo=github
    :target: https://github.com/OCA/stock-logistics-barcode/tree/17.0/barcodes_generator_abstract
    :alt: OCA/stock-logistics-barcode
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/stock-logistics-barcode-17-0/stock-logistics-barcode-17-0-barcodes_generator_abstract
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/stock-logistics-barcode&target_branch=17.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module extends Odoo functionality, allowing user to generate
barcode depending on a given barcode rule for any Model.

For example, if the barcode pattern is "20.....{NNNDD}":

-  the EAN13 code will begin with '20',
-  followed by 5 digits (named *Barcode Base* in this module),
-  followed by 5 others digits to define the variable price with 2
   decimals,
-  the last digit (the 13rd digit) is the control digit (i.e. the
   checksum).

With this module, it is possible to:

-  Affect a pattern (barcode.rule) to a model

-  Define a Barcode base:

   -  manually, if the base of the barcode must be set by a user
      (typically an internal code defined in your company).
   -  automatically by a sequence, if you want to let Odoo increment a
      sequence (typical case of a customer number incrementation).

-  Generate a barcode, based on the defined pattern and the barcode base

**Table of contents**

.. contents::
   :local:

Installation
============

This module use an extra python library named 'python-barcode' you
should install to make barcode generation works properly.

``sudo pip install python-barcode``

Configuration
=============

To configure this module, you need to:

-  Go to Settings / Technical / Sequences & Identifiers / Barcode
   Nomenclatures
-  Select a Nomenclature
-  Create or select a rule

|image|

-  For manual generation, set:

   -  'Base set Manually' in 'Generate Type'
   -  Set the model

|image1|

-  For automatic generation, set:

   -  'Base managed by Sequence' in 'Generate Type'
   -  Set the model
   -  Generate a new sequence by button, or affect a existing one

|image2|

In all cases, padding will be computed automaticaly, based on the number
of '.' in the Barcode Pattern field.

.. |image| image:: https://raw.githubusercontent.com/OCA/stock-logistics-barcode/17.0/barcodes_generator_abstract/static/description/barcode_nomenclature_form.png
.. |image1| image:: https://raw.githubusercontent.com/OCA/stock-logistics-barcode/17.0/barcodes_generator_abstract/static/description/barcode_rule_form_manual.png
.. |image2| image:: https://raw.githubusercontent.com/OCA/stock-logistics-barcode/17.0/barcodes_generator_abstract/static/description/barcode_rule_form_sequence.png

Usage
=====

This module is an abstract module. You can configure Barcode Rule, but
to enable this feature, you need to install an extra module for a given
model. This repository provide 'barcodes_generator_product' and
'barcodes_generator_partner' module to generate barcode for product or
partner model.

Alternatively, you can develop a custom module for a custom model. See
'Inheritance' parts.

If you want to generate barcode for another model, you can create a
custom module that depend on 'barcodes_generator_abstract' and inherit
your model like that:

::

   class MyModel(models.Model):
       _name = 'my.model'
       _inherit = ['my.model', 'barcode.generate.mixin']

   class barcode_rule(models.Model):
       _inherit = 'barcode.rule'

       generate_model = fields.Selection(selection_add=[('my.model', 'My Model')])

Eventually, you should inherit your model view adding buttons and
fields.

Note
----

Your model should have a field 'barcode' defined.

Known issues / Roadmap
======================

-  On barcode.rule model, constraint and domain system could be set
   between 'type' and 'generate_model' fields.
-  Cache is being cleared in a constraint in barcode.rule. Mutating in a
   constraint is bad practice & should be moved somewhere.

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/stock-logistics-barcode/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/stock-logistics-barcode/issues/new?body=module:%20barcodes_generator_abstract%0Aversion:%2017.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* GRAP
* La Louve
* LasLabs

Contributors
------------

-  Sylvain LE GAL (https://twitter.com/legalsylvain)
-  Dave Lasley <<EMAIL>>
-  `Tecnativa <https://www.tecnativa.com>`__:

   -  Carlos Roca

-  `Ooops404 <https://www.ooops404.com>`__:

   -  Ilyas <<EMAIL>>

Other credits
-------------

Images
~~~~~~

-  Icon of the module is based on the Oxygen Team work and is under LGPL
   licence:
   http://www.iconarchive.com/show/oxygen-icons-by-oxygen-icons.org.html

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

.. |maintainer-legalsylvain| image:: https://github.com/legalsylvain.png?size=40px
    :target: https://github.com/legalsylvain
    :alt: legalsylvain

Current `maintainer <https://odoo-community.org/page/maintainer-role>`__:

|maintainer-legalsylvain| 

This module is part of the `OCA/stock-logistics-barcode <https://github.com/OCA/stock-logistics-barcode/tree/17.0/barcodes_generator_abstract>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
