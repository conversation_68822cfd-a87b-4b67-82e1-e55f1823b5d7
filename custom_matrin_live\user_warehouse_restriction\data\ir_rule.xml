<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="restrict_warehouse_for_user_group" model="res.groups">
            <field name="name">Restrict Warehouse for User</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="restrict_warehouse_for_user_rule" model="ir.rule">
            <field name="name">Restrict User with Specific Warehouse</field>
            <field name="model_id" ref="stock.model_stock_quant"/>
            <field name="domain_force">[('warehouse_id', 'in', user.warehouse_ids.ids)]</field>
<!--            <field name="domain_force">[('user_id', '=', user.id)]</field>-->
            <field name="groups" eval="[(4, ref('restrict_warehouse_for_user_group'))]"/>
        </record>

<!--        <record id="restrict_payment_journal_for_user_rule" model="ir.rule">
            <field name="name">Restrict Payment User with Specific Journal</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('restrict_journal_for_user_group'))]"/>
        </record>-->
    </data>
</odoo>