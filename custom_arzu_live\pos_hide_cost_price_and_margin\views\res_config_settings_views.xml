<?xml version="1.0" encoding="utf-8" ?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="model">res.config.settings</field>
            <field
            name="inherit_id"
            ref="point_of_sale.res_config_settings_view_form"
        />
            <field name="arch" type="xml">
                <xpath expr="//setting[@id='margin_and_cost']" position="before">
                    <setting
                    id="margin_and_cost_admin"
                    string="Margins &amp; Costs (Admin)"
                    help="When disabled, margin and cost of product are hidden in the Product info."
                >
                        <field name="pos_is_margins_costs_accessible_to_admin" />
                    </setting>
                </xpath>
                <xpath expr="//setting[@id='margin_and_cost']" position="attributes">
                    <attribute
                    name="string"
                >Margins &amp; Costs (every user)</attribute>
                    <attribute
                    name="invisible"
                >not pos_is_margins_costs_accessible_to_admin</attribute>
                </xpath>
            </field>
        </record>
</odoo>
