# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* cubes_barcode_module
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-12-10 07:02+0000\n"
"PO-Revision-Date: 2020-12-10 07:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cubes_barcode_module
#: model:ir.actions.report,print_report_name:cubes_barcode_module.sh_barcode_report_action
msgid "'Product Dynamic Barcode Label'"
msgstr "'Etiqueta de código de barras dinámico del producto'"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__after
msgid "After"
msgstr "Después"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__barcode
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_barcode_report_doc
msgid "Barcode"
msgstr "Código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_field_id
msgid "Barcode Field"
msgstr "Campo de código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_height
msgid "Barcode Height"
msgstr "Altura del código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__quantity
msgid "Barcode Quantity"
msgstr "Cantidad de código de barras"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_report_cubes_barcode_module_sh_barcode_report_doc
msgid "Barcode Report"
msgstr "Informe de código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_width
msgid "Barcode Width"
msgstr "Ancho del código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_print_barcode_or_qr
msgid "Barcode/QR Type"
msgstr "Tipo de código de barras / QR"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_currency_position__before
msgid "Before"
msgstr "antes de"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Cancel"
msgstr "Cancelar"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__center
msgid "Center"
msgstr "Centrar"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code128
msgid "Code 128"
msgstr "Código 128"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__code11
msgid "Code11"
msgstr "Código11"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__company_id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__company_id
msgid "Company"
msgstr "Empresa"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__create_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__create_date
msgid "Created on"
msgstr "Creado en"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_currency_position
msgid "Currency Symbol Position"
msgstr "Posición del símbolo de moneda"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__display_name
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__display_name
msgid "Display Name"
msgstr "Nombre para mostrar"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label"
msgstr "Etiqueta de código de barras dinámico"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Barcode Label Line"
msgstr "Línea de etiqueta de código de barras dinámica"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__lable_id
msgid "Dynamic Label"
msgstr "Etiqueta dinámica"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template
msgid "Dynamic Label Template"
msgstr "Plantilla de etiqueta dinámica"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_print_line_ids
msgid "Dynamic Lable Line"
msgstr "Línea de etiqueta dinámica"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print
msgid "Dynamic Lable Print"
msgstr "Impresión de etiqueta dinámica"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_lable_print_line
msgid "Dynamic Lable Print Line"
msgstr "Línea de impresión de etiquetas dinámica"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.account_move_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_product_action
#: model:ir.actions.act_window,name:cubes_barcode_module.product_template_action
#: model:ir.actions.act_window,name:cubes_barcode_module.purchase_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sale_order_action
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_action_product_label_wizard
#: model:ir.actions.act_window,name:cubes_barcode_module.stock_picking_action
msgid "Dynamic Product Label"
msgstr "Etiqueta de producto dinámica"

#. module: cubes_barcode_module
#: model:ir.actions.act_window,name:cubes_barcode_module.sh_product_lable_template_action
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_lable_template_tree_view
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "Dynamic Product Label Template"
msgstr "Plantilla de etiqueta de producto dinámica"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended39
msgid "Extended39"
msgstr "Extendido39"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__extended93
msgid "Extended93"
msgstr "Extendido93"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__fim
msgid "FIM"
msgstr "FIM"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_field_id
msgid "Field"
msgstr "Campo"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__ttype
msgid "Field Type"
msgstr "Tipo de campo"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_bold
msgid "Font Bold"
msgstr "Negrita"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_color
msgid "Font Color"
msgstr "Color de fuente"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_italic
msgid "Font Italic"
msgstr "Fuente cursiva"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_size
msgid "Font Size"
msgstr "Tamaño de fuente"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_font_underline
msgid "Font Underline"
msgstr "Subrayado de fuente"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__i2of5
msgid "I2of5"
msgstr "I2of5"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__id
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__id
msgid "ID"
msgstr "CARNÉ DE IDENTIDAD"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_label_display
msgid "Is Label Display ?"
msgstr "¿Se muestra la etiqueta?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_is_price_field
msgid "Is Price Field ?"
msgstr "¿Es el campo de precio?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__is_valid
msgid "Is Valid ?"
msgstr "Es válido ?"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__sh_lable_template_id
msgid "Label Print Template"
msgstr "Plantilla de impresión de etiquetas"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__landscape
msgid "Landscape"
msgstr "Paisaje"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_report_cubes_barcode_module_sh_barcode_report_doc____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template____last_update
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_uid
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__write_date
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__write_date
msgid "Last Updated on"
msgstr "Ultima actualización en"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__left
msgid "Left"
msgstr "Izquierda"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__msi
msgid "MSI"
msgstr "MSI"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_margin
msgid "Margin"
msgstr "Margen"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__name
msgid "Name"
msgstr "Nombre"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_product_template_dynamic_view
msgid "PDF Page Configuration"
msgstr "Configuración de página PDF"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_height
msgid "PDF Page Height"
msgstr "Altura de la página PDF"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_width
msgid "PDF Page Width"
msgstr "Ancho de página PDF"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__postnet
msgid "POSTNET"
msgstr "POSTNET"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_page_orientation
msgid "Page Orientation"
msgstr "Orientación de la página"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__paperformat_id
msgid "Paper Format"
msgstr "Formato de papel"

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid ""
"Please select fields which you have defined in dynamic barcode lines, If you"
" have not added pls add field first in dynamic barcode label lines."
msgstr ""
"Seleccione los campos que ha definido en líneas de código de barras dinámicas, si"
"No se han agregado los pls agregan el campo primero en las líneas de etiquetas de códigos de barras dinámicos."

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_page_orientation__portrait
msgid "Portrait"
msgstr "Retrato"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sh_position
msgid "Position"
msgstr "Posición"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Print"
msgstr "Impresión"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_lable_print_line__product_id
msgid "Product"
msgstr "Producto"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product & Barcode Quantity"
msgstr "Cantidad de producto y código de barras"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_main
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template_sub
msgid "Product Barcode Label"
msgstr "Etiqueta de código de barras del producto"

#. module: cubes_barcode_module
#: model:ir.ui.menu,name:cubes_barcode_module.sh_product_label_template
#: model:res.groups,name:cubes_barcode_module.group_product_barcode_label_template
msgid "Product Barcode Label Template"
msgstr "Plantilla de etiqueta de código de barras de producto"

#. module: cubes_barcode_module
#: model:ir.actions.report,name:cubes_barcode_module.sh_barcode_report_action
msgid "Product Dynamic Barcode Label"
msgstr "Etiqueta de código de barras dinámico del producto"

#. module: cubes_barcode_module
#: model_terms:ir.ui.view,arch_db:cubes_barcode_module.sh_dynamic_lable_print_wizard_view
msgid "Product Dynamic Label Print"
msgstr "Impresión de etiquetas dinámicas del producto"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_print_barcode_or_qr__qr
msgid "QR"
msgstr "QR"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template_line__sh_position__right
msgid "Right"
msgstr "Correcto"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard39
msgid "Standard39"
msgstr "Estándar39"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__standard93
msgid "Standard93"
msgstr "Estándar93"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template_line__template_id
msgid "Template"
msgstr "Modelo"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_template_line_ids
msgid "Template Line"
msgstr "Línea de plantilla"

#. module: cubes_barcode_module
#: model:ir.model,name:cubes_barcode_module.model_sh_dynamic_template_line
msgid "Template Lines"
msgstr "Líneas de plantilla"

#. module: cubes_barcode_module
#: model:ir.model.fields,field_description:cubes_barcode_module.field_sh_dynamic_template__sh_barcode_type
msgid "Type of Barcode"
msgstr "Tipo de código de barras"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__upca
msgid "UPCA"
msgstr "UPCA"

#. module: cubes_barcode_module
#: model:ir.model.fields.selection,name:cubes_barcode_module.selection__sh_dynamic_template__sh_barcode_type__usps_4state
msgid "USPS_4State"
msgstr "USPS_4State"

#. module: cubes_barcode_module
#: model:ir.model.fields,help:cubes_barcode_module.field_sh_dynamic_template_line__sequence
msgid "Used to handle lines."
msgstr "Se usa para manejar líneas."

#. module: cubes_barcode_module
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#: code:addons/cubes_barcode_module/models/sh_product_label_dynamic_template.py:0
#, python-format
msgid "You can not Add Duplicate Fields"
msgstr "No puede agregar campos duplicados"
