<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Template Form View -->
    <record id="product_template_only_form_view" model="ir.ui.view">
        <field name="name">product.template.view.form.inherit.hide.cost.price</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_only_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//label[@for='standard_price']" position="attributes">
                <attribute name="groups">hide_cost_price.groups_view_cost_price</attribute>
            </xpath>
            <xpath expr="//div[@name='standard_price_uom']" position="attributes">
                <attribute name="groups">hide_cost_price.groups_view_cost_price</attribute>
            </xpath>
        </field>
    </record>
    <!-- Product Template Tree View -->
    <record id="product_template_tree_view" model="ir.ui.view">
        <field name="name">product.template.view.tree.inherit.hide.cost.price</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='standard_price']" position="attributes">
                <attribute name="groups">hide_cost_price.groups_view_cost_price</attribute>
            </xpath>
        </field>
    </record>
</odoo>
