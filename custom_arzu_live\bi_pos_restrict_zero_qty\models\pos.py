# -*- coding: utf-8 -*-
# Part of BrowseInfo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, _


class PosConfig(models.Model):
	_inherit = "pos.config"

	restrict_zero_qty = fields.Boolean(string='Restrict Zero Quantity')
	default_location_src_id = fields.Many2one('stock.location', related='picking_type_id.default_location_src_id')


class ResConfigSettings(models.TransientModel):
	_inherit = 'res.config.settings'

	restrict_zero_qty = fields.Boolean(related="pos_config_id.restrict_zero_qty",readonly=False)


class PosSession(models.Model):
	_inherit = 'pos.session'

	def _loader_params_product_product(self):
		result = super()._loader_params_product_product()
		# product_qty = self.product_3.with_context(location=self.config_id.picking_type_id.default_location_src_id.id).qty_available
		result['search_params']['fields'].extend(['qty_available','type', 'qty_in_location'])
		return result

	def _get_pos_ui_pos_config(self, params):
		CONFIG = self.env['pos.config'].sudo()
		result = super()._get_pos_ui_pos_config(params)
		pos_config_id = CONFIG.browse(result['id']) 
		result['default_location_src_id'] = pos_config_id.default_location_src_id.id
		return result