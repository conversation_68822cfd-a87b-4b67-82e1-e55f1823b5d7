# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * barcodes_generator_abstract
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-25 08:40+0000\n"
"PO-Revision-Date: 2024-01-26 10:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__generate_type
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_type
msgid ""
"Allow to generate barcode, including a number  (a base) in the final "
"barcode.\n"
"\n"
" - 'Base Set Manually' : User should set manually the value of the barcode "
"base\n"
" - 'Base managed by Sequence': System will generate the base via a sequence"
msgstr ""
"Consente di generare un codice a barre, incluso un numero  (una base) nel "
"codice a barre finale.\n"
"\n"
" 'Base impostata manualmente' : l'utente deve impostare manualmente il "
"valore della base del codice a barre\n"
" 'Base gestita da sequenza': l'utente utilizzerà un pulsante per generare "
"una nuova base. Questa base sarà generata da una sequenza"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_automate
msgid "Automatic Generation"
msgstr "Generazione Automatica"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_base
msgid "Barcode Base"
msgstr "Base codice a barre"

#. module: barcodes_generator_abstract
#: model_terms:ir.ui.view,arch_db:barcodes_generator_abstract.view_barcode_rule_form
msgid "Barcode Generation"
msgstr "Generazione codici a barre"

#. module: barcodes_generator_abstract
#: model:ir.ui.menu,name:barcodes_generator_abstract.menu_barcode_rule
msgid "Barcode Nomenclatures"
msgstr "Nomenclatura Codice a Barre"

#. module: barcodes_generator_abstract
#: model:ir.model,name:barcodes_generator_abstract.model_barcode_rule
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_rule_id
msgid "Barcode Rule"
msgstr "Regola codice a barre"

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__sequence
msgid "Base managed by Sequence"
msgstr "Base gestita da Sequenza"

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__manual
msgid "Base set Manually"
msgstr "Base impostata Manualmente"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_automate
msgid ""
"Check this to automatically generate a base and a barcode if this rule is "
"selected."
msgstr ""
"Selezionare questa opzione per generare qutomaticamente una base e un codice "
"a barre se questa regola è selezionata."

#. module: barcodes_generator_abstract
#: model:ir.model,name:barcodes_generator_abstract.model_barcode_generate_mixin
msgid "Generate Barcode Mixin"
msgstr "Genera Codice a barre"

#. module: barcodes_generator_abstract
#: model:res.groups,name:barcodes_generator_abstract.generate_barcode
msgid "Generate Barcodes"
msgstr "Genera codici a barre"

#. module: barcodes_generator_abstract
#. odoo-python
#: code:addons/barcodes_generator_abstract/models/barcode_generate_mixin.py:0
#, python-format
msgid ""
"Generate Base can be used only with barcode rule with 'Generate Type' set to "
"'Base managed by Sequence'"
msgstr ""
"Genera base può essere utilizzato solo con la regola del codice a barre con "
"'Genera tipo' impostato su 'Base gestita da sequenza'"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_model
msgid "Generate Model"
msgstr "Genera modello"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_generate_mixin__generate_type
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__generate_type
msgid "Generate Type"
msgstr "Genera tipo"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__sequence_id
msgid "Generation Sequence"
msgstr "Sequenza generazione"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_rule__generate_model
msgid "If 'Generate Type' is set, mention the model related to this rule."
msgstr ""
"Se 'Genera tipo' è impostato, menzionare il modello relativo a questa regola."

#. module: barcodes_generator_abstract
#: model_terms:ir.ui.view,arch_db:barcodes_generator_abstract.view_barcode_rule_form
msgid ""
"If you leave the sequence field blank, a sequence will be created "
"automatically when the barcode rule is saved, based on the padding of the "
"barcode."
msgstr ""
"Se si lascia vuoto il campo sequenza, una sequenza verrà creata "
"automaticamente quando la regola codice a barre verrà salvata, in base al "
"riempimento del codice a barre."

#. module: barcodes_generator_abstract
#: model:ir.model.fields.selection,name:barcodes_generator_abstract.selection__barcode_rule__generate_type__no
msgid "No generation"
msgstr "Nessuna generazione"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,field_description:barcodes_generator_abstract.field_barcode_rule__padding
msgid "Padding"
msgstr "Padding"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_rule_id
msgid "Select a rule to generate a barcode"
msgstr "Selezionare una regola per generare il codice a barre"

#. module: barcodes_generator_abstract
#. odoo-python
#: code:addons/barcodes_generator_abstract/models/barcode_rule.py:0
#, python-format
msgid "Sequence - %s"
msgstr "Sequenza - %s"

#. module: barcodes_generator_abstract
#: model:ir.model.fields,help:barcodes_generator_abstract.field_barcode_generate_mixin__barcode_base
msgid ""
"This value is used to generate barcode according to the setting of the "
"barcode rule."
msgstr ""
"Questo valore è utilizzato per generare il codice a barre in accordo alle "
"impostazioni della regola del codice a barre."

#~ msgid ""
#~ "Allow to generate barcode, including a number  (a base) in the final "
#~ "barcode.\n"
#~ " 'Base Set Manually' : User should set manually the value of the barcode "
#~ "base\n"
#~ " 'Base managed by Sequence': User will use a button to generate a new "
#~ "base. This base will be generated by a sequence"
#~ msgstr ""
#~ "Consenti di generare un codice a barre, incluso un numero  (a base) nel "
#~ "codice a barre finale.\n"
#~ " 'Base impostata manualmente' : L'utente deve impostare manualmente il "
#~ "valore della base del codice a barre\n"
#~ " 'Base gestita da Sequence': L'utente utilizzerà un pulsante per generare "
#~ "una nuova base. Questa base sarà generata da una sequenza"

#~ msgid ""
#~ "Check this to automatically generate a barcode upon creation of a new "
#~ "record in the mixed model."
#~ msgstr ""
#~ "Spuntare questo per generare automaticamente un codice a barre al momento "
#~ "della creazione di un nuovo record nel modello misto."

#, python-format
#~ msgid ""
#~ "Only one rule per model can be used for automatic barcode generation."
#~ msgstr ""
#~ "Solo una regola per modello può essere usata per la generazione "
#~ "automatica dei codici a barre."

#~ msgid "Display Name"
#~ msgstr "Nome visualizzato"

#~ msgid "Generate Sequence"
#~ msgstr "Genera Sequenza"

#, python-format
#~ msgid ""
#~ "Generate Sequence is possible only if  'Generate Type' is set to 'Base "
#~ "managed by Sequence'"
#~ msgstr ""
#~ "Genera Sequenza è possibile solo se  'Genera Tipo' è impostato per 'Base "
#~ "gestita da sequenza'"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"

#~ msgid "Partners"
#~ msgstr "Partner"

#~ msgid "Products"
#~ msgstr "Prodotti"

#~ msgid "Sequence Id"
#~ msgstr "ID Sequenza"

#~ msgid "Stock Location"
#~ msgstr "Ubicazione Scorte"
