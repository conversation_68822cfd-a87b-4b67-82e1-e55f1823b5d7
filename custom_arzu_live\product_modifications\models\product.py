# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'


    default_code = fields.Char(string="Internal Reference", compute='_compute_default_code', inverse='_set_default_code', store=True)

    def _compute_default_code(self):
        pass

    def _set_default_code(self):
        pass

    def write(self, vals):
        res = super().write(vals)
        if 'default_code' in vals:
            for template in self:
                template.product_variant_ids.write({'default_code': vals['default_code']})
        return res

class ProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        for record in records:
            if record.product_tmpl_id.default_code and not record.default_code:
                record.default_code = record.product_tmpl_id.default_code
        return records


