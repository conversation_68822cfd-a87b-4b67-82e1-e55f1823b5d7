<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="restrict_journal_for_user_group" model="res.groups">
        <field name="name">Restrict Journals for User</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

        <record id="restrict_journal_for_user_rule" model="ir.rule">
            <field name="name">Restrict User with Specific Journal</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('restrict_journal_for_user_group'))]"/>
        </record>

                <record id="restrict_payment_journal_for_user_rule" model="ir.rule">
            <field name="name">Restrict Payment User with Specific Journal</field>
            <field name="model_id" ref="account.model_account_payment"/>
            <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
            <field name="groups" eval="[(4, ref('restrict_journal_for_user_group'))]"/>
        </record>
    </data>
</odoo>