.pos-receipt-print {
    width: 512px;
    height: 0;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
    text-align: left;
    direction: ltr;
    font-size: 27px;
    color: #000000;
}

.pos-receipt .pos-receipt-right-align {
    float: right;
}

.pos-receipt .pos-receipt-center-align {
    text-align: center;
}

.pos-receipt .pos-receipt-left-padding {
    padding-left: 2em;
}

.pos-receipt .pos-receipt-logo {
    width: 85%;
    display: block;
    margin: 0 auto;
}

.pos-receipt .pos-receipt-contact {
    text-align: center;
    font-size: 75%;
}

.pos-receipt .pos-receipt-order-data {
    text-align: center;
    font-size: 75%;
}

.pos-receipt .pos-receipt-amount {
    font-size: 125%;
    padding-left: 6em;
}

.pos-receipt .pos-receipt-title {
    font-weight: bold;
    font-size: 125%;
    text-align: center;
}

.pos-receipt .pos-receipt-header {
    font-size: 125%;
    text-align: center;
}

.pos-receipt .pos-order-receipt-cancel {
    color: red;
}

.pos-receipt .pos-receipt-customer-note {
    word-break: break-all;
}

.pos-payment-terminal-receipt {
    text-align: center;
    font-size: 75%;
}

.responsive-price {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
}

.responsive-price > .pos-receipt-right-align {
    margin-left: auto;
}
.td_discount {
    text-align:center;
    > t > span {
         color: red;
         text-decoration: line-through;
    }
}
.test_class {
    color:red;
}