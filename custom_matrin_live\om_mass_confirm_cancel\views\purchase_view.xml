<odoo>
    <data>

        <record id="action_purchase_confirm" model="ir.actions.server">
            <field name="name">Confirm RFQ</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="binding_model_id" ref="purchase.model_purchase_order"/>
            <field name="state">code</field>
            <field name="code">
                if records:
                    for rec in records:
                        rec.button_confirm()
            </field>
        </record>

        <record id="action_purchase_cancel" model="ir.actions.server">
            <field name="name">Cancel RFQ</field>
            <field name="model_id" ref="purchase.model_purchase_order"/>
            <field name="binding_model_id" ref="purchase.model_purchase_order"/>
            <field name="state">code</field>
            <field name="code">
                if records:
                    for rec in records:
                        rec.button_cancel()
            </field>
        </record>

    </data>
</odoo>
