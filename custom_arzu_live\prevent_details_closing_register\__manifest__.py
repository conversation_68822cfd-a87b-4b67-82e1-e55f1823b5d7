# -*- coding: utf-8 -*-
{
    'name': "prevent_details_closing_register",

    'summary': "A module to hide details of POS Closing Details",

    'description': """
    A module to hide details of POS Closing Details
    """,

    'author': "<PERSON>",
    'website': "https://www.cubes.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/15.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Point of Sale',
    'version': '0.1',

    # any module necessary for this one to work correctly
    'depends': ['base','point_of_sale','pos_hr'],

    # always loaded
    'data': [
       
        'views/views.xml',
       
    ],
    # only loaded in demonstration mode
    'assets': {
        'point_of_sale._assets_pos': [
            'prevent_details_closing_register/static/src/xml/closing_popup.xml',
            
        ],
    },
    'license': 'LGPL-3',
    
}

