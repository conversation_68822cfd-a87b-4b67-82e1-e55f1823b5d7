from odoo import api, fields, models


class StockWarehouseInherit(models.Model):
    _inherit = 'stock.warehouse'

    warehouse_phone = fields.Char(
        string="Warehouse Phone"
    )
    warehouse_address = fields.Char(
        string="Warehouse Address"
    )
    subzon_domain = fields.Char()


    zone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Sender Zone'
    )
    sub_zone_id = fields.Many2one(
        comodel_name='skyex.subzones',
        string='Sender Sub Zone',
    )

    @api.onchange('zone_id')
    def onchange_zone_id(self):
        if self.zone_id:
            self.subzon_domain = [('id', 'in', self.sub_zone_id.search([('zone_id', '=', self.zone_id.id)]).ids)]
        else:
            self.subzon_domain = [('id', 'in', [])]

class SaleOrderInherit(models.Model):
    _inherit = 'sale.order'

    receiption_zone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Destination Zone'
    )

    receiption_sub_zone_id = fields.Many2one(
        comodel_name='skyex.subzones',
        string='Destination Sub Zone',
    )

    receiption_subzon_domain = fields.Char()

    @api.onchange('receiption_zone_id')
    def onchange_receiption_zone_id(self):
        if self.receiption_zone_id:
            self.receiption_subzon_domain = [
                ('id', 'in', self.receiption_sub_zone_id.search([('zone_id', '=', self.receiption_zone_id.id)]).ids)]
        else:
            self.receiption_subzon_domain = [('id', 'in', [])]




