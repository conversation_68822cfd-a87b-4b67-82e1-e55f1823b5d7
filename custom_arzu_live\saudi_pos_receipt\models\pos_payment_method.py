# -*- coding: utf-8 -*-
# Part of BrowseInfo. See LICENSE file for full copyright and licensing details.
import logging
from odoo import api, fields, models, tools, _

_logger = logging.getLogger(__name__)


class POSPaymentMethod(models.Model):
    _inherit = 'pos.payment.method'

    cash_method = fields.Boolean(default=False)

    def is_cash_method(self):
        return self.cash_method


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _loader_params_pos_payment_method(self):
        result = super()._loader_params_pos_payment_method()
        result['search_params']['fields'].append('cash_method')
        return result
