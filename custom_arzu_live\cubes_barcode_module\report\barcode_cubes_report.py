# -*- coding: utf-8 -*-
# Part of Softhealer Technologies.

from odoo import api, models
import base64
import qrcode
import os


class BarcodeReport(models.AbstractModel):
    _name = 'report.cubes_barcode_module.sh_barcode_cubes_report_doc'
    _description = 'Barcode Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        """Get data from barcode label template and prepare final data dictionary and pass to the qweb template and print the label"""
        data = dict(data or {})
        data_list = []
        for elem in data['product_dic']:
            product = self.env['product.product'].search([('id','=',elem['product_id'])])
            color = False
            size = False
            for attrs in product.product_template_attribute_value_ids:
                if attrs.attribute_id.name == 'Color':
                    color = attrs.name
                if attrs.attribute_id.name == 'Size':
                    size = attrs.name

            vals = {
                'name':product.name,
                'barcode':'/report/barcode/Code128/' + str(product.barcode),
                'barcode_no': product.barcode,
                'ref': product.default_code,
                'price': float(product.list_price),
                'size':size,
                'color':color
            }
            for i in range(elem['qty']):
                data_list.append(vals)

        data.update({
            'data_list': data_list,
        })
        return data
