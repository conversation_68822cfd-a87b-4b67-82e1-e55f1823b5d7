# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author: <PERSON><PERSON>gy<PERSON>v KP (<EMAIL>)
#
#    This program is free software: you can modify
#    it under the terms of the GNU Affero General Public License (AGPL) as
#    published by the Free Software Foundation, either version 3 of the
#    License, or (at your option) any later version.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU Affero General Public License for more details.
#
#    You should have received a copy of the GNU Affero General Public License
#    along with this program.  If not, see <https://www.gnu.org/licenses/>.
#
################################################################################
from odoo import models


class PosSession(models.Model):
    """
    The inherited class PosSession to add new fields and models to pos.session
    """
    _inherit = 'pos.session'

    def _loader_params_hr_employee(self):
        """
        Method for loading hr_employee fields to pos.session
        :return: dictionary containing hr_employee access right fields
        """
        result = super()._loader_params_hr_employee()
        result['search_params']['fields'].extend(
            ['disable_payment', 'disable_customer', 'disable_plus_minus',
             'disable_numpad', 'disable_qty', 'disable_discount',
             'disable_price', 'disable_remove_button'])
        return result
