<odoo>
  <data noupdate="1">
    <record id="journal_restrict_group" model="res.groups">
      <field name="name">Journal Restrictions</field>
      <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record model="ir.rule" id="journal_security_rule">
      <field name="name">Journals restricted to users</field>
      <field name="model_id" ref="account.model_account_journal"/>
      <field name="groups" eval="[(4, ref('dvit_account_journal_restrict.journal_restrict_group'))]"/>
      <field name="domain_force">[('id', 'in', user.journal_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="journal_security_rule_account_invoice">
      <field name="name">Invoices of Jornals restricted to user</field>
      <field name="model_id" ref="account.model_account_move"/>
      <field name="groups" eval="[(4, ref('dvit_account_journal_restrict.journal_restrict_group'))]"/>
      <field name="domain_force">[('journal_id.id', 'in', user.journal_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="journal_security_rule_account_move">
      <field name="name">Account Move of Jornals restricted to user</field>
      <field name="model_id" ref="account.model_account_move"/>
      <field name="groups" eval="[(4, ref('dvit_account_journal_restrict.journal_restrict_group'))]"/>
      <field name="domain_force">[('journal_id.id', 'in', user.journal_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="journal_security_rule_account_move_line">
      <field name="name">Account Move Line of Jornals restricted to user</field>
      <field name="model_id" ref="account.model_account_move_line"/>
      <field name="groups" eval="[(4, ref('dvit_account_journal_restrict.journal_restrict_group'))]"/>
      <field name="domain_force">['|',('journal_id','=',False),('journal_id.id', 'in', user.journal_ids.ids)]</field>
    </record>

    <record model="ir.rule" id="journal_security_rule_account_payment">
      <field name="name">Account Payments of Jornals restricted to user</field>
      <field name="model_id" ref="account.model_account_payment"/>
      <field name="groups" eval="[(4, ref('dvit_account_journal_restrict.journal_restrict_group'))]"/>
      <field name="domain_force">[('journal_id.id', 'in', user.journal_ids.ids)]</field>
    </record>

  </data>
</odoo>
