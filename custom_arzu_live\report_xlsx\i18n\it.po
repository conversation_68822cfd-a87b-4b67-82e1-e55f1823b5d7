# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * report_xlsx
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-05-31 03:52+0000\n"
"PO-Revision-Date: 2024-02-12 10:46+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_abstract
msgid "Abstract XLSX Report"
msgstr "Report XLSX astratto"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_report_report_xlsx_partner_xlsx
msgid "Partner XLSX Report"
msgstr "Resoconto XLSX partner"

#. module: report_xlsx
#: model:ir.actions.report,name:report_xlsx.partner_xlsx
msgid "Print to XLSX"
msgstr "Stampa su XLSX"

#. module: report_xlsx
#: model:ir.model,name:report_xlsx.model_ir_actions_report
msgid "Report Action"
msgstr "Azione resoconto"

#. module: report_xlsx
#: model:ir.model.fields,field_description:report_xlsx.field_ir_actions_report__report_type
msgid "Report Type"
msgstr "Tipo resoconto"

#. module: report_xlsx
#: model:ir.model.fields,help:report_xlsx.field_ir_actions_report__report_type
msgid ""
"The type of the report that will be rendered, each one having its own "
"rendering method. HTML means the report will be opened directly in your "
"browser PDF means the report will be rendered using Wkhtmltopdf and "
"downloaded by the user."
msgstr ""
"Il tipo di resoconto che verrà generato, ognuno avente il suo metodo di "
"generazione. HTML vuol dire che il resoconto sarà aperto direttamente nel "
"tuo browser mentre PDF vuol dire che il resoconto sarà generato usando "
"Wkhtmltopdf e scaricato dall'utente."

#. module: report_xlsx
#: model:ir.model.fields.selection,name:report_xlsx.selection__ir_actions_report__report_type__xlsx
msgid "XLSX"
msgstr "XLSX"

#, fuzzy
#~ msgid "ir.actions.report"
#~ msgstr "ir.actions.report.xml"
