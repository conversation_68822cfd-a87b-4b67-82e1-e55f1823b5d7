<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="internal_transfer_group" model="res.groups">
        <field name="name">Internal Transfer Request</field>
    </record>

    <record id="internal_transfer_group_stock_picking_type_rule" model="ir.rule">
        <field name="name">Operation Types Rule For Internal Transfer Module</field>
        <field name="model_id" search="[('model','=','internal.transfer.request')]" model="ir.model"/>
        <field name="groups" eval="[(4, ref('internal_transfer_group'))]"/>
        <field name="domain_force">[('source_picking_type_id','in', user.default_picking_type_ids.ids)]</field>
        <field name="perm_read" eval="True"/>
        <field name="perm_write" eval="True"/>
        <field name="perm_create" eval="True"/>
        <field name="perm_unlink" eval="False"/>
    </record>

</odoo>