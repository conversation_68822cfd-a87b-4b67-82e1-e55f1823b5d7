# Saudi Point of Sale Receipt

This module provides a customized receipt format for Point of Sale (POS) in Saudi Arabia, including Arabic text and Saudi-specific formatting requirements.

## Features

### 1. Saudi-Style Receipt Format
- Arabic text and right-to-left (RTL) layout
- Saudi-specific tax number display (الرقم الضريبي)
- Custom header and footer sections
- Product code display
- Discount and total calculations

### 2. Partner Information Display
- Option to print customer information on receipts
- Customer name, VAT number, phone, and email display
- Configurable through POS settings

### 3. **Double Printing Feature** (New)
- Enable automatic double printing of receipts
- Configurable setting in POS configuration
- Visual indicator on receipt when double printing is enabled
- 1-second delay between prints to ensure proper printing

## Installation

1. Install the module in your Odoo instance
2. Go to Point of Sale > Configuration > Point of Sale
3. Select or create a POS configuration
4. Configure the receipt settings as needed

## Configuration

### POS Configuration Settings

1. **Print Partner in Receipt**: Enable to display customer information on receipts
2. **Enable Double Printing**: Enable to automatically print receipts twice

### How to Enable Double Printing

1. Navigate to **Point of Sale > Configuration > Point of Sale**
2. Select your POS configuration
3. In the **Receipt** section, check the **"Enable Double Printing"** option
4. Save the configuration

When double printing is enabled:
- Receipts will be printed twice automatically
- A visual indicator "نسختان مطبوعتان - Double Print" will appear on the receipt
- There will be a 1-second delay between the two prints

## Technical Details

### Models Modified
- `pos.config`: Added `enable_double_printing` field
- `pos.session`: Extended to load double printing configuration

### JavaScript Components
- `OrderReceipt.js`: Handles double printing logic
- Patches the receipt printing function to print twice when enabled

### Templates
- `OrderReceipt.xml`: Saudi-style receipt template with double printing indicator

## Dependencies

- `point_of_sale`
- `l10n_gcc_pos`
- `point_of_sale_logo`

## Version

- Version: ********.0
- Compatible with Odoo 17.0

## Author

Mohamed Saber

## License

This module is licensed under the same license as Odoo.
