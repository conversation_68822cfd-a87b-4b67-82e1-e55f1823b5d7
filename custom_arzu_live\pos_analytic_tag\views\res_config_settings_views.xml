<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--Inherited form view of res config settings to add field in
    configuration settings-->
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.pos.analytic.tag</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="point_of_sale.res_config_settings_view_form"/>
        <field name="arch" type="xml">
             <xpath expr="//app[@name='point_of_sale']" position="inside">
                <h2>POS Analytic Tags</h2>
                <div class="row mt16 o_settings_container">
                    <div class="col-12 col-lg-6 o_setting_box">
                        <div class="o_setting_right_pane">
                            <span class="o_form_label">Analytic Account</span>
                            <div class="text-muted">
                                Track costs and revenues in each POS sessions
                            </div>
                            <div class="content-group mt16">
                                <field name="pos_analytic_account_id"
                                       domain="['|', ('company_id', '=', company_id), ('company_id', '=', False)]"/>
                            </div>
                        </div>
                    </div>
                </div>
             </xpath>
        </field>
    </record>
</odoo>
