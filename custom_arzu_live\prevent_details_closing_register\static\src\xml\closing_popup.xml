<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="prevent_details_closing_register.ClosePosPopup" t-inherit="point_of_sale.ClosePosPopup" t-inherit-mode="extension">
        <xpath expr="//div/div[@class='modal-header']/div" position="attributes">


            <attribute name="t-if">
                pos.get_cashier().access_pos_closing_details  === true 
            </attribute>

        </xpath>
        <xpath expr="//div/footer/div[2]" position="attributes">
            <attribute name="t-if">
                pos.get_cashier().access_pos_closing_details  === true 
            </attribute>
        </xpath>

        <xpath expr="//div/main/div/table" position="attributes">
            <attribute name="t-if">
                pos.get_cashier().access_pos_closing_details  === true 
            </attribute>
        </xpath>
        <xpath expr="//div/main/div/table" position="after">
            <table class="text-start" t-if="pos.get_cashier().access_pos_closing_details  !== true">
                <thead>
                    <tr class="text-dark">
                        <th style="width:20%">Counted</th>
                    </tr>
                </thead>

                <tbody >
                    <td class="d-flex">
                        <Input tModel="[state.payments[props.default_cash_details.id], 'counted']" callback.bind="(value) =>  this.setManualCashInput(value)" isValid.bind="env.utils.isValidFloat"/>
                        <div class="button icon ClosePosPopup btn btn-secondary" t-on-click="openDetailsPopup">
                            <i class="fa fa-money fa-2x" role="img" title="Open the money details popup"/>
                        </div>
                    </td>
                </tbody>
            </table>
        </xpath>

    </t>
</templates>