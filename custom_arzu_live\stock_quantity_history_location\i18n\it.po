# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_quantity_history_location
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-10-24 10:06+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: none\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__include_child_locations
msgid "Include Child Locations"
msgstr "Includere ubicazioni figlie"

#. module: stock_quantity_history_location
#: model:ir.model.fields,field_description:stock_quantity_history_location.field_stock_quantity_history__location_id
msgid "Location"
msgstr "Ubicazione"

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quant
msgid "Quants"
msgstr "Quanti"

#. module: stock_quantity_history_location
#: model:ir.model,name:stock_quantity_history_location.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Storico quantità giacenza"

#, python-format
#~ msgid "Inventory at Date"
#~ msgstr "Inventario alla data"

#, python-format
#~ msgid "Inventory at Date & Location"
#~ msgstr "Inventario alla Data e Locazione"

#~ msgid "Include child locations"
#~ msgstr "Includi locazioni figlie"
