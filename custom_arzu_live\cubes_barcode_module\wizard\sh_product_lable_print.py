# -*- coding: utf-8 -*-
# Part of Softhealer Technologies.

from odoo import models, fields, api


class LablePrint(models.TransientModel):
    _name = 'sh.dynamic.lable.print'
    _description = 'Dynamic Lable Print'

    sh_lable_print_line_ids = fields.One2many(
        'sh.dynamic.lable.print.line', 'lable_id', string='Dynamic Lable Line')
    @api.model
    def default_get(self, fields_list):
        """Set Default Product and it's quantity to label template popup while click on dynamic label action"""
        res = super(LablePrint, self).default_get(fields_list)
        context = self.env.context
        line_ids = []

        if context.get('active_model') == 'product.template' and context.get('active_ids'):
            for template in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                line_vals = {
                    'product_id': template.product_variant_id.id,
                    'quantity': 1,
                }
                line_ids.append((0, 0, line_vals))
        elif context.get('active_model') == 'product.product' and context.get('active_ids'):
            for product in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                line_vals = {
                    'product_id': product.id,
                    'quantity': 1,
                }
                line_ids.append((0, 0, line_vals))
        elif context.get('active_model') == 'sale.order' and context.get('active_ids'):
            for order in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                for line in order.order_line:
                    line_vals = {
                        'partner_id':order.partner_id.id,
                        'product_id': line.product_id.id,
                        'quantity': int(line.product_uom_qty),
                    }
                    line_ids.append((0, 0, line_vals))
        elif context.get('active_model') == 'account.move' and context.get('active_ids'):
            for move in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                for line in move.invoice_line_ids:
                    line_vals = {
                        'partner_id':move.partner_id.id,
                        'product_id': line.product_id.id,
                        'quantity': int(line.quantity),
                    }
                    line_ids.append((0, 0, line_vals))
        elif context.get('active_model') == 'purchase.order' and context.get('active_ids'):
            for order in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                for line in order.order_line:
                    line_vals = {
                        'partner_id':order.partner_id.id,
                        'product_id': line.product_id.id,
                        'quantity': int(line.product_qty),
                    }
                    line_ids.append((0, 0, line_vals))
        elif context.get('active_model') == 'stock.picking' and context.get('active_ids'):
            for order in self.env[context.get('active_model')].sudo().browse(context.get('active_ids')):
                for line in order.move_ids_without_package:
                    line_vals = {
                        'partner_id':order.partner_id.id,
                        'product_id': line.product_id.id,
                        'quantity': int(line.product_uom_qty),
                    }
                    line_ids.append((0, 0, line_vals))
        res.update({
            'sh_lable_print_line_ids': line_ids
        })
        return res

    def print_cubes_barcode(self):
        """Button For print label"""
        datas = self.read()[0]
        products = []
        if self.sh_lable_print_line_ids:
            for line in self.sh_lable_print_line_ids:
                product_vals = {
                    'product_id': line.product_id.id,
                    'qty': line.quantity,
                }
                products.append(product_vals)
        datas.update({
            'product_dic': products
        })
        return self.env.ref('cubes_barcode_module.sh_barcode_cubes_report_action').report_action([], data=datas)


class LablePrintLine(models.TransientModel):
    _name = 'sh.dynamic.lable.print.line'
    _description = 'Dynamic Lable Print Line'

    lable_id = fields.Many2one(
        'sh.dynamic.lable.print', string='Dynamic Label')
    product_id = fields.Many2one('product.product', string='Product')
    partner_id = fields.Many2one('res.partner', string='Partner')
    quantity = fields.Integer('Barcode Quantity', default=1)
