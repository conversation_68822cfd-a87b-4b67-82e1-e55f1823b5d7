# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    customer_due_amount = fields.Monetary(related='partner_id.total_due', string='Customer Due')

    payment_difference = fields.Monetary(
        compute='_compute_payment_difference')
    payment_difference_handling = fields.Selection(
        string="Payment Difference Handling",
        selection=[('open', 'Keep open'), ('reconcile', 'Mark as fully paid')],
        default='open',
        readonly=False,
    )
    writeoff_account_id = fields.Many2one(
        comodel_name='account.account',
        string="Difference Account",
        copy=False,
        domain="[('deprecated', '=', False)]",
        check_company=True,
        readonly=False,
    )
    writeoff_label = fields.Char(string='Journal Item Label', default='Write-Off',
                                 help='Change label of the counterpart that will hold the payment difference')

    def action_post(self):
        ''' draft -> posted '''
        # Do not allow to post if the account is required but not trusted
        for payment in self:
            if payment.require_partner_bank_account and not payment.partner_bank_id.allow_out_payment:
                raise UserError(
                    _('To record payments with %s, the recipient bank account must be manually validated. You should go on the partner bank account in order to validate it.',
                      self.payment_method_line_id.name))

        # self.move_id._post(soft=False)

        self.filtered(
            lambda pay: pay.is_internal_transfer and not pay.paired_internal_transfer_payment_id
        )._create_paired_internal_transfer_payment()

    @api.depends('amount', 'customer_due_amount')
    def _compute_payment_difference(self):
        for payment in self:
            if payment.partner_type == 'customer' and payment.partner_id and payment.customer_due_amount:
                payment.payment_difference = payment.customer_due_amount - payment.amount
            else:
                payment.payment_difference = 0.0

    def action_view_customer_due(self):
        self.ensure_one()
        if not self.partner_id:
            raise ValidationError("Select customer !")
        return {
            'name': _("Overdue Payments for %s", self.partner_id.display_name),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'views': [[self.env.ref('account_followup.customer_statements_form_view').id, 'form']],
            'res_model': 'res.partner',
            'res_id': self.partner_id.id,
        }
