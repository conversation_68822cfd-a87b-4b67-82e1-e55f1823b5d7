from odoo import models, fields

class SkyexCountry(models.Model):
    _name = 'skyex.country'
    _description = 'SkyEx Country'
    _rec_name = 'code'

    api_id = fields.Char("API ID")
    name = fields.Char("Name", required=True)
    code = fields.Char("Code")


class SkyexZones(models.Model):
    _name = 'skyex.zones'
    _description = 'SkyEx Zones'

    api_id = fields.Char("API ID")
    name = fields.Char("Name", required=True)
    code = fields.Char("Code")

class SkyexSubzones(models.Model):
    _name = 'skyex.subzones'
    _description = 'SkyEx Subzones'

    api_id = fields.Char("API ID")
    name = fields.Char("Name", required=True)
    code = fields.Char("Code")
    zone_id = fields.Many2one(
        comodel_name='skyex.zones',
        string='Zone'
    )


class SkyexShipmentTypes(models.Model):
    _name = 'skyex.shipment.types'
    _description = 'SkyEx Shipment Types'

    api_id = fields.Char("API ID")
    name = fields.Char("Name", required=True)
    code = fields.Char("Code")
