# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    customer_due_amount = fields.Monetary(related='partner_id.total_due', string='Customer Due')

    def action_view_customer_due(self):
        self.ensure_one()
        if not self.partner_id:
            raise ValidationError("Select customer !")
        return {
            'name': _("Overdue Payments for %s", self.partner_id.display_name),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'views': [[self.env.ref('account_followup.customer_statements_form_view').id, 'form']],
            'res_model': 'res.partner',
            'res_id': self.partner_id.id,
        }
